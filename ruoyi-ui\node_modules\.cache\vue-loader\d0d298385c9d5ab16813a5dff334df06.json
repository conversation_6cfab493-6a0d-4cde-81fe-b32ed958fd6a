{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\eventList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\eventList.vue", "mtime": 1756451963650}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IHBhcnNlVGltZSB9IGZyb20gJ0AvdXRpbHMvcnVveWknCmltcG9ydCB7IGdldE11bFR5cGVEaWN0IH0gZnJvbSAnLi4vLi4vLi4vLi4vYXBpL3N5c3RlbS9kaWN0L2RhdGEnCmltcG9ydCB7IGdldERlcHRTeXN0ZW0gfSBmcm9tICcuLi8uLi8uLi8uLi9hcGkvbW9uaXRvcjIvYXBwbGljYXRpb25Bc3NldHMnCmltcG9ydCB7IGdldEFsYXJtLCBkZWxBbGFybSwgbGlzdEFsYXJtLCBhZGRBbGFybSwgdXBkYXRlQWxhcm0sIGFkZEJsb2NrSXAsIHJlZnJlc2hBdHRhY2tEaXJlY3Rpb24gfSBmcm9tICcuLi8uLi8uLi8uLi9hcGkvdGhyZWF0ZW4vdGhyZWF0ZW5XYXJuJwppbXBvcnQgeyBnZXRBc3NldEluZm9CeUlwIH0gZnJvbSAnLi4vLi4vLi4vLi4vYXBpL3NhZmUvb3ZlcnZpZXcnCmltcG9ydCBEeW5hbWljVGFnIGZyb20gJy4uLy4uLy4uLy4uL2NvbXBvbmVudHMvRHluYW1pY1RhZycKaW1wb3J0IEFsYXJtRGV0YWlsIGZyb20gJy4uLy4uLy4uL2Jhc2lzL3NlY3VyaXR5V2Fybi9hbGFybURldGFpbCcKaW1wb3J0IGltcG9ydFRocmVhdGVuIGZyb20gJ0Avdmlld3MvYmFzaXMvc2VjdXJpdHlXYXJuL2ltcG9ydFRocmVhdGVuLnZ1ZScKaW1wb3J0IFRocmVhdGVuQ29uZmlnTGlzdCBmcm9tICdAL3ZpZXdzL2Jhc2lzL3NlY3VyaXR5V2Fybi90aHJlYXRlbkNvbmZpZ0xpc3QudnVlJwppbXBvcnQgU2VydmVyQWRkIGZyb20gJy4uLy4uLy4uL2hobENvZGUvY29tcG9uZW50L2FwcGxpY2F0aW9uL2FkZHMvc2VydmVyQWRkJwppbXBvcnQgU2FmZUFkZCBmcm9tICcuLi8uLi8uLi9oaGxDb2RlL2NvbXBvbmVudC9hcHBsaWNhdGlvbi9hZGRzL3NhZmVBZGQnCmltcG9ydCBWaWV3U3RyYXRlZ3kgZnJvbSAnLi4vLi4vLi4vYmFzaXMvc2VjdXJpdHlXYXJuL3ZpZXdTdHJhdGVneScKaW1wb3J0IFB1Ymxpc2hDbGlja0RpYWxvZyBmcm9tICcuLi8uLi8uLi9iYXNpcy9zZWN1cml0eVdhcm4vcHVibGlzaENsaWNrRGlhbG9nJwppbXBvcnQgRmxvd0JveCBmcm9tICcuLi8uLi8uLi96ZXJvQ29kZS93b3JrRmxvdy9jb21wb25lbnRzL0Zsb3dCb3gnCmltcG9ydCBGbG93VGVtcGxhdGVTZWxlY3QgZnJvbSAnLi4vLi4vLi4vLi4vY29tcG9uZW50cy9GbG93VGVtcGxhdGVTZWxlY3QnCmltcG9ydCBBdHRhY2tTdGFnZSBmcm9tICcuLi8uLi8uLi90aHJlYXQvb3ZlcnZpZXcvYXR0YWNrU3RhZ2UnCmltcG9ydCBBdHRhY2tWaWV3TGlzdCBmcm9tICcuL2F0dGFja1ZpZXdMaXN0JwppbXBvcnQgU3VmZmVyVmlld0xpc3QgZnJvbSAnLi9zdWZmZXJWaWV3TGlzdCcKaW1wb3J0IGF0dGFja0RldGFpbCBmcm9tICcuL2RldGFpbC9pbmRleC52dWUnCmltcG9ydCBzdWZmZXJEZXRhaWwgZnJvbSAnLi9kZXRhaWwvaW5kZXgudnVlJwppbXBvcnQgRGVwdFNlbGVjdCBmcm9tICdAL3ZpZXdzL2NvbXBvbmVudHMvc2VsZWN0L2RlcHRTZWxlY3QudnVlJwppbXBvcnQgeyB1bmlxdWVBcnIgfSBmcm9tICdAL3V0aWxzJwppbXBvcnQgeyBGbG93RW5naW5lSW5mbyB9IGZyb20gJ0AvYXBpL2xvd0NvZGUvRmxvd0VuZ2luZScKaW1wb3J0IHsgbGlzdFVzZXIgfSBmcm9tICdAL2FwaS9zeXN0ZW0vdXNlcicKaW1wb3J0IEF0dGFja1N0YWdlVGV4dCBmcm9tICdAL3ZpZXdzL3RocmVhdC9vdmVydmlldy9hdHRhY2tTdGFnZVRleHQudnVlJwppbXBvcnQgewogIGxpc3REZXZpY2VDb25maWcKfSBmcm9tICdAL2FwaS9mZnNhZmUvZGV2aWNlQ29uZmlnJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdFdmVudExpc3QnLAogIGNvbXBvbmVudHM6IHsKICAgIEF0dGFja1N0YWdlVGV4dCwKICAgIERlcHRTZWxlY3QsCiAgICBTdWZmZXJWaWV3TGlzdCwKICAgIEF0dGFja1ZpZXdMaXN0LAogICAgQXR0YWNrU3RhZ2UsCiAgICBGbG93VGVtcGxhdGVTZWxlY3QsCiAgICBGbG93Qm94LAogICAgUHVibGlzaENsaWNrRGlhbG9nLAogICAgYXR0YWNrRGV0YWlsLAogICAgc3VmZmVyRGV0YWlsLAogICAgVGhyZWF0ZW5Db25maWdMaXN0LCBWaWV3U3RyYXRlZ3ksIFNhZmVBZGQsIFNlcnZlckFkZCwgaW1wb3J0VGhyZWF0ZW4sIEFsYXJtRGV0YWlsLCBEeW5hbWljVGFnCiAgfSwKICBkaWN0czogWyd0aHJlYXRlbl90eXBlJywgJ2F0dGFja19zdGFnZScsICdhdHRhY2tfcmVzdWx0JywgJ2hhbmRsZV9zdGF0ZScsICdzeW5jaHJvbml6YXRpb25fc3RhdHVzJywgJ2F0dGFja19kaXJlY3Rpb24nXSwKICBwcm9wczogewogICAgcHJvcHNBY3RpdmVOYW1lOiB7CiAgICAgIHR5cGU6IFN0cmluZwogICAgfSwKICAgIHByb3BzUXVlcnlQYXJhbXM6IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiBmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gbnVsbAogICAgICB9CiAgICB9LAogICAgY3VycmVudEJ0bjogewogICAgICB0eXBlOiBOdW1iZXIsCiAgICAgIGRlZmF1bHQ6IG51bGwKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICBjb25zdCB2YWxpZGF0ZUJsb2NrSXAgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CiAgICAgIGlmICghdmFsdWUpIHsKICAgICAgICByZXR1cm4gY2FsbGJhY2sobmV3IEVycm9yKCdJUOS4jeiDveS4uuepuicpKQogICAgICB9CiAgICAgIC8vIGxldCBwYXR0ZXJuID0gL14oKDFbMC05XXsyfXwyWzAtNF1bMC05XXwyNVswLTVdfChcZCl7MSwyfSlcLigxWzAtOV17Mn18MlswLTRdWzAtOV18MjVbMC01XXwoXGQpezEsMn18MClcLigxWzAtOV17Mn18MlswLTRdWzAtOV18MjVbMC01XXwoXGQpezEsMn18MClcLigxWzAtOV17Mn18MlswLTRdWzAtOV18MjVbMC01XXwoXGQpezEsMn18MCkpJC87CiAgICAgIGNvbnN0IHBhdHRlcm4gPSAvXlxzKigoMjVbMC01XXwyWzAtNF1bMC05XXxbMDFdP1swLTldWzAtOV0/KVwuKXszfSgyNVswLTVdfDJbMC00XVswLTldfFswMV0/WzAtOV1bMC05XT8pKFxzKjtccyooKDI1WzAtNV18MlswLTRdWzAtOV18WzAxXT9bMC05XVswLTldPylcLil7M30oMjVbMC01XXwyWzAtNF1bMC05XXxbMDFdP1swLTldWzAtOV0/KSkqXHMqJC8KICAgICAgaWYgKCFwYXR0ZXJuLnRlc3QodmFsdWUpKSB7CiAgICAgICAgcmV0dXJuIGNhbGxiYWNrKG5ldyBFcnJvcign6K+36L6T5YWl5q2j56Gu55qESVAnKSkKICAgICAgfQogICAgICByZXR1cm4gY2FsbGJhY2soKQogICAgfQogICAgcmV0dXJuIHsKICAgICAgdXNlckxpc3Q6IFtdLAogICAgICBzaG93SGFuZGxlRGlhbG9nOiBmYWxzZSwKICAgICAgaGFuZGxlRm9ybTogewogICAgICAgIGlkOiAnJywKICAgICAgICBoYW5kbGVEZXNjOiAnJywKICAgICAgICBoYW5kbGVTdGF0ZTogJycKICAgICAgfSwKICAgICAgaGFuZGxlUnVsZXM6IHsKICAgICAgICBoYW5kbGVTdGF0ZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeWkhOeQhueKtuaAgScsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXQogICAgICB9LAogICAgICBzaG93QWxsOiBmYWxzZSwKICAgICAgdGhyZWF0ZW5EaWN0OiBbXSwKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBoYW5kbGVTdGF0ZTogJzAnCiAgICAgIH0sCiAgICAgIGRlcHRPcHRpb25zOiBbXSwKICAgICAgcmFuZ2VUaW1lOiBbXSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHRocmVhdGVuV2Fybkxpc3Q6IFtdLAogICAgICB0b3RhbDogMCwKICAgICAgdGl0bGU6ICcnLAogICAgICBvcGVuVGhyZW50ZW46IGZhbHNlLAogICAgICBmb3JtOiB7fSwKICAgICAgcnVsZXM6IHsKICAgICAgICB0aHJlYXRlbk5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IGZhbHNlLCBtaW46IDAsIG1heDogNTAwLCBtZXNzYWdlOiAn5ZGK6K2m5ZCN56ew5LiN6IO96LaF6L+HNTAw5a2X56ymJywgdHJpZ2dlcjogJ2JsdXInIH0sCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5ZGK6K2m5ZCN56ewJywgdHJpZ2dlcjogJ2JsdXInIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICBwYXR0ZXJuOiAvXlteXHNdKy8sCiAgICAgICAgICAgIG1lc3NhZ2U6ICfkuI3og73ku6XnqbrmoLzlvIDlpLTvvIEnLAogICAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICAgIH0KICAgICAgICBdLAogICAgICAgIGFsYXJtTGV2ZWw6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlkYrorabnrYnnuqcnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgICAgdGhyZWF0ZW5UeXBlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5ZGK6K2m57G75Z6LJywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIHJlYXNvbjogWwogICAgICAgICAgeyByZXF1aXJlZDogZmFsc2UsIG1pbjogMCwgbWF4OiAyMDAwLCBtZXNzYWdlOiAn5ZGK6K2m5Y6f5Zug5LiN6IO96LaF6L+HMjAwMOWtl+espicsIHRyaWdnZXI6ICdibHVyJyB9LAogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWRiuitpuWOn+WboCcsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBoYW5kU3VnZ2VzdDogWwogICAgICAgICAgeyByZXF1aXJlZDogZmFsc2UsIG1pbjogMCwgbWF4OiAyMDAwLCBtZXNzYWdlOiAn5ZGK6K2m5bu66K6u5LiN6IO96LaFMjAwMOWtl+espicsIHRyaWdnZXI6ICdibHVyJyB9LAogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWRiuitpuW7uuiuricsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBsb2dUaW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5pel5b+X5pe26Ze0JywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIGNyZWF0ZVRpbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlkYrorabml7bpl7QnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgICAgc3JjSXA6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IGZhbHNlLCBtaW46IDAsIG1heDogMzAsIG1lc3NhZ2U6ICfmupBJUOS4jeiDvei2hei/hzMw5a2X56ymJywgdHJpZ2dlcjogJ2JsdXInIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICBwYXR0ZXJuOiAnXigyNVswLTVdfDJbMC00XVxcZHxbMC0xXT9cXGQ/XFxkKShcXC4oMjVbMC01XXwyWzAtNF1cXGR8WzAtMV0/XFxkP1xcZCkpezN9JCcsCiAgICAgICAgICAgIG1lc3NhZ2U6ICdJUOWcsOWdgOS4jeiDveS4uuepuuaIluagvOW8j+S4jeato+ehricsCiAgICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgICAgfQogICAgICAgIF0sCiAgICAgICAgc3JjUG9ydDogWwogICAgICAgICAgeyByZXF1aXJlZDogZmFsc2UsIG1pbjogMCwgbWF4OiAxMSwgbWVzc2FnZTogJ+a6kElQ56uv5Y+j5LiN6IO96LaF6L+HMTHlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cicgfSwKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHBhdHRlcm46ICdeWzAtOV0qWzEtOV1bMC05XSokJywgbWVzc2FnZTogJ+a6kElQ56uv5Y+j5LiN6IO95Li656m65oiW5qC85byP5LiN5q2j56GuJywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIGRlc3RJcDogWwogICAgICAgICAgeyByZXF1aXJlZDogZmFsc2UsIG1pbjogMCwgbWF4OiAzMCwgbWVzc2FnZTogJ+ebruagh0lQ5LiN6IO96LaF6L+HMzDlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cicgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIHBhdHRlcm46ICdeKDI1WzAtNV18MlswLTRdXFxkfFswLTFdP1xcZD9cXGQpKFxcLigyNVswLTVdfDJbMC00XVxcZHxbMC0xXT9cXGQ/XFxkKSl7M30kJywKICAgICAgICAgICAgbWVzc2FnZTogJ0lQ5Zyw5Z2A5LiN6IO95Li656m65oiW5qC85byP5LiN5q2j56GuJywKICAgICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgICB9CiAgICAgICAgXSwKICAgICAgICBkZXN0UG9ydDogWwogICAgICAgICAgeyByZXF1aXJlZDogZmFsc2UsIG1pbjogMCwgbWF4OiAxMSwgbWVzc2FnZTogJ+ebruagh0lQ56uv5Y+j5LiN6IO96LaF6L+HMTHlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cicgfSwKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHBhdHRlcm46ICdeWzAtOV0qWzEtOV1bMC05XSokJywgbWVzc2FnZTogJ+ebruagh0lQ56uv5Y+j5LiN6IO95Li656m65oiW5qC85byP5LiN5q2j56GuJywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIG1hdGVSdWxlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiBmYWxzZSwgbWluOiAwLCBtYXg6IDIwMCwgbWVzc2FnZTogJ+WIhuaekOinhOWImeS4jeiDvei2hei/hzIwMOWtl+espicsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBhc3NvY2lhRGV2aWNlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiBmYWxzZSwgbWluOiAwLCBtYXg6IDIwMCwgbWVzc2FnZTogJ+WFs+iBlOiuvuWkh+S4jeiDvei2hei/hzIwMOWtl+espicsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBhdHRhY2tUeXBlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiBmYWxzZSwgbWluOiAwLCBtYXg6IDEwMCwgbWVzc2FnZTogJ+aUu+WHu+aWueW8j+S4jeiDvei2hei/hzEwMOWtl+espicsIHRyaWdnZXI6ICdibHVyJyB9LAogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeaUu+WHu+aWueW8jycsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBhdHRhY2tTdGFnZTogWwogICAgICAgICAgeyByZXF1aXJlZDogZmFsc2UsIG1pbjogMCwgbWF4OiAxMDAsIG1lc3NhZ2U6ICfmlLvlh7vpk77pmLbmrrXkuI3og73otoXov4cxMDDlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cicgfSwKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXmlLvlh7vpk77pmLbmrrUnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgICAgYXR0YWNrUmVzdWx0OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiBmYWxzZSwgbWluOiAwLCBtYXg6IDEwMCwgbWVzc2FnZTogJ+aUu+WHu+e7k+aenOS4jeiDvei2hei/hzEwMOWtl+espicsIHRyaWdnZXI6ICdibHVyJyB9LAogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeaUu+WHu+e7k+aenCcsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXQogICAgICB9LAogICAgICBibG9ja2luZ0Zvcm06IHt9LAogICAgICBibG9ja2luZ1J1bGVzOiB7CiAgICAgICAgYmxvY2tfaXA6IFsKICAgICAgICAgIC8vIOWPr+WQjOaXtuS8oOWkmuS4qu+8jOeUqCI7IumalOW8gAogICAgICAgICAgeyB2YWxpZGF0b3I6IHZhbGlkYXRlQmxvY2tJcCwgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIGR1cmF0aW9uX3RpbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6npmLvmlq3ml7bplb8nLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgICAgcmVtYXJrczogWwogICAgICAgICAgeyByZXF1aXJlZDogZmFsc2UsIG1pbjogMCwgbWF4OiA1MDAsIG1lc3NhZ2U6ICflpIfms6jkuI3og73otoXov4c1MDDlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgYmxvY2tpbmdJcExpc3Q6IFtdLAogICAgICBibG9ja2luZ0RpYWxvZ1Zpc2libGU6IGZhbHNlLCAvLyDmibnph4/pmLvmlq3lvLnnqpcKICAgICAgZWRpdGFibGU6IHRydWUsCiAgICAgIGFzc2V0SW5mb0xpc3Q6IFtdLAogICAgICBvcGVuRGlhbG9nOiBmYWxzZSwKICAgICAgYXNzZXREYXRhOiB7fSwKICAgICAgaW1wb3J0RGlhbG9nOiBmYWxzZSwKICAgICAgc2VydmVyT3BlbjogZmFsc2UsCiAgICAgIGFzc2V0SWQ6IG51bGwsCiAgICAgIHNhZmVPcGVuOiBmYWxzZSwKICAgICAgdGhyZWF0ZW5Db25maWdGbGFnOiBmYWxzZSwKICAgICAgdmlld1N0cmF0ZWd5OiBmYWxzZSwKICAgICAgcHVibGlzaERpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBmbG93VmlzaWJsZTogZmFsc2UsCiAgICAgIGZsb3dUZW1wbGF0ZVNlbGVjdFZpc2libGU6IGZhbHNlLAogICAgICBmbG93U3RhdGVPcHRpb25zOiBbCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflvoXlrqHmoLgnLAogICAgICAgICAgdmFsdWU6IDAKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5b6F5aSE572uJywKICAgICAgICAgIHZhbHVlOiAxCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+W+heWPjemmiOWuoeaguCcsCiAgICAgICAgICB2YWx1ZTogMgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflvoXpqozor4EnLAogICAgICAgICAgdmFsdWU6IDMKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5bey5a6M5oiQJywKICAgICAgICAgIHZhbHVlOiA0CiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+W+heaPkOS6pCcsCiAgICAgICAgICB2YWx1ZTogLTEKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5pyq5YiG6YWNJywKICAgICAgICAgIHZhbHVlOiA5OQogICAgICAgIH0KICAgICAgXSwKICAgICAgaGFuZGxlU3RhdGVPcHRpb25zOiBbCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICfmnKrlpITnva4nLAogICAgICAgICAgdmFsdWU6ICcwJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflt7LlpITnva4nLAogICAgICAgICAgdmFsdWU6ICcxJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflv73nlaUnLAogICAgICAgICAgdmFsdWU6ICcyJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflpITnva7kuK0nLAogICAgICAgICAgdmFsdWU6ICczJwogICAgICAgIH0KICAgICAgXSwKICAgICAgYWN0aXZlTmFtZTogJ2RldGFpbCcsCiAgICAgIHN5bmNTdGF0ZU9wdGlvbnM6IFsKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+acquWQjOatpScsCiAgICAgICAgICB2YWx1ZTogMAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflt7LlkIzmraUnLAogICAgICAgICAgdmFsdWU6IDEKICAgICAgICB9CiAgICAgIF0sCiAgICAgIGJsb2NraW5nRHVyYXRpb246IFsKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJzMw5YiG6ZKfJywKICAgICAgICAgIHZhbHVlOiAnMzBtJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICcyNOWwj+aXticsCiAgICAgICAgICB2YWx1ZTogJzI0aCcKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAnNDjlsI/ml7YnLAogICAgICAgICAgdmFsdWU6ICc0OGgnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJzflpKknLAogICAgICAgICAgdmFsdWU6ICcxNjhoJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICfmsLjkuYUnLAogICAgICAgICAgdmFsdWU6ICfmsLjkuYUnCiAgICAgICAgfQogICAgICBdLAogICAgICBtdWx0aXBsZVNlbGVjdGlvbjogW10sCiAgICAgIGRldmljZUNvbmZpZ0xpc3Q6IFtdCiAgICB9CiAgfSwKICB3YXRjaDogewogICAgLy8g55uR5ZCs55uu5qCHaXAKICAgICdmb3JtLmRlc3RJcCcodmFsdWUsIG9sZFZhbHVlKSB7CiAgICAgIHZhciByZyA9IC9eKDI1WzAtNV18MlswLTRdXGR8WzAtMV0/XGQ/XGQpKFwuKDI1WzAtNV18MlswLTRdXGR8WzAtMV0/XGQ/XGQpKXszfSQvCiAgICAgIHZhciByZWcgPSByZy50ZXN0KHZhbHVlKQogICAgICBpZiAocmVnKSB7CiAgICAgICAgLy8g5qC55o2uaXDojrflj5botYTkuqfmlbDmja4KICAgICAgICBnZXRBc3NldEluZm9CeUlwKHZhbHVlKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmxlbmd0aCkgewogICAgICAgICAgICBjb25zdCBhc3NldERhdGEgPSByZXNwb25zZS5kYXRhCiAgICAgICAgICAgIGFzc2V0RGF0YS5mb3JFYWNoKGl0ZW0gPT4gaXRlbS52YWx1ZSA9IGl0ZW0uYXNzZXROYW1lICsgJy0nICsgaXRlbS5hc3NldFR5cGVEZXNjKQogICAgICAgICAgICBpZiAodmFsdWUgIT09IG9sZFZhbHVlICYmIG9sZFZhbHVlKSB7CiAgICAgICAgICAgICAgdGhpcy5mb3JtLmFzc2V0SWQgPSAnJwogICAgICAgICAgICAgIHRoaXMuZm9ybS5kZXB0SWQgPSAnJwogICAgICAgICAgICB9CiAgICAgICAgICAgIC8vIOi1hOS6p+aVsOaNruacieWkmuadoeaYvuekuuS4i+aLieahhu+8jOWPquacieS4gOadoeS4jeaYvuekugogICAgICAgICAgICBpZiAoYXNzZXREYXRhLmxlbmd0aCA9PT0gMSkgewogICAgICAgICAgICAgIHRoaXMuZm9ybS5hc3NldElkID0gYXNzZXREYXRhWzBdLmFzc2V0SWQKICAgICAgICAgICAgICB0aGlzLmZvcm0uZGVwdElkID0gYXNzZXREYXRhWzBdLmRlcHRJZAogICAgICAgICAgICB9CiAgICAgICAgICAgIGlmIChhc3NldERhdGEubGVuZ3RoID4gMSAmJiAhdGhpcy5mb3JtLmFzc2V0SWQpIHsKICAgICAgICAgICAgICB0aGlzLmZvcm0uYXNzZXRJZCA9ICcnCiAgICAgICAgICAgICAgdGhpcy5mb3JtLmRlcHRJZCA9ICcnCiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy5hc3NldEluZm9MaXN0ID0gYXNzZXREYXRhCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLmFzc2V0SW5mb0xpc3QgPSBbXQogICAgICAgICAgICByZXR1cm4gdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmnKrmn6Xor6LliLDotYTkuqfmlbDmja4nKQogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5hc3NldEluZm9MaXN0ID0gW10KICAgICAgICB0aGlzLmZvcm0uYXNzZXRJZCA9ICcnCiAgICAgICAgdGhpcy5mb3JtLmRlcHRJZCA9ICcnCiAgICAgIH0KICAgIH0sCiAgICBwcm9wc0FjdGl2ZU5hbWUoKSB7CiAgICAgIHRoaXMuaW5pdCgpCiAgICB9LAogICAgcHJvcHNRdWVyeVBhcmFtczogewogICAgICBoYW5kbGVyKHZhbCkgewogICAgICAgIHRoaXMuaGFuZGxlUHJvcHNRdWVyeSh2YWwpCiAgICAgIH0KICAgIH0sCiAgICAvKiByYW5nZVRpbWUodmFsKSB7CiAgICAgIGNvbnNvbGUubG9nKHZhbCkKICAgIH0sKi8KICAgICdibG9ja2luZ0Zvcm0uYmxvY2tfaXAnOiB7CiAgICAgIGhhbmRsZXIodmFsdWUpIHsKICAgICAgICBpZiAodmFsdWUpIHsKICAgICAgICAgIHRoaXMuYmxvY2tpbmdJcExpc3QgPSB2YWx1ZS5zcGxpdCgnOycpLm1hcChpcCA9PiBpcC50cmltKCkpLmZpbHRlcihpcCA9PiBpcCkKICAgICAgICB9CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0RGV2aWNlQ29uZmlnTGlzdCgpCiAgfSwKICBtb3VudGVkKCkgewogICAgaWYgKCF0aGlzLiRyb3V0ZS5xdWVyeSB8fCBPYmplY3Qua2V5cyh0aGlzLiRyb3V0ZS5xdWVyeSkubGVuZ3RoIDwgMSkgewogICAgICB0aGlzLmluaXQoKQogICAgfSBlbHNlIHsKICAgICAgdGhpcy5oYW5kbGVQcm9wc1F1ZXJ5KHRoaXMuJHJvdXRlLnF1ZXJ5KQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgaW5pdCgpIHsKICAgICAgLy8gdGhpcy5yZXNldFF1ZXJ5KCkKICAgICAgdGhpcy5nZXRUaHJlYXRlbkRpY3QoKQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkKICAgICAgdGhpcy5nZXREZXB0c0RhdGEoKQogICAgICB0aGlzLmdldFVzZXJMaXN0KCkKICAgIH0sCiAgICBnZXRVc2VyTGlzdCgpIHsKICAgICAgbGlzdFVzZXIoeyBwYWdlTnVtOiAxLCBwYWdlU2l6ZTogMTAwMCB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5yb3dzKSB7CiAgICAgICAgICB0aGlzLnVzZXJMaXN0ID0gcmVzLnJvd3MKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucHJvcHNRdWVyeVBhcmFtcy5hbGFybUxldmVsID0gdGhpcy5xdWVyeVBhcmFtcy5hbGFybUxldmVsCiAgICAgIHRoaXMucHJvcHNRdWVyeVBhcmFtcy5yZWZlcmVuY2VJZCA9IHRoaXMucXVlcnlQYXJhbXMucmVmZXJlbmNlSWQKICAgICAgLy8gdGhpcy4kZW1pdCgndXBkYXRlOmN1cnJlbnRCdG4nLHRoaXMucXVlcnlQYXJhbXMuYWxhcm1MZXZlbD9wYXJzZUludCh0aGlzLnF1ZXJ5UGFyYW1zLmFsYXJtTGV2ZWwpIDogbnVsbCkKICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsgLi4udGhpcy5xdWVyeVBhcmFtcywgLi4udGhpcy5wcm9wc1F1ZXJ5UGFyYW1zIH0KICAgICAgaWYgKHRoaXMucmFuZ2VUaW1lICE9IG51bGwpIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN0YXJ0VGltZSA9IHBhcnNlVGltZSh0aGlzLnJhbmdlVGltZVswXSkKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmVuZFRpbWUgPSBwYXJzZVRpbWUodGhpcy5yYW5nZVRpbWVbMV0pCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zdGFydFRpbWUgPSBudWxsCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbmRUaW1lID0gbnVsbAogICAgICB9CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlU2l6ZSA9IDEwCgogICAgICBpZiAoIXRoaXMucXVlcnlQYXJhbXMuc3RhcnRUaW1lKSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zdGFydFRpbWUgPSBwYXJzZVRpbWUobmV3IERhdGUoKS5zZXRIb3VycygtMTY4LCAwLCAwLCAwKSwgJ3t5fS17bX0te2R9IDAwOjAwOjAwJykgLy8g5LiA5ZGo5YmN77yM5pe26Ze06YOo5YiG5Li6IDAwOjAwOjAwCiAgICAgIH0KICAgICAgaWYgKCF0aGlzLnF1ZXJ5UGFyYW1zLmVuZFRpbWUpIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmVuZFRpbWUgPSBwYXJzZVRpbWUobmV3IERhdGUoKS5zZXRIb3VycygyMywgNTksIDU5LCA5OTkpLCAne3l9LXttfS17ZH0gMjM6NTk6NTknKSAvLyDlvZPliY3ml6XmnJ/vvIzml7bpl7Tpg6jliIbkuLogMjM6NTk6NTkKICAgICAgfQogICAgICB0aGlzLnJhbmdlVGltZSA9IFt0aGlzLnF1ZXJ5UGFyYW1zLnN0YXJ0VGltZSwgdGhpcy5xdWVyeVBhcmFtcy5lbmRUaW1lXQogICAgICB0aGlzLnRvdGFsID0gMAogICAgICB0aGlzLmdldExpc3QoKQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgY29uc3QgZGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5xdWVyeVBhcmFtcykpCiAgICAgICAgaWYgKGRhdGEudGhyZWF0ZW5UeXBlICE9IG51bGwpIHsKICAgICAgICAgIGRhdGEudGhyZWF0ZW5UeXBlID0gZGF0YS50aHJlYXRlblR5cGUuam9pbignLycpCiAgICAgICAgfQogICAgICAgIHRoaXMuJHJlZnMuYXRjQWdlLmluaXRBdHRhY2tTdGFnZShkYXRhKQogICAgICB9KQogICAgfSwKICAgIC8vIOiOt+WPluWRiuitpuexu+Wei+Wkmue6p+Wtl+WFuOaVsOaNrgogICAgZ2V0VGhyZWF0ZW5EaWN0KCkgewogICAgICBnZXRNdWxUeXBlRGljdCh7CiAgICAgICAgZGljdFR5cGU6ICd0aHJlYXRlbl9hbGFybV90eXBlJwogICAgICB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy50aHJlYXRlbkRpY3QgPSByZXMuZGF0YQogICAgICB9KQogICAgfSwKICAgIC8vIOiOt+WPlumDqOmXqOaVsOaNrgogICAgZ2V0RGVwdHNEYXRhKCkgewogICAgICBnZXREZXB0U3lzdGVtKCkudGhlbihyZXMgPT4gdGhpcy5kZXB0T3B0aW9ucyA9IHJlcy5kYXRhKQogICAgfSwKICAgIGhhbmRsZUNoYW5nZSh2YWwpIHsKICAgICAgLy8g6I635Y+W5omA5bGe6YOo6Zeo5pyA5ZCOaWQKICAgICAgaWYgKHZhbCkgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGVwdElkID0gdmFsW3ZhbC5sZW5ndGggLSAxXQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGVwdElkID0gJycKICAgICAgfQogICAgfSwKICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSB7CiAgICAgICAgdGhyZWF0ZW5OYW1lOiBudWxsLAogICAgICAgIHRocmVhdGVuVHlwZTogbnVsbCwKICAgICAgICBhbGFybUxldmVsOiBudWxsLAogICAgICAgIHJlZmVyZW5jZUlkOiBudWxsLAogICAgICAgIHNyY0lwOiBudWxsLAogICAgICAgIGRlc3RJcDogbnVsbCwKICAgICAgICBoYW5kbGVTdGF0ZTogJzAnLAogICAgICAgIGZsb3dTdGF0ZTogbnVsbCwKICAgICAgICB1cGRhdGVUaW1lOiBudWxsLAogICAgICAgIGF0dGFja0RpcmVjdGlvbjogbnVsbCwKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMAogICAgICB9CiAgICAgIGNvbnN0IGF0Y0FnZSA9IHRoaXMuJHJlZnMuYXRjQWdlCiAgICAgIGlmIChhdGNBZ2UpIHsKICAgICAgICBhdGNBZ2UuY3VycmVudFNlbGVjdGVkQ2FyZCA9IG51bGwKICAgICAgfQogICAgICB0aGlzLnJhbmdlVGltZSA9IG51bGwKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpCiAgICB9LAogICAgLy8g5paw5aKe5aiB6IOB5oOF5oqlCiAgICBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMub3BlblRocmVudGVuID0gdHJ1ZQogICAgICB0aGlzLmZvcm0gPSB7fQogICAgICB0aGlzLmVkaXRhYmxlID0gdHJ1ZQogICAgICB0aGlzLnRpdGxlID0gJ+aWsOWinuWogeiDgeaDheaKpScKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgJ2Fzc2V0SWQnLCAnJykgLy8g6Kej5YazZWwtc2VsZWN05peg5rOV6KeG5Zu+5LiO5pWw5o2u55qE5pu05pawCiAgICB9LAogICAgLy8g5a+85YWl5Yqf6IO9CiAgICBoYW5kbGVJbXBvcnQoKSB7CiAgICAgIHRoaXMuaW1wb3J0RGlhbG9nID0gdHJ1ZQogICAgfSwKICAgIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgKICAgICAgICAnL3N5c3RlbS90aHJlYWR0ZW4vZXhwb3J0JywKICAgICAgICB7CiAgICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zCiAgICAgICAgfSwKICAgICAgICBg5aiB6IOB5ZGK6K2mXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgCiAgICAgICkKICAgIH0sCgogICAgLy8g6I635Y+W5YiX6KGo5pWw5o2u5p+l6K+iCiAgICBoYW5kbGVSb3dDbGljayhyb3csIGNvbHVtbiwgZXZlbnQpIHsKICAgICAgLy8g6I635Y+W5ZGK6K2m6K+m5oOF5Y2V5Liq5Y2V5YWD5qC85pWw5o2u6L+b6KGM562b6YCJCiAgICAgIGlmIChyb3cgJiYgcm93LmlkKSB7CiAgICAgICAgaWYgKGNvbHVtbi5wcm9wZXJ0eSkgewogICAgICAgICAgaWYgKGNvbHVtbi5wcm9wZXJ0eSA9PT0gJ2Zsb3dTdGF0ZScpIHsKICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtc1tjb2x1bW4ucHJvcGVydHldID0gIXJvd1tjb2x1bW4ucHJvcGVydHldID8gOTkgOiBOdW1iZXIocm93W2NvbHVtbi5wcm9wZXJ0eV0pCiAgICAgICAgICAgIGxpc3RBbGFybSh7CiAgICAgICAgICAgICAgW2NvbHVtbi5wcm9wZXJ0eV06ICFyb3dbY29sdW1uLnByb3BlcnR5XSA/IDk5IDogTnVtYmVyKHJvd1tjb2x1bW4ucHJvcGVydHldKSwKICAgICAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgICAgICBzdGFydFRpbWU6IHBhcnNlVGltZSh0aGlzLnJhbmdlVGltZVswXSksCiAgICAgICAgICAgICAgZW5kVGltZTogcGFyc2VUaW1lKHRoaXMucmFuZ2VUaW1lWzFdKQogICAgICAgICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLnRocmVhdGVuV2Fybkxpc3QgPSByZXNwb25zZS5yb3dzCiAgICAgICAgICAgICAgdGhpcy50aHJlYXRlbldhcm5MaXN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICAgICAgICBpdGVtLmFzc2V0VHlwZSA9IGl0ZW0uYXNzZXRDbGFzc0Rlc2MgKyAnLScgKyBpdGVtLmFzc2V0VHlwZURlc2MKICAgICAgICAgICAgICAgIGlmIChpdGVtLmFzc2V0VHlwZSA9PSAnbnVsbC1udWxsJykgewogICAgICAgICAgICAgICAgICBpdGVtLmFzc2V0VHlwZSA9IG51bGwKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbAogICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIHJldHVybgogICAgICAgICAgfSBlbHNlIGlmIChjb2x1bW4ucHJvcGVydHkgPT09ICd0aHJlYXRlblR5cGUnKSB7CiAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXNbY29sdW1uLnByb3BlcnR5XSA9IHJvd1tjb2x1bW4ucHJvcGVydHldLnNwbGl0KCcvJykKICAgICAgICAgIH0gZWxzZSBpZiAoY29sdW1uLnByb3BlcnR5ID09PSAnYWxhcm1MZXZlbCcpIHsKICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtc1tjb2x1bW4ucHJvcGVydHldID0gcm93W2NvbHVtbi5wcm9wZXJ0eV0udG9TdHJpbmcoKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtc1tjb2x1bW4ucHJvcGVydHldID0gcm93W2NvbHVtbi5wcm9wZXJ0eV0KICAgICAgICAgIH0KICAgICAgICAgIGxpc3RBbGFybSh7CiAgICAgICAgICAgIFtjb2x1bW4ucHJvcGVydHldOiByb3dbY29sdW1uLnByb3BlcnR5XSwKICAgICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgICBzdGFydFRpbWU6IHBhcnNlVGltZSh0aGlzLnJhbmdlVGltZVswXSksCiAgICAgICAgICAgIGVuZFRpbWU6IHBhcnNlVGltZSh0aGlzLnJhbmdlVGltZVsxXSkKICAgICAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICB0aGlzLnRocmVhdGVuV2Fybkxpc3QgPSByZXNwb25zZS5yb3dzCiAgICAgICAgICAgIHRoaXMudGhyZWF0ZW5XYXJuTGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgICAgIGl0ZW0uYXNzZXRUeXBlID0gaXRlbS5hc3NldENsYXNzRGVzYyArICctJyArIGl0ZW0uYXNzZXRUeXBlRGVzYwogICAgICAgICAgICAgIGlmIChpdGVtLmFzc2V0VHlwZSA9PSAnbnVsbC1udWxsJykgewogICAgICAgICAgICAgICAgaXRlbS5hc3NldFR5cGUgPSBudWxsCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQogICAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwKICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9CiAgICB9LAoKICAgIC8vIOWkmumAiQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHZhbCkgewogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gdmFsCiAgICB9LAoKICAgIGZsb3dTdGF0ZUZvcm1hdHRlcihyb3csIGNvbHVtbiwgY2VsbFZhbHVlLCBpbmRleCkgewogICAgICBsZXQgbmFtZSA9ICfmnKrliIbphY0nCiAgICAgIGNvbnN0IG1hdGNoID0gdGhpcy5mbG93U3RhdGVPcHRpb25zLmZpbmQoaXRlbSA9PiBpdGVtLnZhbHVlID09IGNlbGxWYWx1ZSkKICAgICAgaWYgKG1hdGNoKSB7CiAgICAgICAgbmFtZSA9IG1hdGNoLmxhYmVsCiAgICAgIH0KICAgICAgcmV0dXJuIG5hbWUKICAgIH0sCiAgICBkaXNwb3NlckZvcm1hdHRlcihyb3csIGNvbHVtbiwgY2VsbFZhbHVlLCBpbmRleCkgewogICAgICBsZXQgbmFtZSA9ICcnCiAgICAgIGlmIChjZWxsVmFsdWUpIHsKICAgICAgICB0aGlzLnVzZXJMaXN0LmZvckVhY2goZSA9PiB7CiAgICAgICAgICBpZiAoZS51c2VySWQgPT0gY2VsbFZhbHVlKSB7CiAgICAgICAgICAgIG5hbWUgPSBlLm5pY2tOYW1lCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICByZXR1cm4gbmFtZQogICAgICB9CiAgICAgIHJldHVybiBuYW1lCiAgICB9LAoKICAgIGhhbmRsZVN0YXRlRm9ybWF0dGVyKHJvdywgY29sdW1uLCBjZWxsVmFsdWUsIGluZGV4KSB7CiAgICAgIGxldCBuYW1lID0gJ+acquWkhOe9ricKICAgICAgY29uc3QgbWF0Y2ggPSB0aGlzLmhhbmRsZVN0YXRlT3B0aW9ucy5maW5kKGl0ZW0gPT4gaXRlbS52YWx1ZSA9PSBjZWxsVmFsdWUpCiAgICAgIGlmIChtYXRjaCkgewogICAgICAgIG5hbWUgPSBtYXRjaC5sYWJlbAogICAgICB9CiAgICAgIHJldHVybiBuYW1lCiAgICB9LAogICAgaGFuZGxlRGV0YWlsKHJvdykgewogICAgICB0aGlzLmFzc2V0RGF0YSA9IHsgLi4ucm93IH0KICAgICAgdGhpcy50aXRsZSA9ICfmn6XnnIvlkYrorabor6bmg4UnCiAgICAgIHRoaXMub3BlbkRldGFpbCh0cnVlKQogICAgfSwKICAgIHNob3dIYW5kbGUocm93KSB7CiAgICAgIC8vIOiOt+WPluS6i+S7tuivpuaDheWNleS4quWNleWFg+agvOaVsOaNrui/m+ihjOetm+mAiQogICAgICBpZiAocm93LmhhbmRsZVN0YXRlID09PSAnMScgfHwgcm93LmhhbmRsZVN0YXRlID09PSAnMicpIHsKICAgICAgICB0aGlzLmhhbmRsZUZvcm0uaGFuZGxlU3RhdGUgPSBwYXJzZUludChyb3cuaGFuZGxlU3RhdGUpCiAgICAgICAgdGhpcy5oYW5kbGVGb3JtLmhhbmRsZURlc2MgPSByb3cuaGFuZGxlRGVzYwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuaGFuZGxlRm9ybSA9IHsKICAgICAgICAgIGhhbmRsZURlc2M6ICcnLAogICAgICAgICAgaGFuZGxlU3RhdGU6ICcnCiAgICAgICAgfQogICAgICB9CiAgICAgIHRoaXMuaGFuZGxlRm9ybS5pZCA9IHJvdy5pZAogICAgICB0aGlzLnNob3dIYW5kbGVEaWFsb2cgPSB0cnVlCiAgICB9LAogICAgaGFuZGxlRWRpdChyb3cpIHsKICAgICAgZ2V0QWxhcm0ocm93LmlkKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5mb3JtID0geyAuLi5yZXMuZGF0YSB9CiAgICAgICAgaWYgKHRoaXMuZm9ybS5hbGFybUxldmVsICE9IG51bGwpIHsKICAgICAgICAgIHRoaXMuZm9ybS5hbGFybUxldmVsID0gKHRoaXMuZm9ybS5hbGFybUxldmVsKS50b1N0cmluZygpCiAgICAgICAgfQogICAgICAgIGlmICh0aGlzLmZvcm0udGhyZWF0ZW5UeXBlICE9IG51bGwpIHsKICAgICAgICAgIHRoaXMuZm9ybS50aHJlYXRlblR5cGUgPSB0aGlzLmZvcm0udGhyZWF0ZW5UeXBlLnNwbGl0KCcvJykKICAgICAgICB9CiAgICAgICAgaWYgKHRoaXMuZm9ybS5hdHRhY2tOdW0gIT0gbnVsbCkgewogICAgICAgICAgdGhpcy5mb3JtLmF0dGFja051bSA9ICh0aGlzLmZvcm0uYXR0YWNrTnVtKS50b1N0cmluZygpCiAgICAgICAgfQogICAgICAgIGlmICh0aGlzLmZvcm0uc3JjUG9ydCAhPSBudWxsKSB7CiAgICAgICAgICB0aGlzLmZvcm0uc3JjUG9ydCA9ICh0aGlzLmZvcm0uc3JjUG9ydCkudG9TdHJpbmcoKQogICAgICAgIH0KICAgICAgICBpZiAodGhpcy5mb3JtLmRlc3RQb3J0ICE9IG51bGwpIHsKICAgICAgICAgIHRoaXMuZm9ybS5kZXN0UG9ydCA9ICh0aGlzLmZvcm0uZGVzdFBvcnQpLnRvU3RyaW5nKCkKICAgICAgICB9CiAgICAgICAgdGhpcy50aXRsZSA9ICfkv67mlLnlqIHog4Hmg4XmiqUnCiAgICAgICAgdGhpcy5vcGVuVGhyZW50ZW4gPSB0cnVlCiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICBjb25zdCBpZHMgPSByb3cuaWQKICAgICAgY29uc3QgdGl0bGUgPSByb3cudGhyZWF0ZW5OYW1lCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOWRiuitpuWQjeensOS4uuOAkCcgKyB0aXRsZSArICfjgJHnmoTmlbDmja7pobk/JykudGhlbigoKSA9PiB7CiAgICAgICAgcmV0dXJuIGRlbEFsYXJtKGlkcykKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQogICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKCiAgICAgIH0pCiAgICB9LAogICAgYWRkT3JVcGRhdGVGbG93SGFuZGxlKGlkLCBmbG93U3RhdGUsIHJvdykgewogICAgICBjb25zdCBkYXRhID0gewogICAgICAgIGlkOiBpZCB8fCAnJywKICAgICAgICBmb3JtVHlwZTogMSwKICAgICAgICBvcFR5cGU6IGZsb3dTdGF0ZSA/IDAgOiAnLTEnLAogICAgICAgIHN0YXR1czogZmxvd1N0YXRlLAogICAgICAgIHJvdzogcm93LAogICAgICAgIGlzV29yazogdHJ1ZQogICAgICB9CiAgICAgIGRhdGEucm93LndvcmtUeXBlID0gJzInCiAgICAgIGRhdGEucm93LmV2ZW50VHlwZSA9IDMKICAgICAgZGF0YS5vcmlnaW5UeXBlID0gJ2V2ZW50JwogICAgICB0aGlzLmN1cnJlbnRGbG93RGF0YSA9IGRhdGEKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICB0aGlzLmdldENvbmZpZ0tleSgnZGVmYXVsdC5mbG93VGVtcGxhdGVJZCcpLnRoZW4ocmVzID0+IHsKICAgICAgICBjb25zdCBmbG93SWQgPSByZXMubXNnCiAgICAgICAgaWYgKGZsb3dJZCkgewogICAgICAgICAgdGhpcy5nZXRGbG93RW5naW5lSW5mbyhmbG93SWQpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuZmxvd1RlbXBsYXRlU2VsZWN0VmlzaWJsZSA9IHRydWUKICAgICAgICB9CiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gewogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCiAgICAgIH0pCiAgICB9LAogICAgZ2V0Rmxvd0VuZ2luZUluZm8odmFsKSB7CiAgICAgIEZsb3dFbmdpbmVJbmZvKHZhbCkudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMuZGF0YSAmJiByZXMuZGF0YS5mbG93VGVtcGxhdGVKc29uKSB7CiAgICAgICAgICBjb25zdCBkYXRhID0gSlNPTi5wYXJzZShyZXMuZGF0YS5mbG93VGVtcGxhdGVKc29uKQogICAgICAgICAgaWYgKCFkYXRhWzBdLmZsb3dJZCkgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor6XmtYHnqIvmqKHmnb/lvILluLgs6K+36YeN5paw6YCJ5oupJykKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuY3VycmVudEZsb3dEYXRhLmZsb3dJZCA9IGRhdGFbMF0uZmxvd0lkCiAgICAgICAgICAgIHRoaXMuZmxvd1Zpc2libGUgPSB0cnVlCiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgICB0aGlzLiRyZWZzLkZsb3dCb3guaW5pdCh0aGlzLmN1cnJlbnRGbG93RGF0YSkKICAgICAgICAgICAgfSkKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gewogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCiAgICAgIH0pCiAgICB9LAogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICBjb25zdCBxdWVyeVBhcmFtcyA9IHsKICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zCiAgICAgIH0KICAgICAgaWYgKHF1ZXJ5UGFyYW1zLnRocmVhdGVuVHlwZSAhPSBudWxsKSB7CiAgICAgICAgcXVlcnlQYXJhbXMudGhyZWF0ZW5UeXBlID0gcXVlcnlQYXJhbXMudGhyZWF0ZW5UeXBlLmpvaW4oJy8nKQogICAgICB9CiAgICAgIC8vIOWQjOatpeivt+axguexu+Wei+e7n+iuoeaVsOaNrgogICAgICB0aGlzLiRlbWl0KCdnZXRMaXN0JywgeyAuLi5xdWVyeVBhcmFtcyB9KQogICAgICBsaXN0QWxhcm0ocXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMudGhyZWF0ZW5XYXJuTGlzdCA9IHJlc3BvbnNlLnJvd3MKICAgICAgICB0aGlzLnRocmVhdGVuV2Fybkxpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGl0ZW0uYXNzZXRUeXBlID0gaXRlbS5hc3NldENsYXNzRGVzYyArICctJyArIGl0ZW0uYXNzZXRUeXBlRGVzYwogICAgICAgICAgaWYgKGl0ZW0uYXNzZXRUeXBlID09ICdudWxsLW51bGwnKSB7CiAgICAgICAgICAgIGl0ZW0uYXNzZXRUeXBlID0gbnVsbAogICAgICAgICAgfQogICAgICAgICAgaWYgKGl0ZW0uZGVwdE5hbWUpIHsKICAgICAgICAgICAgY29uc3QgZGVwdE5hbWVBcnIgPSB1bmlxdWVBcnIoaXRlbS5kZXB0TmFtZS5zcGxpdCgnLCcpKQogICAgICAgICAgICBpdGVtLmRlcHROYW1lID0gZGVwdE5hbWVBcnIuam9pbignLCcpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZUNsb3NlKGRvbmUpIHsKICAgICAgZG9uZSgpCiAgICAgIHRoaXMuZm9ybSA9IHt9CiAgICAgIHRoaXMuJHJlZnMuZm9ybS5yZXNldEZpZWxkcygpCiAgICB9LAogICAgc3VibWl0SGFuZGxlRm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1snaGFuZGxlU3RhdGVGb3JtJ10udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgdXBkYXRlQWxhcm0odGhpcy5oYW5kbGVGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5aSE572u5oiQ5YqfJykKICAgICAgICAgICAgdGhpcy5oYW5kbGVGb3JtID0ge30KICAgICAgICAgICAgdGhpcy5zaG93SGFuZGxlRGlhbG9nID0gZmFsc2UKICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgIGlmICh0aGlzLmZvcm0udGhyZWF0ZW5UeXBlICE9IG51bGwpIHsKICAgICAgICB0aGlzLmZvcm0udGhyZWF0ZW5UeXBlID0gdGhpcy5mb3JtLnRocmVhdGVuVHlwZS5qb2luKCcvJykKICAgICAgfQogICAgICB0aGlzLiRyZWZzWydmb3JtJ10udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCA9PSBudWxsKSB7CiAgICAgICAgICAgIGFkZEFsYXJtKHRoaXMuZm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5paw5aKe5oiQ5YqfJykKICAgICAgICAgICAgICB0aGlzLmZvcm0gPSB7fQogICAgICAgICAgICAgIHRoaXMub3BlblRocmVudGVuID0gZmFsc2UKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICAgICAgICB9KQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdXBkYXRlQWxhcm0odGhpcy5mb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkv67mlLnmiJDlip8nKQogICAgICAgICAgICAgIHRoaXMuZm9ybSA9IHt9CiAgICAgICAgICAgICAgdGhpcy5vcGVuVGhyZW50ZW4gPSBmYWxzZQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuVGhyZW50ZW4gPSBmYWxzZQogICAgICB0aGlzLiRyZWZzLmZvcm0ucmVzZXRGaWVsZHMoKQogICAgfSwKICAgIG9wZW5EZXRhaWwodmFsKSB7CiAgICAgIHRoaXMub3BlbkRpYWxvZyA9IHZhbAogICAgfSwKICAgIGNsb3NlRGlhbG9nKCkgewogICAgICB0aGlzLmltcG9ydERpYWxvZyA9IGZhbHNlCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQogICAgfSwKICAgIGNsb3NlQXNzZXREaWFsb2coKSB7CiAgICAgIHRoaXMuc2VydmVyT3BlbiA9IGZhbHNlCiAgICAgIHRoaXMuc2FmZU9wZW4gPSBmYWxzZQogICAgICB0aGlzLm5ldHdvcmtPcGVuID0gZmFsc2UKICAgIH0sCiAgICBjbG9zZUZsb3coaXNyUmVmcmVzaCkgewogICAgICB0aGlzLmZsb3dWaXNpYmxlID0gZmFsc2UKICAgICAgaWYgKGlzclJlZnJlc2gpIHRoaXMuZ2V0TGlzdCgpCiAgICB9LAogICAgZmxvd1RlbXBsYXRlU2VsZWN0Q2hhbmdlKHZhbCkgewogICAgICB0aGlzLmZsb3dUZW1wbGF0ZVNlbGVjdFZpc2libGUgPSBmYWxzZQogICAgICB0aGlzLmZsb3dWaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLmN1cnJlbnRGbG93RGF0YS5mbG93SWQgPSB2YWwKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuJHJlZnMuRmxvd0JveC5pbml0KHRoaXMuY3VycmVudEZsb3dEYXRhKQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZUF0Y0FnZUNsaWNrKGF0Y0FnZSkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmF0dGFja1NlZyA9IGF0Y0FnZQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkKICAgIH0sCiAgICBoYW5kbGVQcm9wc1F1ZXJ5KHZhbCkgewogICAgICBpZiAodmFsICYmIE9iamVjdC5rZXlzKHZhbCkubGVuZ3RoID4gMCkgewogICAgICAgIGlmICh2YWwuYXR0YWNrU2VnICYmIHRoaXMuJHJlZnMuYXRjQWdlKSB7CiAgICAgICAgICB0aGlzLiRyZWZzLmF0Y0FnZS5jdXJyZW50U2VsZWN0ZWRDYXJkID0gdmFsLmF0dGFja1NlZwogICAgICAgIH0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gdmFsCiAgICAgICAgaWYgKHZhbC5zdGFydFRpbWUgJiYgdmFsLmVuZFRpbWUpIHsKICAgICAgICAgIHRoaXMucmFuZ2VUaW1lID0gW3ZhbC5zdGFydFRpbWUsIHZhbC5lbmRUaW1lXQogICAgICAgIH0KICAgICAgICBpZiAodmFsLmhhbmRsZSA9PSAnMScpIHsKICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuaGFuZGxlU3RhdGUgPSAnMScKICAgICAgICB9IGVsc2UgaWYgKHZhbC5oYW5kbGUgPT0gJzAnKSB7CiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmhhbmRsZVN0YXRlID0gJzAnCiAgICAgICAgfQogICAgICAgIGlmICh2YWwuZGF0YXNvdXJjZSkgewogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kYXRhU291cmNlID0gcGFyc2VJbnQodmFsLmRhdGFzb3VyY2UpCiAgICAgICAgfQogICAgICAgIHRoaXMuZ2V0VGhyZWF0ZW5EaWN0KCkKICAgICAgICB0aGlzLmdldERlcHRzRGF0YSgpCiAgICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpCiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVBcHBsaWNhdGlvblRhZ1Nob3coYXBwbGljYXRpb25MaXN0KSB7CiAgICAgIGlmICghYXBwbGljYXRpb25MaXN0IHx8IGFwcGxpY2F0aW9uTGlzdC5sZW5ndGggPCAxKSB7CiAgICAgICAgcmV0dXJuICcnCiAgICAgIH0KICAgICAgbGV0IHJlc3VsdCA9IGFwcGxpY2F0aW9uTGlzdFswXS5hc3NldE5hbWUKICAgICAgaWYgKGFwcGxpY2F0aW9uTGlzdC5sZW5ndGggPiAxKSB7CiAgICAgICAgcmVzdWx0ICs9ICcuLi4nCiAgICAgIH0KICAgICAgcmV0dXJuIHJlc3VsdAogICAgfSwKCiAgICBoYW5kbGVCbG9ja2luZygpIHsKICAgICAgaWYgKHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubGVuZ3RoIDwgMSkgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup6KaB6Zi75pat55qEaXAnKQogICAgICB0aGlzLmJsb2NraW5nRGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgbGV0IGFyciA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5zcmNJcCkKICAgICAgYXJyID0gQXJyYXkuZnJvbShuZXcgU2V0KGFycikpCiAgICAgIHRoaXMuJHNldCh0aGlzLmJsb2NraW5nRm9ybSwgJ2Jsb2NrX2lwJywgYXJyLmpvaW4oJzsnKSkKICAgIH0sCiAgICBibG9ja2luZ1N1Ym1pdCgpIHsKICAgICAgdGhpcy4kcmVmc1snYmxvY2tpbmdGb3JtJ10udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgYWRkQmxvY2tJcCh0aGlzLmJsb2NraW5nRm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+a3u+WKoOaIkOWKnycpCiAgICAgICAgICB9KS5maW5hbGx5KCgpID0+IHsKICAgICAgICAgICAgdGhpcy5ibG9ja2luZ0RpYWxvZ1Zpc2libGUgPSBmYWxzZQogICAgICAgICAgICB0aGlzLiRyZWZzLm11bHRpcGxlVGFibGUuY2xlYXJTZWxlY3Rpb24oKQogICAgICAgICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gW10KICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGdldERldmljZUNvbmZpZ0xpc3QoKSB7CiAgICAgIGxpc3REZXZpY2VDb25maWcoeyBxdWVyeUFsbERhdGE6IHRydWUgfSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZGV2aWNlQ29uZmlnTGlzdCA9IHJlcy5yb3dzCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOWIt+aWsOaUu+WHu+aWueWQkQogICAgaGFuZGxlUmVmcmVzaEF0dGFja0RpcmVjdGlvbihyb3csIGV2ZW50KSB7CiAgICAgIC8vIOmYu+atouS6i+S7tuWGkuazoe+8jOmBv+WFjeinpuWPkeihjOeCueWHu+S6i+S7tgogICAgICBpZiAoZXZlbnQpIHsKICAgICAgICBldmVudC5zdG9wUHJvcGFnYXRpb24oKQogICAgICB9CgogICAgICAvLyDpmLLmraLph43lpI3ngrnlh7sKICAgICAgaWYgKHJvdy5yZWZyZXNoaW5nKSB7CiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRoaXMuJHNldChyb3csICdyZWZyZXNoaW5nJywgdHJ1ZSkKCiAgICAgIHJlZnJlc2hBdHRhY2tEaXJlY3Rpb24oW3Jvdy5pZF0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAgJiYgcmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICAgIC8vIOabtOaWsOW9k+WJjeihjOeahOaUu+WHu+aWueWQkQogICAgICAgICAgY29uc3QgcmVmcmVzaFJlc3VsdCA9IHJlc3BvbnNlLmRhdGFbMF0KICAgICAgICAgIHRoaXMuJHNldChyb3csICdhdHRhY2tEaXJlY3Rpb24nLCByZWZyZXNoUmVzdWx0LmF0dGFja0RpcmVjdGlvbikKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5pS75Ye75pa55ZCR5Yi35paw5oiQ5YqfJykKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5pS75Ye75pa55ZCR5Yi35paw5aSx6LSlJykKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICBjb25zb2xlLmVycm9yKCfliLfmlrDmlLvlh7vmlrnlkJHlpLHotKU6JywgZXJyb3IpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5pS75Ye75pa55ZCR5Yi35paw5aSx6LSl77yaJyArIChlcnJvci5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nKSkKICAgICAgfSkuZmluYWxseSgoKSA9PiB7CiAgICAgICAgdGhpcy4kc2V0KHJvdywgJ3JlZnJlc2hpbmcnLCBmYWxzZSkKICAgICAgfSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["eventList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwt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file": "eventList.vue", "sourceRoot": "src/views/frailty/event/component", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          ref=\"queryForm\"\n          :model=\"queryParams\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"最近告警时间\" label-width=\"98px\">\n                <el-date-picker\n                  v-model=\"rangeTime\"\n                  type=\"datetimerange\"\n                  range-separator=\"至\"\n                  start-placeholder=\"开始日期\"\n                  end-placeholder=\"结束日期\"\n                  :default-time=\"['00:00:00', '23:59:59']\"\n                />\n              </el-form-item>\n            </el-col>\n            <!--            <el-col :span=\"6\">\n              <el-form-item label=\"告警等级\" prop=\"alarmLevel\">\n                <el-select\n                  clearable\n                  v-model=\"queryParams.alarmLevel\"\n                  placeholder=\"请选择告警等级\"\n                >\n                  <el-option\n                    v-for=\"dict in dict.type.threaten_type\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置状态\" prop=\"\">\n                <el-select v-model=\"queryParams.handleState\" placeholder=\"请选择处置状态\" clearable>\n                  <el-option\n                    v-for=\"(item,index) in handleStateOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"告警类型\" prop=\"threatenType\">\n                <el-cascader\n                  v-model=\"queryParams.threatenType\"\n                  :options=\"threatenDict\"\n                  clearable\n                  :props=\"{ label: 'dictLabel', value: 'dictValue' }\"\n                  placeholder=\"请选择告警类型\"\n                >\n                  <template slot-scope=\"{ node, data }\">\n                    <span>{{ data.dictLabel }}</span>\n                    <span v-if=\"!node.isLeaf\"> ({{ data.children.length }}) </span>\n                  </template>\n                </el-cascader>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleQuery\"\n                >查询\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n                <el-button v-if=\"!showAll\" class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\">\n                  展开\n                </el-button>\n                <el-button v-else class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\">收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row v-if=\"showAll\" :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"告警名称\" prop=\"threatenName\">\n                <el-input\n                  v-model=\"queryParams.threatenName\"\n                  placeholder=\"请输入告警名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"源IP\" prop=\"srcIp\">\n                <el-input\n                  v-model=\"queryParams.srcIp\"\n                  placeholder=\"请输入源IP\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"目标IP\" prop=\"destIp\">\n                <el-input\n                  v-model=\"queryParams.destIp\"\n                  placeholder=\"请输入目标IP\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row v-if=\"showAll\" :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"数据来源\" prop=\"dataSource\">\n                <el-select\n                  v-model=\"queryParams.dataSource\"\n                  placeholder=\"请选择数据来源\"\n                  clearable\n                  @change=\"$forceUpdate()\"\n                >\n                  <el-option :key=\"1\" label=\"探测\" :value=\"1\" />\n                  <el-option :key=\"2\" label=\"手动\" :value=\"2\" />\n                  <el-option :key=\"8\" label=\"流量\" :value=\"8\" />\n                  <el-option :key=\"9\" label=\"探针\" :value=\"9\" />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属部门\" prop=\"deptId\">\n                <dept-select v-model=\"queryParams.deptId\" />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"同步状态\">\n                <el-select v-model=\"queryParams.synchronizationStatus\" placeholder=\"请选择同步状态\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.synchronization_status\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row v-if=\"showAll\" :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"通报状态\" prop=\"flowState\">\n                <el-select v-model=\"queryParams.flowState\" placeholder=\"请选择通报状态\" clearable>\n                  <el-option\n                    v-for=\"(item,index) in flowStateOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置人\">\n                <el-input\n                  v-model=\"queryParams.disposer\"\n                  placeholder=\"请输入处置人\"\n                  clearable\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属探针\">\n                <el-select v-model=\"queryParams.deviceConfigId\" filterable clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in deviceConfigList\"\n                    :key=\"item.id\"\n                    :label=\"item.deviceName\"\n                    :value=\"item.id\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row v-if=\"showAll\" :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"攻击方向\">\n                <el-select v-model=\"queryParams.attackDirection\" placeholder=\"请选择攻击方向\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.attack_direction\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n      <!--      <div class=\"custom-content-search-chunk\" style=\"margin-bottom: 8px\">\n        <attack-stage ref=\"atcAge\" @handleClick=\"handleAtcAgeClick\"/>\n      </div>-->\n      <div\n        class=\"custom-content-container\"\n        :style=\"showAll ? { height: 'calc(100% - 298px)' } :{ height: 'calc(100% - 208px)' }\"\n      >\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">告警列表</span></div>\n          <div style=\"width: 60%; margin-left: 10%\">\n            <!--            <attack-stage-text ref=\"atcAge\" @handleClick=\"handleAtcAgeClick\" />-->\n            <attack-stage-text ref=\"atcAge\" />\n          </div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-col :span=\"1.5\">\n                  <el-button\n                    v-hasPermi=\"['system:threadten:add']\"\n                    type=\"primary\"\n                    size=\"small\"\n                    @click=\"handleAdd\"\n                  >新增\n                  </el-button>\n                </el-col>\n                <el-col :span=\"1.5\">\n                  <el-button\n                    class=\"btn1\"\n                    size=\"small\"\n                    @click=\"handleBlocking\"\n                  >批量阻断</el-button>\n                </el-col>\n                <el-col :span=\"1.5\">\n                  <el-button\n                    v-hasPermi=\"['system:threadten:import']\"\n                    class=\"btn1\"\n                    size=\"small\"\n                    @click=\"handleImport\"\n                  >导入\n                  </el-button>\n                </el-col>\n                <el-button\n                  v-hasPermi=\"['system:threadten:export']\"\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                >导出\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <el-table\n          ref=\"multipleTable\"\n          v-loading=\"loading\"\n          height=\"100%\"\n          :data=\"threatenWarnList\"\n          @row-click=\"handleRowClick\"\n          @selection-change=\"handleSelectionChange\"\n        >\n          <el-table-column type=\"selection\" width=\"55\" />\n          <!--          <el-table-column type=\"index\" width=\"100\" label=\"序号\"/>-->\n          <el-table-column label=\"最近告警时间\" width=\"200\" prop=\"updateTime\" />\n          <el-table-column label=\"告警名称\" prop=\"threatenName\" min-width=\"260\" />\n          <el-table-column label=\"告警类型\" prop=\"threatenType\" width=\"150\" />\n          <el-table-column label=\"告警等级\" prop=\"alarmLevel\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <dict-tag :options=\"dict.type.threaten_type\" :value=\"scope.row.alarmLevel\" />\n            </template>\n          </el-table-column>\n          <el-table-column label=\"源IP\" prop=\"srcIp\" width=\"180\">\n            <template slot-scope=\"scope\">\n              <div style=\"display: flex; align-items: center; justify-content: flex-start\">\n                <span>{{ scope.row.srcIp }}</span>\n                <img v-if=\"scope.row.isBlocking\" style=\"width: 24px;margin-left: 10px\" src=\"@/assets/images/block.png\" alt=\"\">\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"目标IP/应用\" width=\"150\" prop=\"destIp\" />\n          <el-table-column label=\"处置人\" prop=\"disposer\" width=\"150\" :formatter=\"disposerFormatter\" />\n          <el-table-column label=\"关联业务系统\" prop=\"businessApplicationList\" width=\"200\">\n            <template slot-scope=\"scope\">\n              <el-tooltip\n                v-if=\"scope.row.businessApplications && scope.row.businessApplications.length > 0\"\n                placement=\"bottom-end\"\n                effect=\"light\"\n              >\n                <div slot=\"content\">\n                  <div\n                    v-for=\"(item,tagIndex) in scope.row.businessApplications\"\n                    v-if=\"tagIndex <= 5\"\n                    :key=\"item.assetId\"\n                    class=\"overflow-tag\"\n                  >\n                    <el-tag type=\"primary\"><span>{{ item.assetName }}</span></el-tag>\n                  </div>\n                  <div v-if=\"scope.row.businessApplications.length > 5\">\n                    <el-tag type=\"primary\"><span>...</span></el-tag>\n                  </div>\n                </div>\n                <el-tag type=\"primary\" class=\"asset-tag\">\n                  <span>{{ handleApplicationTagShow(scope.row.businessApplications) }}</span></el-tag>\n              </el-tooltip>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"通报状态\" prop=\"flowState\" width=\"150\" :formatter=\"flowStateFormatter\" />\n          <el-table-column label=\"处置状态\" prop=\"handleState\" width=\"150\" :formatter=\"handleStateFormatter\" />\n          <el-table-column label=\"所属部门\" prop=\"deptName\" width=\"150\" />\n          <el-table-column label=\"数据来源\" prop=\"dataSource\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <span v-if=\"scope.row.dataSource == '1'\">探测</span>\n              <span v-else-if=\"scope.row.dataSource == '2'\">手动</span>\n              <span v-else-if=\"scope.row.dataSource == '8'\">流量</span>\n              <span v-else-if=\"scope.row.dataSource == '9'\">探针</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"发现次数\" prop=\"alarmNum\" width=\"150\" />\n          <el-table-column label=\"同步状态\" prop=\"synchronizationStatus\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <span v-if=\"scope.row.synchronizationStatus === '0'\">未同步</span>\n              <span v-else-if=\"scope.row.synchronizationStatus === '1'\">已同步</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"攻击方向\" prop=\"attackDirection\" width=\"180\">\n            <template slot-scope=\"scope\">\n              <div style=\"display: flex; align-items: center; justify-content: space-between\">\n                <dict-tag :options=\"dict.type.attack_direction\" :value=\"scope.row.attackDirection\" />\n                <el-button\n                  type=\"text\"\n                  size=\"mini\"\n                  icon=\"el-icon-refresh\"\n                  :loading=\"scope.row.refreshing\"\n                  title=\"刷新攻击方向\"\n                  style=\"margin-left: 8px; color: #409EFF;\"\n                  @click=\"handleRefreshAttackDirection(scope.row, $event)\"\n                />\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"操作\"\n            width=\"250\"\n            fixed=\"right\"\n            :show-overflow-tooltip=\"false\"\n            class-name=\"small-padding fixed-width\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                v-hasPermi=\"['system:threadten:query']\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleDetail(scope.row)\"\n              >详情\n              </el-button>\n              <el-button\n                v-if=\"scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')\"\n                v-hasPermi=\"['system:threadten:edit']\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleEdit(scope.row)\"\n              >编辑\n              </el-button>\n              <el-button\n                v-if=\"scope.row.workId==null && !(scope.row.handleState === '3')\"\n                v-hasPermi=\"['system:threadten:remove']\"\n                size=\"mini\"\n                type=\"text\"\n                class=\"table-delBtn\"\n                @click=\"handleDelete(scope.row)\"\n              >删除\n              </el-button>\n              <el-button\n                v-if=\"scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')\"\n                v-hasPermi=\"['system:threadten:edit']\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"showHandle(scope.row)\"\n              >处置\n              </el-button>\n              <el-button\n                v-if=\"!(scope.row.handleState === '1' || scope.row.handleState === '3') && (scope.row.flowState == null || scope.row.flowState === '99')\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"addOrUpdateFlowHandle(null,null,scope.row)\"\n              >创建通报\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n    <!-- 处置威胁情报对话框! -->\n    <el-dialog\n      title=\"快速处置\"\n      :visible.sync=\"showHandleDialog\"\n      width=\"600px\"\n      append-to-body\n    >\n      <el-form ref=\"handleStateForm\" :model=\"handleForm\" :rules=\"handleRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"handleState\">\n          <el-select v-model=\"handleForm.handleState\" clearable placeholder=\"请选择处置状态\">\n            <el-option\n              v-for=\"dict in dict.type.handle_state\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"parseInt(dict.value)\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input v-model=\"handleForm.handleDesc\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入处置说明\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHandleForm\">确 定</el-button>\n        <el-button @click=\"showHandleDialog = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n    <!-- 添加或修改威胁情报对话框! -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"openThrenten\"\n      width=\"80%\"\n      append-to-body\n      :before-close=\"handleClose\"\n    >\n      <el-row>\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"106px\" :disabled=\"!editable\">\n          <el-col :span=\"24\" class=\"mb8\">\n            <el-divider direction=\"vertical\" />\n            <div class=\"my-title\">基本信息</div>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"告警名称\" prop=\"threatenName\">\n              <el-input v-model=\"form.threatenName\" placeholder=\"请输入告警名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警等级\" prop=\"alarmLevel\">\n              <el-select v-model=\"form.alarmLevel\" placeholder=\"请选择告警等级\" clearable>\n                <el-option\n                  v-for=\"dict in dict.type.threaten_type\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警类型\" prop=\"threatenType\">\n              <el-cascader\n                v-model=\"form.threatenType\"\n                :options=\"threatenDict\"\n                clearable\n                placeholder=\"请选择告警类型\"\n                :props=\"{ label: 'dictLabel', value: 'dictValue' }\"\n                style=\"width: 100%\"\n              >\n                <template slot-scope=\"{ node, data }\">\n                  <span>{{ data.dictLabel }}</span>\n                  <span v-if=\"!node.isLeaf\"> ({{ data.children.length }}) </span>\n                </template>\n              </el-cascader>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"告警原因\" prop=\"reason\">\n              <el-input\n                v-model=\"form.reason\"\n                :autosize=\"{minRows: 3, maxRows: 3}\"\n                type=\"textarea\"\n                placeholder=\"请输入告警原因\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"处置建议\" prop=\"handSuggest\">\n              <el-input\n                v-model=\"form.handSuggest\"\n                :autosize=\"{minRows: 3, maxRows: 3}\"\n                type=\"textarea\"\n                placeholder=\"请输入告警建议\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警时间\" prop=\"createTime\">\n              <el-date-picker\n                v-model=\"form.createTime\"\n                type=\"date\"\n                placeholder=\"选择告警时间\"\n                format=\"yyyy 年 MM 月 dd 日\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"最近告警时间\" prop=\"updateTime\">\n              <el-date-picker\n                v-model=\"form.updateTime\"\n                type=\"date\"\n                placeholder=\"选择最近告警时间\"\n                format=\"yyyy 年 MM 月 dd 日\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"威胁标签\" prop=\"label\">\n              <DynamicTag v-model=\"form.label\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"关联设备\" prop=\"associaDevice\">\n              <el-input\n                v-model=\"form.associaDevice\"\n                placeholder=\"请输入关联设备\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\" class=\"mb8\">\n            <el-divider direction=\"vertical\" />\n            <div class=\"my-title\">攻击关系</div>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"源IP\" prop=\"srcIp\">\n              <el-input\n                v-model=\"form.srcIp\"\n                style=\"width: 50%\"\n                placeholder=\"请输入源IP\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"源IP端口\" prop=\"srcPort\">\n              <el-input\n                v-model=\"form.srcPort\"\n                style=\"width: 30%\"\n                placeholder=\"请输入源IP端口\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"目标IP/应用\" prop=\"destIp\">\n              <el-input\n                v-model=\"form.destIp\"\n                style=\"width: 50%\"\n                placeholder=\"请输入目标IP\"\n              />\n              <!--目标ip查询后有多个及显示资产数据，提交表单传(assetId： 资产id,deptId：部门id)-->\n              <el-select\n                v-show=\"assetInfoList.length >= 2\"\n                v-model=\"form.assetId\"\n                style=\"width: 50%;\"\n                placeholder=\"请确认疑似资产\"\n              >\n                <el-option\n                  v-for=\"item in assetInfoList\"\n                  :key=\"item.assetId\"\n                  :label=\"item.value\"\n                  :value=\"item.assetId\"\n                />\n              </el-select>\n              <el-select v-show=\"false\" v-model=\"form.deptId\" style=\"width: 50%;\" placeholder=\"请选择资产\">\n                <el-option v-for=\"item in assetInfoList\" :label=\"item.value\" :value=\"item.deptId\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"目标IP端口\" prop=\"destPort\">\n              <el-input\n                v-model=\"form.destPort\"\n                style=\"width: 30%\"\n                placeholder=\"请输入目标IP端口\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col v-if=\"form.fileUrl!=null||editable\">\n            <el-col :span=\"24\" class=\"mb8\">\n              <el-divider direction=\"vertical\" />\n              <div class=\"my-title\">文件上传</div>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"上传文件\" prop=\"fileUrl\">\n                <file-upload\n                  v-model=\"form.fileUrl\"\n                  :dis-upload=\"!editable\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-col>\n        </el-form>\n      </el-row>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button\n          v-if=\"editable\"\n          type=\"primary\"\n          @click=\"submitForm\"\n        >确 定\n        </el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      v-if=\"openDialog\"\n      :title=\"title\"\n      :visible.sync=\"openDialog\"\n      width=\"80%\"\n      append-to-body\n    >\n      <el-tabs v-model=\"activeName\">\n        <el-tab-pane label=\"事件详情\" name=\"detail\">\n          <alarm-detail\n            v-if=\"openDialog\"\n            :asset-data=\"assetData\"\n            @openDetail=\"openDetail\"\n          />\n        </el-tab-pane>\n        <el-tab-pane v-if=\"assetData.srcIp\" label=\"攻击IP关联事件\" name=\"attack\">\n          <attack-detail :detail-type=\"'attack'\" :host-ip=\"assetData.srcIp\" :current-asset-data=\"assetData\" />\n        </el-tab-pane>\n        <el-tab-pane v-if=\"assetData.destIp\" label=\"受害IP关联事件\" name=\"suffer\">\n          <suffer-detail :detail-type=\"'suffer'\" :host-ip=\"assetData.destIp\" :current-asset-data=\"assetData\" />\n        </el-tab-pane>\n      </el-tabs>\n    </el-dialog>\n\n    <el-dialog title=\"导入威胁告警\" :visible.sync=\"importDialog\" width=\"800px\" append-to-body>\n      <import-threaten v-if=\"importDialog\" @closeDialog=\"closeDialog\" />\n    </el-dialog>\n\n    <el-dialog title=\"查看服务器资产\" :visible.sync=\"serverOpen\" width=\"80%\" append-to-body>\n      <server-add v-if=\"serverOpen\" :asset-id=\"assetId\" :editable=\"editable\" @cancel=\"closeAssetDialog()\" />\n    </el-dialog>\n\n    <el-dialog title=\"查看安全设备资产\" :visible.sync=\"safeOpen\" width=\"80%\" append-to-body>\n      <safe-add v-if=\"safeOpen\" :asset-id=\"assetId\" :editable=\"editable\" @cancel=\"closeAssetDialog()\" />\n    </el-dialog>\n\n    <el-dialog title=\"查看告警策略\" :visible.sync=\"viewStrategy\" width=\"400\" append-to-body>\n      <view-strategy v-if=\"viewStrategy\" @close=\"viewStrategy=false\" />\n    </el-dialog>\n\n    <el-dialog title=\"告警策略配置\" :visible.sync=\"threatenConfigFlag\" width=\"800\" append-to-body>\n      <threaten-config-list v-if=\"threatenConfigFlag\" @close=\"threatenConfigFlag=false\" />\n    </el-dialog>\n\n    <el-dialog title=\"批量阻断\" :visible.sync=\"blockingDialogVisible\" width=\"400\">\n      <el-form ref=\"blockingForm\" :model=\"blockingForm\" :rules=\"blockingRules\" class=\"blocking-form\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"阻断ip\" prop=\"block_ip\">\n              <span slot=\"label\">\n                阻断ip\n                <template>\n                  <el-tooltip placement=\"top\">\n                    <div slot=\"content\">默认加载选择的事件的源IP，多个则以“;”隔开</div>\n                    <i class=\"el-icon-info\" />\n                  </el-tooltip>\n                </template>\n              </span>\n              <el-input v-model=\"blockingForm.block_ip\" placeholder=\"请输入ip\">\n                <el-popover\n                  slot=\"suffix\"\n                  placement=\"bottom\"\n                  width=\"100\"\n                  trigger=\"hover\"\n                >\n                  <ul>\n                    <li v-for=\"(ip, index) in blockingIpList\" :key=\"index\">{{ ip }}</li>\n                  </ul>\n                  <i slot=\"reference\" class=\"el-icon-more\" />\n                </el-popover>\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"阻断时长\" prop=\"duration_time\">\n              <el-select v-model=\"blockingForm.duration_time\" placeholder=\"请选择阻断时长\">\n                <el-option\n                  v-for=\"item in blockingDuration\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-form-item label=\"备注\" prop=\"remarks\">\n            <el-input v-model=\"blockingForm.remarks\" type=\"textarea\" maxlength=\"500\" show-word-limit placeholder=\"请输入阻断描述\" />\n          </el-form-item>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"blockingSubmit\">确 定</el-button>\n        <el-button @click=\"blockingDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <FlowBox v-if=\"flowVisible\" ref=\"FlowBox\" @close=\"closeFlow\" />\n    <flow-template-select :show.sync=\"flowTemplateSelectVisible\" @change=\"flowTemplateSelectChange\" />\n\n    <publish-click-dialog\n      :publish-dialog-visible=\"publishDialogVisible\"\n      title=\"发布告警事件\"\n      width=\"30%\"\n      @updateVisible=\"(val) => { this.publishDialogVisible = val}\"\n    />\n  </div>\n</template>\n\n<script>\nimport { parseTime } from '@/utils/ruoyi'\nimport { getMulTypeDict } from '../../../../api/system/dict/data'\nimport { getDeptSystem } from '../../../../api/monitor2/applicationAssets'\nimport { getAlarm, delAlarm, listAlarm, addAlarm, updateAlarm, addBlockIp, refreshAttackDirection } from '../../../../api/threaten/threatenWarn'\nimport { getAssetInfoByIp } from '../../../../api/safe/overview'\nimport DynamicTag from '../../../../components/DynamicTag'\nimport AlarmDetail from '../../../basis/securityWarn/alarmDetail'\nimport importThreaten from '@/views/basis/securityWarn/importThreaten.vue'\nimport ThreatenConfigList from '@/views/basis/securityWarn/threatenConfigList.vue'\nimport ServerAdd from '../../../hhlCode/component/application/adds/serverAdd'\nimport SafeAdd from '../../../hhlCode/component/application/adds/safeAdd'\nimport ViewStrategy from '../../../basis/securityWarn/viewStrategy'\nimport PublishClickDialog from '../../../basis/securityWarn/publishClickDialog'\nimport FlowBox from '../../../zeroCode/workFlow/components/FlowBox'\nimport FlowTemplateSelect from '../../../../components/FlowTemplateSelect'\nimport AttackStage from '../../../threat/overview/attackStage'\nimport AttackViewList from './attackViewList'\nimport SufferViewList from './sufferViewList'\nimport attackDetail from './detail/index.vue'\nimport sufferDetail from './detail/index.vue'\nimport DeptSelect from '@/views/components/select/deptSelect.vue'\nimport { uniqueArr } from '@/utils'\nimport { FlowEngineInfo } from '@/api/lowCode/FlowEngine'\nimport { listUser } from '@/api/system/user'\nimport AttackStageText from '@/views/threat/overview/attackStageText.vue'\nimport {\n  listDeviceConfig\n} from '@/api/ffsafe/deviceConfig'\n\nexport default {\n  name: 'EventList',\n  components: {\n    AttackStageText,\n    DeptSelect,\n    SufferViewList,\n    AttackViewList,\n    AttackStage,\n    FlowTemplateSelect,\n    FlowBox,\n    PublishClickDialog,\n    attackDetail,\n    sufferDetail,\n    ThreatenConfigList, ViewStrategy, SafeAdd, ServerAdd, importThreaten, AlarmDetail, DynamicTag\n  },\n  dicts: ['threaten_type', 'attack_stage', 'attack_result', 'handle_state', 'synchronization_status', 'attack_direction'],\n  props: {\n    propsActiveName: {\n      type: String\n    },\n    propsQueryParams: {\n      type: Object,\n      default: function() {\n        return null\n      }\n    },\n    currentBtn: {\n      type: Number,\n      default: null\n    }\n  },\n  data() {\n    const validateBlockIp = (rule, value, callback) => {\n      if (!value) {\n        return callback(new Error('IP不能为空'))\n      }\n      // let pattern = /^((1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2})\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0)\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0)\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0))$/;\n      const pattern = /^\\s*((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\\s*;\\s*((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))*\\s*$/\n      if (!pattern.test(value)) {\n        return callback(new Error('请输入正确的IP'))\n      }\n      return callback()\n    }\n    return {\n      userList: [],\n      showHandleDialog: false,\n      handleForm: {\n        id: '',\n        handleDesc: '',\n        handleState: ''\n      },\n      handleRules: {\n        handleState: [\n          { required: true, message: '请选择处理状态', trigger: 'blur' }\n        ]\n      },\n      showAll: false,\n      threatenDict: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        handleState: '0'\n      },\n      deptOptions: [],\n      rangeTime: [],\n      loading: false,\n      threatenWarnList: [],\n      total: 0,\n      title: '',\n      openThrenten: false,\n      form: {},\n      rules: {\n        threatenName: [\n          { required: false, min: 0, max: 500, message: '告警名称不能超过500字符', trigger: 'blur' },\n          { required: true, message: '请输入告警名称', trigger: 'blur' },\n          {\n            required: true,\n            pattern: /^[^\\s]+/,\n            message: '不能以空格开头！',\n            trigger: 'blur'\n          }\n        ],\n        alarmLevel: [\n          { required: true, message: '请输入告警等级', trigger: 'blur' }\n        ],\n        threatenType: [\n          { required: true, message: '请输入告警类型', trigger: 'blur' }\n        ],\n        reason: [\n          { required: false, min: 0, max: 2000, message: '告警原因不能超过2000字符', trigger: 'blur' },\n          { required: true, message: '请输入告警原因', trigger: 'blur' }\n        ],\n        handSuggest: [\n          { required: false, min: 0, max: 2000, message: '告警建议不能超2000字符', trigger: 'blur' },\n          { required: true, message: '请输入告警建议', trigger: 'blur' }\n        ],\n        logTime: [\n          { required: true, message: '请输入日志时间', trigger: 'blur' }\n        ],\n        createTime: [\n          { required: true, message: '请输入告警时间', trigger: 'blur' }\n        ],\n        srcIp: [\n          { required: false, min: 0, max: 30, message: '源IP不能超过30字符', trigger: 'blur' },\n          {\n            required: true,\n            pattern: '^(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)){3}$',\n            message: 'IP地址不能为空或格式不正确',\n            trigger: 'blur'\n          }\n        ],\n        srcPort: [\n          { required: false, min: 0, max: 11, message: '源IP端口不能超过11字符', trigger: 'blur' },\n          { required: true, pattern: '^[0-9]*[1-9][0-9]*$', message: '源IP端口不能为空或格式不正确', trigger: 'blur' }\n        ],\n        destIp: [\n          { required: false, min: 0, max: 30, message: '目标IP不能超过30字符', trigger: 'blur' },\n          {\n            required: true,\n            pattern: '^(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)){3}$',\n            message: 'IP地址不能为空或格式不正确',\n            trigger: 'blur'\n          }\n        ],\n        destPort: [\n          { required: false, min: 0, max: 11, message: '目标IP端口不能超过11字符', trigger: 'blur' },\n          { required: true, pattern: '^[0-9]*[1-9][0-9]*$', message: '目标IP端口不能为空或格式不正确', trigger: 'blur' }\n        ],\n        mateRule: [\n          { required: false, min: 0, max: 200, message: '分析规则不能超过200字符', trigger: 'blur' }\n        ],\n        associaDevice: [\n          { required: false, min: 0, max: 200, message: '关联设备不能超过200字符', trigger: 'blur' }\n        ],\n        attackType: [\n          { required: false, min: 0, max: 100, message: '攻击方式不能超过100字符', trigger: 'blur' },\n          { required: true, message: '请输入攻击方式', trigger: 'blur' }\n        ],\n        attackStage: [\n          { required: false, min: 0, max: 100, message: '攻击链阶段不能超过100字符', trigger: 'blur' },\n          { required: true, message: '请输入攻击链阶段', trigger: 'blur' }\n        ],\n        attackResult: [\n          { required: false, min: 0, max: 100, message: '攻击结果不能超过100字符', trigger: 'blur' },\n          { required: true, message: '请输入攻击结果', trigger: 'blur' }\n        ]\n      },\n      blockingForm: {},\n      blockingRules: {\n        block_ip: [\n          // 可同时传多个，用\";\"隔开\n          { validator: validateBlockIp, trigger: 'blur' }\n        ],\n        duration_time: [\n          { required: true, message: '请选择阻断时长', trigger: 'blur' }\n        ],\n        remarks: [\n          { required: false, min: 0, max: 500, message: '备注不能超过500字符', trigger: 'blur' }\n        ]\n      },\n      blockingIpList: [],\n      blockingDialogVisible: false, // 批量阻断弹窗\n      editable: true,\n      assetInfoList: [],\n      openDialog: false,\n      assetData: {},\n      importDialog: false,\n      serverOpen: false,\n      assetId: null,\n      safeOpen: false,\n      threatenConfigFlag: false,\n      viewStrategy: false,\n      publishDialogVisible: false,\n      flowVisible: false,\n      flowTemplateSelectVisible: false,\n      flowStateOptions: [\n        {\n          label: '待审核',\n          value: 0\n        },\n        {\n          label: '待处置',\n          value: 1\n        },\n        {\n          label: '待反馈审核',\n          value: 2\n        },\n        {\n          label: '待验证',\n          value: 3\n        },\n        {\n          label: '已完成',\n          value: 4\n        },\n        {\n          label: '待提交',\n          value: -1\n        },\n        {\n          label: '未分配',\n          value: 99\n        }\n      ],\n      handleStateOptions: [\n        {\n          label: '未处置',\n          value: '0'\n        },\n        {\n          label: '已处置',\n          value: '1'\n        },\n        {\n          label: '忽略',\n          value: '2'\n        },\n        {\n          label: '处置中',\n          value: '3'\n        }\n      ],\n      activeName: 'detail',\n      syncStateOptions: [\n        {\n          label: '未同步',\n          value: 0\n        },\n        {\n          label: '已同步',\n          value: 1\n        }\n      ],\n      blockingDuration: [\n        {\n          label: '30分钟',\n          value: '30m'\n        },\n        {\n          label: '24小时',\n          value: '24h'\n        },\n        {\n          label: '48小时',\n          value: '48h'\n        },\n        {\n          label: '7天',\n          value: '168h'\n        },\n        {\n          label: '永久',\n          value: '永久'\n        }\n      ],\n      multipleSelection: [],\n      deviceConfigList: []\n    }\n  },\n  watch: {\n    // 监听目标ip\n    'form.destIp'(value, oldValue) {\n      var rg = /^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}$/\n      var reg = rg.test(value)\n      if (reg) {\n        // 根据ip获取资产数据\n        getAssetInfoByIp(value).then(response => {\n          if (response.data.length) {\n            const assetData = response.data\n            assetData.forEach(item => item.value = item.assetName + '-' + item.assetTypeDesc)\n            if (value !== oldValue && oldValue) {\n              this.form.assetId = ''\n              this.form.deptId = ''\n            }\n            // 资产数据有多条显示下拉框，只有一条不显示\n            if (assetData.length === 1) {\n              this.form.assetId = assetData[0].assetId\n              this.form.deptId = assetData[0].deptId\n            }\n            if (assetData.length > 1 && !this.form.assetId) {\n              this.form.assetId = ''\n              this.form.deptId = ''\n            }\n            this.assetInfoList = assetData\n          } else {\n            this.assetInfoList = []\n            return this.$message.warning('未查询到资产数据')\n          }\n        })\n      } else {\n        this.assetInfoList = []\n        this.form.assetId = ''\n        this.form.deptId = ''\n      }\n    },\n    propsActiveName() {\n      this.init()\n    },\n    propsQueryParams: {\n      handler(val) {\n        this.handlePropsQuery(val)\n      }\n    },\n    /* rangeTime(val) {\n      console.log(val)\n    },*/\n    'blockingForm.block_ip': {\n      handler(value) {\n        if (value) {\n          this.blockingIpList = value.split(';').map(ip => ip.trim()).filter(ip => ip)\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.getDeviceConfigList()\n  },\n  mounted() {\n    if (!this.$route.query || Object.keys(this.$route.query).length < 1) {\n      this.init()\n    } else {\n      this.handlePropsQuery(this.$route.query)\n    }\n  },\n  methods: {\n    init() {\n      // this.resetQuery()\n      this.getThreatenDict()\n      this.handleQuery()\n      this.getDeptsData()\n      this.getUserList()\n    },\n    getUserList() {\n      listUser({ pageNum: 1, pageSize: 1000 }).then(res => {\n        if (res.rows) {\n          this.userList = res.rows\n        }\n      })\n    },\n    handleQuery() {\n      this.propsQueryParams.alarmLevel = this.queryParams.alarmLevel\n      this.propsQueryParams.referenceId = this.queryParams.referenceId\n      // this.$emit('update:currentBtn',this.queryParams.alarmLevel?parseInt(this.queryParams.alarmLevel) : null)\n      this.queryParams = { ...this.queryParams, ...this.propsQueryParams }\n      if (this.rangeTime != null) {\n        this.queryParams.startTime = parseTime(this.rangeTime[0])\n        this.queryParams.endTime = parseTime(this.rangeTime[1])\n      } else {\n        this.queryParams.startTime = null\n        this.queryParams.endTime = null\n      }\n      this.queryParams.pageNum = 1\n      this.queryParams.pageSize = 10\n\n      if (!this.queryParams.startTime) {\n        this.queryParams.startTime = parseTime(new Date().setHours(-168, 0, 0, 0), '{y}-{m}-{d} 00:00:00') // 一周前，时间部分为 00:00:00\n      }\n      if (!this.queryParams.endTime) {\n        this.queryParams.endTime = parseTime(new Date().setHours(23, 59, 59, 999), '{y}-{m}-{d} 23:59:59') // 当前日期，时间部分为 23:59:59\n      }\n      this.rangeTime = [this.queryParams.startTime, this.queryParams.endTime]\n      this.total = 0\n      this.getList()\n      this.$nextTick(() => {\n        const data = JSON.parse(JSON.stringify(this.queryParams))\n        if (data.threatenType != null) {\n          data.threatenType = data.threatenType.join('/')\n        }\n        this.$refs.atcAge.initAttackStage(data)\n      })\n    },\n    // 获取告警类型多级字典数据\n    getThreatenDict() {\n      getMulTypeDict({\n        dictType: 'threaten_alarm_type'\n      }).then(res => {\n        this.threatenDict = res.data\n      })\n    },\n    // 获取部门数据\n    getDeptsData() {\n      getDeptSystem().then(res => this.deptOptions = res.data)\n    },\n    handleChange(val) {\n      // 获取所属部门最后id\n      if (val) {\n        this.queryParams.deptId = val[val.length - 1]\n      } else {\n        this.queryParams.deptId = ''\n      }\n    },\n    resetQuery() {\n      this.queryParams = {\n        threatenName: null,\n        threatenType: null,\n        alarmLevel: null,\n        referenceId: null,\n        srcIp: null,\n        destIp: null,\n        handleState: '0',\n        flowState: null,\n        updateTime: null,\n        attackDirection: null,\n        pageNum: 1,\n        pageSize: 10\n      }\n      const atcAge = this.$refs.atcAge\n      if (atcAge) {\n        atcAge.currentSelectedCard = null\n      }\n      this.rangeTime = null\n      this.handleQuery()\n    },\n    // 新增威胁情报\n    handleAdd() {\n      this.openThrenten = true\n      this.form = {}\n      this.editable = true\n      this.title = '新增威胁情报'\n      this.$set(this.form, 'assetId', '') // 解决el-select无法视图与数据的更新\n    },\n    // 导入功能\n    handleImport() {\n      this.importDialog = true\n    },\n    handleExport() {\n      this.download(\n        '/system/threadten/export',\n        {\n          ...this.queryParams\n        },\n        `威胁告警_${new Date().getTime()}.xlsx`\n      )\n    },\n\n    // 获取列表数据查询\n    handleRowClick(row, column, event) {\n      // 获取告警详情单个单元格数据进行筛选\n      if (row && row.id) {\n        if (column.property) {\n          if (column.property === 'flowState') {\n            this.queryParams[column.property] = !row[column.property] ? 99 : Number(row[column.property])\n            listAlarm({\n              [column.property]: !row[column.property] ? 99 : Number(row[column.property]),\n              pageNum: 1,\n              pageSize: 10,\n              startTime: parseTime(this.rangeTime[0]),\n              endTime: parseTime(this.rangeTime[1])\n            }).then(response => {\n              this.threatenWarnList = response.rows\n              this.threatenWarnList.forEach(item => {\n                item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc\n                if (item.assetType == 'null-null') {\n                  item.assetType = null\n                }\n              })\n              this.total = response.total\n              this.loading = false\n            })\n            return\n          } else if (column.property === 'threatenType') {\n            this.queryParams[column.property] = row[column.property].split('/')\n          } else if (column.property === 'alarmLevel') {\n            this.queryParams[column.property] = row[column.property].toString()\n          } else {\n            this.queryParams[column.property] = row[column.property]\n          }\n          listAlarm({\n            [column.property]: row[column.property],\n            pageNum: 1,\n            pageSize: 10,\n            startTime: parseTime(this.rangeTime[0]),\n            endTime: parseTime(this.rangeTime[1])\n          }).then(response => {\n            this.threatenWarnList = response.rows\n            this.threatenWarnList.forEach(item => {\n              item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc\n              if (item.assetType == 'null-null') {\n                item.assetType = null\n              }\n            })\n            this.total = response.total\n            this.loading = false\n          })\n        }\n      }\n    },\n\n    // 多选\n    handleSelectionChange(val) {\n      this.multipleSelection = val\n    },\n\n    flowStateFormatter(row, column, cellValue, index) {\n      let name = '未分配'\n      const match = this.flowStateOptions.find(item => item.value == cellValue)\n      if (match) {\n        name = match.label\n      }\n      return name\n    },\n    disposerFormatter(row, column, cellValue, index) {\n      let name = ''\n      if (cellValue) {\n        this.userList.forEach(e => {\n          if (e.userId == cellValue) {\n            name = e.nickName\n          }\n        })\n        return name\n      }\n      return name\n    },\n\n    handleStateFormatter(row, column, cellValue, index) {\n      let name = '未处置'\n      const match = this.handleStateOptions.find(item => item.value == cellValue)\n      if (match) {\n        name = match.label\n      }\n      return name\n    },\n    handleDetail(row) {\n      this.assetData = { ...row }\n      this.title = '查看告警详情'\n      this.openDetail(true)\n    },\n    showHandle(row) {\n      // 获取事件详情单个单元格数据进行筛选\n      if (row.handleState === '1' || row.handleState === '2') {\n        this.handleForm.handleState = parseInt(row.handleState)\n        this.handleForm.handleDesc = row.handleDesc\n      } else {\n        this.handleForm = {\n          handleDesc: '',\n          handleState: ''\n        }\n      }\n      this.handleForm.id = row.id\n      this.showHandleDialog = true\n    },\n    handleEdit(row) {\n      getAlarm(row.id).then(res => {\n        this.form = { ...res.data }\n        if (this.form.alarmLevel != null) {\n          this.form.alarmLevel = (this.form.alarmLevel).toString()\n        }\n        if (this.form.threatenType != null) {\n          this.form.threatenType = this.form.threatenType.split('/')\n        }\n        if (this.form.attackNum != null) {\n          this.form.attackNum = (this.form.attackNum).toString()\n        }\n        if (this.form.srcPort != null) {\n          this.form.srcPort = (this.form.srcPort).toString()\n        }\n        if (this.form.destPort != null) {\n          this.form.destPort = (this.form.destPort).toString()\n        }\n        this.title = '修改威胁情报'\n        this.openThrenten = true\n      })\n    },\n    handleDelete(row) {\n      const ids = row.id\n      const title = row.threatenName\n      this.$modal.confirm('是否确认删除告警名称为【' + title + '】的数据项?').then(() => {\n        return delAlarm(ids)\n      }).then(() => {\n        this.$message.success('删除成功')\n        this.getList()\n      }).catch(() => {\n\n      })\n    },\n    addOrUpdateFlowHandle(id, flowState, row) {\n      const data = {\n        id: id || '',\n        formType: 1,\n        opType: flowState ? 0 : '-1',\n        status: flowState,\n        row: row,\n        isWork: true\n      }\n      data.row.workType = '2'\n      data.row.eventType = 3\n      data.originType = 'event'\n      this.currentFlowData = data\n      this.loading = true\n      this.getConfigKey('default.flowTemplateId').then(res => {\n        const flowId = res.msg\n        if (flowId) {\n          this.getFlowEngineInfo(flowId)\n        } else {\n          this.flowTemplateSelectVisible = true\n        }\n      }).finally(() => {\n        this.loading = false\n      })\n    },\n    getFlowEngineInfo(val) {\n      FlowEngineInfo(val).then(res => {\n        if (res.data && res.data.flowTemplateJson) {\n          const data = JSON.parse(res.data.flowTemplateJson)\n          if (!data[0].flowId) {\n            this.$message.error('该流程模板异常,请重新选择')\n          } else {\n            this.currentFlowData.flowId = data[0].flowId\n            this.flowVisible = true\n            this.$nextTick(() => {\n              this.$refs.FlowBox.init(this.currentFlowData)\n            })\n          }\n        }\n      }).finally(() => {\n        this.loading = false\n      })\n    },\n    getList() {\n      this.loading = true\n      const queryParams = {\n        ...this.queryParams\n      }\n      if (queryParams.threatenType != null) {\n        queryParams.threatenType = queryParams.threatenType.join('/')\n      }\n      // 同步请求类型统计数据\n      this.$emit('getList', { ...queryParams })\n      listAlarm(queryParams).then(response => {\n        this.threatenWarnList = response.rows\n        this.threatenWarnList.forEach(item => {\n          item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc\n          if (item.assetType == 'null-null') {\n            item.assetType = null\n          }\n          if (item.deptName) {\n            const deptNameArr = uniqueArr(item.deptName.split(','))\n            item.deptName = deptNameArr.join(',')\n          }\n        })\n        this.total = response.total\n        this.loading = false\n      })\n    },\n    handleClose(done) {\n      done()\n      this.form = {}\n      this.$refs.form.resetFields()\n    },\n    submitHandleForm() {\n      this.$refs['handleStateForm'].validate(valid => {\n        if (valid) {\n          updateAlarm(this.handleForm).then(res => {\n            this.$message.success('处置成功')\n            this.handleForm = {}\n            this.showHandleDialog = false\n            this.getList()\n          })\n        }\n      })\n    },\n    submitForm() {\n      if (this.form.threatenType != null) {\n        this.form.threatenType = this.form.threatenType.join('/')\n      }\n      this.$refs['form'].validate(valid => {\n        if (valid) {\n          if (this.form.id == null) {\n            addAlarm(this.form).then(res => {\n              this.$message.success('新增成功')\n              this.form = {}\n              this.openThrenten = false\n              this.getList()\n            })\n          } else {\n            updateAlarm(this.form).then(res => {\n              this.$message.success('修改成功')\n              this.form = {}\n              this.openThrenten = false\n              this.getList()\n            })\n          }\n        }\n      })\n    },\n    cancel() {\n      this.openThrenten = false\n      this.$refs.form.resetFields()\n    },\n    openDetail(val) {\n      this.openDialog = val\n    },\n    closeDialog() {\n      this.importDialog = false\n      this.handleQuery()\n    },\n    closeAssetDialog() {\n      this.serverOpen = false\n      this.safeOpen = false\n      this.networkOpen = false\n    },\n    closeFlow(isrRefresh) {\n      this.flowVisible = false\n      if (isrRefresh) this.getList()\n    },\n    flowTemplateSelectChange(val) {\n      this.flowTemplateSelectVisible = false\n      this.flowVisible = true\n      this.currentFlowData.flowId = val\n      this.$nextTick(() => {\n        this.$refs.FlowBox.init(this.currentFlowData)\n      })\n    },\n    handleAtcAgeClick(atcAge) {\n      this.queryParams.attackSeg = atcAge\n      this.handleQuery()\n    },\n    handlePropsQuery(val) {\n      if (val && Object.keys(val).length > 0) {\n        if (val.attackSeg && this.$refs.atcAge) {\n          this.$refs.atcAge.currentSelectedCard = val.attackSeg\n        }\n        this.queryParams = val\n        if (val.startTime && val.endTime) {\n          this.rangeTime = [val.startTime, val.endTime]\n        }\n        if (val.handle == '1') {\n          this.queryParams.handleState = '1'\n        } else if (val.handle == '0') {\n          this.queryParams.handleState = '0'\n        }\n        if (val.datasource) {\n          this.queryParams.dataSource = parseInt(val.datasource)\n        }\n        this.getThreatenDict()\n        this.getDeptsData()\n        this.handleQuery()\n      }\n    },\n    handleApplicationTagShow(applicationList) {\n      if (!applicationList || applicationList.length < 1) {\n        return ''\n      }\n      let result = applicationList[0].assetName\n      if (applicationList.length > 1) {\n        result += '...'\n      }\n      return result\n    },\n\n    handleBlocking() {\n      if (this.multipleSelection.length < 1) return this.$message.warning('请选择要阻断的ip')\n      this.blockingDialogVisible = true\n      let arr = this.multipleSelection.map(item => item.srcIp)\n      arr = Array.from(new Set(arr))\n      this.$set(this.blockingForm, 'block_ip', arr.join(';'))\n    },\n    blockingSubmit() {\n      this.$refs['blockingForm'].validate(valid => {\n        if (valid) {\n          addBlockIp(this.blockingForm).then(res => {\n            this.$message.success('添加成功')\n          }).finally(() => {\n            this.blockingDialogVisible = false\n            this.$refs.multipleTable.clearSelection()\n            this.multipleSelection = []\n          })\n        }\n      })\n    },\n    getDeviceConfigList() {\n      listDeviceConfig({ queryAllData: true }).then(res => {\n        this.deviceConfigList = res.rows\n      })\n    },\n\n    // 刷新攻击方向\n    handleRefreshAttackDirection(row, event) {\n      // 阻止事件冒泡，避免触发行点击事件\n      if (event) {\n        event.stopPropagation()\n      }\n\n      // 防止重复点击\n      if (row.refreshing) {\n        return\n      }\n\n      this.$set(row, 'refreshing', true)\n\n      refreshAttackDirection([row.id]).then(response => {\n        if (response.code === 200 && response.data && response.data.length > 0) {\n          // 更新当前行的攻击方向\n          const refreshResult = response.data[0]\n          this.$set(row, 'attackDirection', refreshResult.attackDirection)\n          this.$message.success('攻击方向刷新成功')\n        } else {\n          this.$message.error('攻击方向刷新失败')\n        }\n      }).catch(error => {\n        console.error('刷新攻击方向失败:', error)\n        this.$message.error('攻击方向刷新失败：' + (error.message || '未知错误'))\n      }).finally(() => {\n        this.$set(row, 'refreshing', false)\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.el-divider {\n  background: #0E94EA;\n}\n\n.el-divider--vertical {\n  display: inline-block;\n  width: 5px;\n  height: 2em;\n  margin: 0 8px 0 0;\n  vertical-align: middle;\n  position: relative;\n}\n\n.my-title {\n  display: inline-block;\n  vertical-align: center;\n}\n\n.asset-tag {\n  margin-left: 5px;\n}\n\n.asset-tag {\n  max-width: 100%;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n}\n\n.el-tooltip__popper {\n  font-size: 12px;\n  max-width: 300px;\n}\n\n.overflow-tag:not(:first-child) {\n  margin-top: 5px;\n}\n.blocking-form {\n  ::v-deep .el-form-item__label {\n    float: none;\n  }\n}\n\n::v-deep .el-tabs__content {\n  overflow: hidden;\n}\n::v-deep .el-dialog__body {\n  max-height: 80vh;\n  overflow-y: auto;\n}\n</style>\n"]}]}