package com.ruoyi.safe.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.monitor2.domain.TblAssetsType;
import com.ruoyi.monitor2.domain.TblAssetsTypeRel;
import com.ruoyi.monitor2.mapper.TblAssetsTypeMapper;
import com.ruoyi.monitor2.service.ITblAssetsTypeRelService;
import com.ruoyi.safe.domain.AssetClass;
import com.ruoyi.safe.domain.AssetClassEnum;
import com.ruoyi.safe.domain.DictVo;
import com.ruoyi.safe.domain.TblAssetOverview;
import com.ruoyi.safe.echart.EChartAssemble;
import com.ruoyi.safe.echart.EChartService;
import com.ruoyi.safe.mapper.AssetTypeMapper;
import com.ruoyi.safe.mapper.SQLExecuteMapper;
import com.ruoyi.safe.mapper.TblAssetOverviewMapper;
import com.ruoyi.safe.service.*;
import com.ruoyi.safe.vo.AssetClassVo;
import com.ruoyi.safe.vo.AssetPropertyVO;
import com.ruoyi.safe.vo.AssetTypeVo;
import com.ruoyi.safe.vo.chart_vo.AssetChartVo;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.service.ISysMenuService;
import com.ruoyi.system.service.ISysRoleService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 资产总表Controller
 *
 * <AUTHOR>
 * @date 2022-11-07
 */
@RestController
@RequestMapping("/safe/overview")
public class TblAssetOverviewController extends BaseController {
    @Autowired
    private ITblAssetOverviewService tblAssetOverviewService;
    @Autowired
    private ITblOutputService tblOutputService;
    @Autowired
    private ITblManagementService tblManagementService;
    @Autowired
    private ITblNetworkIpMacService tblNetworkIpMacService;
    @Autowired
    private ITblMapperService tblMapperService;
    @Autowired
    private ITblComponentService tblComponentService;
    @Autowired
    private ITblDeployService tblDeployService;
    @Autowired
    private ITblVendorService tblVendorService;

    @Autowired
    private AssetTypeMapper assetTypeMapper;
    @Autowired
    private TblAssetsTypeMapper tblAssetsTypeMapper;
    @Autowired
    private ITblAssetsTypeRelService tblAssetsTypeRelService;
    @Autowired
    private ISysMenuService sysMenuService;
    @Autowired
    private SysDictDataMapper sysDictDataMapper;
    @Autowired
    private SQLExecuteMapper sqlExecuteMapper;
    @Autowired
    private EChartAssemble eChartAssemble;
    @Autowired
    private EChartService eChartService;
    @Autowired
    private TblAssetOverviewMapper tblAssetOverviewMapper;
    @Autowired
    private Snowflake snowflake;


    Map<Long, Long> routerMap = new HashMap<>();

    {
        routerMap.put(7L, 2007L);
        routerMap.put(18L, 2092L);
        routerMap.put(17L, 2050L);
        routerMap.put(15L, 2102L);
        routerMap.put(16L, 2072L);
        routerMap.put(3L, 2066L);
        routerMap.put(9L, 2051L);
        routerMap.put(14L, 2095L);
        routerMap.put(8L, 2040L);
        routerMap.put(4L, 2019L);
        routerMap.put(1L, 2076L);
        routerMap.put(6L, 2053L);
        routerMap.put(5L, 2074L);
        routerMap.put(2L, 2082L);
    }

    /**
     * 雪花id预获取
     */
    @GetMapping("/addId")
    public AjaxResult getAddId() {
        return AjaxResult.success(snowflake.nextId());
    }

    /**
     * 查询资产总表列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TblAssetOverview tblAssetOverview) {
        startPage();
        List<TblAssetOverview> list = tblAssetOverviewService.selectTblAssetOverviewList(tblAssetOverview);
        return getDataTable(list);
    }

    @GetMapping("/listAll")
    public AjaxResult listAll(TblAssetOverview tblAssetOverview){
        List<TblAssetOverview> list = tblAssetOverviewService.selectTblAssetOverviewList(tblAssetOverview);
        return AjaxResult.success(list);
    }

    /**
     * 根据多维查询资产总表列表
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody TblAssetOverview tblAssetOverview, @RequestBody TblAssetsType tblAssetsType) {
        if (StringUtils.isNull(tblAssetsType.getTcd()) || tblAssetsType.getTcd() == 1) {
            startPage();
            return getDataTable(tblAssetOverviewService.selectTblAssetOverviewList(tblAssetOverview));
        }
        List<TblAssetsType> tblAssetsTypes = tblAssetsTypeMapper.selectChildrenTypeByTcd(tblAssetsType.getTcd());
        Set<Long> tcds = new HashSet<>();
        for (TblAssetsType type : tblAssetsTypes) {
            tcds.add(type.getTcd());
        }
        tcds.add(tblAssetsType.getTcd());
        List<TblAssetsTypeRel> tblAssetsTypeRels = tblAssetsTypeRelService.selectTblAssetsTypeRelByTcds(Arrays.asList(tcds.toArray()));
        Set<Long> assetIds = new HashSet<>();
        for (TblAssetsTypeRel tblAssetsTypeRel : tblAssetsTypeRels) {
            assetIds.add(tblAssetsTypeRel.getAssetId());
        }
        if (assetIds.size() == 0) {
            return getDataTable(new Page<>());
        }
        startPage();
        List<TblAssetOverview> tblAssetOverviews = tblAssetOverviewService.selectTblAssetOverviewByAssetIdsAndAssetOverView(Arrays.asList(assetIds.toArray()), tblAssetOverview);
        return getDataTable(tblAssetOverviews);
    }

    /**
     * 查询asset_calss s的资产
     */
    @PostMapping("/listByAssetClass")
    public TableDataInfo list(@RequestBody AssetClassVo assetClassVo) {
        //手动分页
        Integer pageNum = 1;
        Integer pageSize = 10;
        if (assetClassVo.getPageNum() != null) {
            pageNum = assetClassVo.getPageNum();
        }
        if (assetClassVo.getPageSize() != null) {
            pageSize = assetClassVo.getPageSize();
        }
        PageHelper.startPage(pageNum, pageSize);
        List<TblAssetOverview> list = tblAssetOverviewService.selectTblAssetOverviewAssetClass(assetClassVo.getAssetClass());
        return getDataTable(list);
    }

    @PostMapping("/listAllByAssetClass")
    public AjaxResult listAllByAssetClass(@RequestBody AssetClassVo assetClassVo) {
        return AjaxResult.success(tblAssetOverviewService.selectTblAssetOverviewAssetClass(assetClassVo.getAssetClass()));
    }

    /**
     * 查询资产总表，过滤虚拟系统已添加资产
     */
    @PostMapping("/list/system")
    public TableDataInfo listByBroadSystem(@RequestBody AssetClassVo assetClassVo) {
        //手动分页
        Integer pageNum = 1;
        Integer pageSize = 10;
        if (assetClassVo.getPageNum() != null) {
            pageNum = assetClassVo.getPageNum();
        }
        if (assetClassVo.getPageSize() != null) {
            pageSize = assetClassVo.getPageSize();
        }
        PageHelper.startPage(pageNum, pageSize);
        List<TblAssetOverview> list = tblAssetOverviewService.selectTblAssetOverviewAndBroadSystem(assetClassVo);
        return getDataTable(list);
    }

    /**
     * 查询资产总表，过滤区域边界已添加资产
     */
    @PostMapping("/list/boundary")
    public TableDataInfo listByBoundary(@RequestBody AssetClassVo assetClassVo) {
        //手动分页
        Integer pageNum = 1;
        Integer pageSize = 10;
        if (assetClassVo.getPageNum() != null) {
            pageNum = assetClassVo.getPageNum();
        }
        if (assetClassVo.getPageSize() != null) {
            pageSize = assetClassVo.getPageSize();
        }
        PageHelper.startPage(pageNum, pageSize);
        List<TblAssetOverview> list = tblAssetOverviewService.selectTblAssetOverviewAndBoundary(assetClassVo);
        return getDataTable(list);
    }

    @DataScope(deptAlias = "a", userAlias = "a")
    @PostMapping("/list/systemByType")
    public TableDataInfo listBySystemAndType(@RequestBody AssetTypeVo assetTypeVo) {
        //手动分页
        Integer pageNum = 1;
        Integer pageSize = 10;
        if (assetTypeVo.getPageNum() != null) {
            pageNum = assetTypeVo.getPageNum();
        }
        if (assetTypeVo.getPageSize() != null) {
            pageSize = assetTypeVo.getPageSize();
        }
        PageHelper.startPage(pageNum, pageSize);
        List<TblAssetOverview> list = tblAssetOverviewService.selectAssetsBySysAndType(assetTypeVo);
        return getDataTable(list);
    }

    @PostMapping("/count")
    public AjaxResult count() {
        AjaxResult success = AjaxResult.success();
        Map<String, Object> assetMap = new LinkedHashMap<>();
        TblAssetOverview tblAssetOverview = new TblAssetOverview();
        assetMap.put("资产总数", tblAssetOverviewService.selectTblAssetOverviewCount(tblAssetOverview));
        for (AssetClassEnum assetClassEnum : AssetClassEnum.values()) {
            if (assetClassEnum.getId() == 0) {
                continue;
            }
            tblAssetOverview.setAssetClass(assetClassEnum.getId());
            assetMap.put(assetClassEnum.getName(), tblAssetOverviewService.selectTblAssetOverviewCount(tblAssetOverview));
        }
        success.put("count", assetMap);
        return success;
    }

    /**
     * 导出资产总表列表
     */
    @PreAuthorize("@ss.hasPermi('safe:overview:export')")
    @Log(title = "资产总表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TblAssetOverview tblAssetOverview) {
        List<TblAssetOverview> list = tblAssetOverviewService.selectTblAssetOverviewList(tblAssetOverview);
        ExcelUtil<TblAssetOverview> util = new ExcelUtil<TblAssetOverview>(TblAssetOverview.class);
        util.exportExcel(response, list, "资产总表数据");
    }

    /**
     * 获取资产总表详细信息
     */
//    @PreAuthorize("@ss.hasPermi('safe:overview:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(tblAssetOverviewService.selectTblAssetOverviewByAssetId(id));
    }

    /**
     * 新增资产总表
     */
    @PreAuthorize("@ss.hasPermi('safe:overview:add')")
    @Log(title = "资产总表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblAssetOverview tblAssetOverview) {
        return toAjax(tblAssetOverviewService.insertTblAssetOverview(tblAssetOverview));
    }

    /**
     * 修改资产总表
     */
    @PreAuthorize("@ss.hasPermi('safe:overview:edit')")
    @Log(title = "资产总表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblAssetOverview tblAssetOverview) {
        return toAjax(tblAssetOverviewService.updateTblAssetOverview(tblAssetOverview));
    }

    /**
     * 删除资产总表
     */
    @PreAuthorize("@ss.hasPermi('safe:overview:remove')")
    @Log(title = "资产总表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tblAssetOverviewService.deleteTblAssetOverviewByIds(ids));
    }

    /**
     * 根据assetClass 和 assetName 查询资产
     */
    @GetMapping("/assetInfoByClassAndAssetName")
    public TableDataInfo assetInfoByClassAndAssetName(TblAssetOverview tblAssetOverview) {
        startPage();
        return getDataTable(tblAssetOverviewService.selectAssetInfoByClassAndName(tblAssetOverview));
    }

    /**
     * 检索资产
     *
     * @param
     * @return
     */
    @GetMapping(value = "/assetRieval")
    public TableDataInfo selectTblAssetOverviewretRieval(String search, List<Long> assetClass) {
        startPage();
        List<TblAssetOverview> tblAssetOverviews = tblAssetOverviewMapper.selectTblAssetOverviewretRieval(search, assetClass);
        if (ObjectUtils.isNotEmpty(tblAssetOverviews)) {
            List<Long> assetIds = tblAssetOverviews.stream().map(asset -> asset.getAssetId()).filter(asset -> ObjectUtils.isNotEmpty(asset)).collect(Collectors.toList());
            if (ObjectUtils.isNotEmpty(assetIds)) {
                return getDataTable(tblAssetOverviewService.selectDetailsTblAssetOverview(assetIds));
            }
        }
        return getDataTable(new Page<>());
    }

    /**
     * 查询某分类
     */
    @GetMapping(value = "/assetType/{id}")
    public TableDataInfo assetTypeC(@PathVariable Long id) {
        return getDataTable(assetTypeMapper.selectedAssetTypeChildrenByIdAPid(id));
    }

    /**
     * 分类列表
     */
    @GetMapping("/assetType")
    public TableDataInfo assetType() {

        return getDataTable(assetTypeMapper.selectAssetTypeList());
    }

    @GetMapping("/assetClass")
    public TableDataInfo assetClass() {
        return getDataTable(assetTypeMapper.selectAssetClassList());
    }

    /**
     * 分类详情
     */
    @GetMapping(value = "/assetTypeDetail/{id}")
    public AjaxResult detailAssetType(@PathVariable Long id) {
        return AjaxResult.success(assetTypeMapper.selectAssetTypeById(id));
    }

    /**
     * 插入分类
     */
    @PostMapping("/assetType/add")
    public AjaxResult addAssType(@RequestBody AssetClass assetClass) {
        return toAjax(assetTypeMapper.insertAssetType(assetClass));
    }

    /**
     * 修改分类
     */
    @PostMapping("/assetType/Change")
    public AjaxResult changeAssetType(@RequestBody AssetClass assetClass) {
        return toAjax(assetTypeMapper.updataAssetType(assetClass));
    }

    /**
     * 删除分类
     */
    @GetMapping(value = "/assetType/delete/{id}")
    public AjaxResult deleteAssetType(@PathVariable Long id) {
        return toAjax(assetTypeMapper.deleteAssetType(id));
    }

    /**
     * 查询资产详细信息
     */
    @GetMapping(value = "/get/{assetId}")
    public AjaxResult getAssetInfo(@PathVariable("assetId") Long assetId) {
        TblAssetOverview result = tblAssetOverviewService.selectAssetInfo(assetId);
        if (result != null) {
            // 设置位置全名
            String locationFullName = tblAssetOverviewService.getLocationFullNameByAssetId(assetId);
            result.setLocationFullName(locationFullName);
        }
        return AjaxResult.success(result);
    }

    /**
     * 查询资产属性关系
     */
    //    @GetMapping(value = "/property/{assetId}")
    //    public AssetPropertyVO getAssetProperty(@PathVariable("assetId") Long assetId) {
    //        TblAssetOverview tblAssetOverview = tblAssetOverviewService.selectAssetInfo(assetId);
    //        Assert.notNull(tblAssetOverview, "未找到资产详细信息");
    //        AssetPropertyVO assetPropertyVO = new AssetPropertyVO();
    //        List<Object> data = new ArrayList<>();
    //        List<AssetPropertyVO.Link> links = new ArrayList<>();
    //        List<AssetPropertyVO.Categorie> categories = new ArrayList<>();
    //        AssetPropertyVO.Categorie categorie = null;
    //        AssetPropertyVO.Common common = null;
    //
    //        // JSONObject jsonObject = JSONUtil.parseObj(tblAssetOverview);
    //        // jsonObject.put("id", assetId.toString());
    //        // jsonObject.put("name", tblAssetOverview.getAssetName());
    //        // jsonObject.put("category", "tbl_asset_overview");
    //        common = new AssetPropertyVO.Common();
    //        common.setId(assetId.toString());
    //        common.setName(tblAssetOverview.getAssetName());
    //        common.setValue("资产分类：" + tblAssetOverview.getAssetClassDesc() + (StringUtils.isNotEmpty(tblAssetOverview.getAssetTypeDesc()) ? "，资产类型：" + tblAssetOverview.getAssetTypeDesc() : ""));
    //        common.setSymbolSize("50");
    //        common.setCategory(tblAssetOverview.getAssetClassDesc());
    //        common.setUrl(getRouterName(tblAssetOverview.getAssetClass()));
    //        common.getParams().put("assetId",tblAssetOverview.getAssetId());
    //        common.getParams().put("assetCode",tblAssetOverview.getAssetCode());
    //        data.add(common);
    //        categories.add(AssetPropertyVO.getCategorie(tblAssetOverview.getAssetClassDesc()));
    //
    //        if (AssetClassEnum.BUSINESSAPPLICATION.isEnum(tblAssetOverview.getAssetClass())) {
    //            TblMapper tblMapper = new TblMapper();
    //            tblMapper.setApplicationId(assetId);
    //            List<TblMapper> tblMappers = tblMapperService.selectTblMapperList(tblMapper);
    //            if (CollectionUtil.isNotEmpty(tblMappers)) {
    //                categories.add(AssetPropertyVO.getCategorie("模块"));
    //                data.add(AssetPropertyVO.getCommon("tbl_mapper-0", "模块", "30", "模块"));
    //                links.add(AssetPropertyVO.getLink("tbl_mapper-0", assetId.toString()));
    //                tblMappers.forEach(e -> {
    //                    AssetPropertyVO.Mapper mapper = new AssetPropertyVO.Mapper();
    //                    BeanUtils.copyProperties(e, mapper);
    //                    mapper.setId("tbl_mapper-" + e.getModuleId());
    //                    mapper.setName(e.getModuleName());
    //                    mapper.setSymbolSize("25");
    //                    mapper.setCategory("模块");
    //                    mapper.setUrl("Mapper");
    //                    mapper.getParams().put("moduleId",e.getModuleId());
    //                    data.add(mapper);
    //                    links.add(AssetPropertyVO.getLink("tbl_mapper-" + e.getModuleId(), "tbl_mapper-0"));
    //
    //                    // 模块-软件组成信息
    //                    TblComponent tblComponent = new TblComponent();
    //                    tblComponent.setModuleId(e.getModuleId());
    //                    List<TblComponent> tblComponents = tblComponentService.selectTblComponentList(tblComponent);
    //                    if (CollectionUtil.isNotEmpty(tblComponents)) {
    //                        data.add(AssetPropertyVO.getCommon("tbl_component-0" + e.getModuleId(), "组成", "20", "组成"));
    //                        links.add(AssetPropertyVO.getLink("tbl_component-0" + e.getModuleId(), "tbl_mapper-" + e.getModuleId()));
    //                        tblComponents.forEach(c -> {
    //                            AssetPropertyVO.Component component = new AssetPropertyVO.Component();
    //                            // BeanUtils.copyProperties(c, component);
    //                            component.setId("tbl_component-" + c.getComponentId());
    //                            component.setName(c.getProcName());
    //                            component.setSymbolSize("10");
    //                            component.setCategory("组成");
    //                            component.setUrl("ComponentSoft");
    //                            component.getParams().put("componentId",c.getComponentId());
    //                            component.getParams().put("moduleId",c.getModuleId());
    //                            data.add(component);
    //                            links.add(AssetPropertyVO.getLink("tbl_component-" + c.getComponentId(), "tbl_component-0" + e.getModuleId()));
    //                        });
    //                    }
    //
    //                    // 模块-部署信息
    //                    TblDeploy tblDeploy = new TblDeploy();
    //                    tblDeploy.setModuelId(e.getModuleId());
    //                    tblDeploy.setPrdid("default");
    //                    List<TblDeploy> tblDeploys = tblDeployService.selectTblDeployList(tblDeploy);
    //                    if (CollectionUtil.isNotEmpty(tblDeploys)) {
    //                        data.add(AssetPropertyVO.getCommon("tbl_deploy-0" + e.getModuleId(), "部署", "20", "部署"));
    //                        links.add(AssetPropertyVO.getLink("tbl_deploy-0" + e.getModuleId(), "tbl_mapper-" + e.getModuleId()));
    //                        tblDeploys.forEach(d -> {
    //                            AssetPropertyVO.Deploy deploy = new AssetPropertyVO.Deploy();
    //                            // BeanUtils.copyProperties(d, deploy);
    //                            deploy.setId("tbl_deploy-" + d.getDeployId());
    //                            deploy.setName(d.getAssetName());
    //                            deploy.setSymbolSize("10");
    //                            deploy.setCategory("部署");
    //                            deploy.setUrl("Assets");
    //                            deploy.getParams().put("assetId",d.getAssetId());
    //                            deploy.getParams().put("assetCode",tblAssetOverviewService.selectTblAssetOverviewByAssetId(d.getAssetId()).getAssetCode());
    //                            data.add(deploy);
    //                            links.add(AssetPropertyVO.getLink("tbl_deploy-" + d.getDeployId(), "tbl_deploy-0" + e.getModuleId()));
    //                        });
    //                    }
    //                });
    //                categories.add(AssetPropertyVO.getCategorie("组成"));
    //                categories.add(AssetPropertyVO.getCategorie("部署"));
    //            }
    //        }
    //
    //        // 位置信息
    //        if (tblAssetOverview.getLocationId() != null && tblAssetOverview.getLocationFullName() != null) {
    //            categories.add(AssetPropertyVO.getCategorie("位置信息"));
    //            data.add(AssetPropertyVO.getCommon("tbl_location-0", "位置信息", "30", "位置信息"));
    //            links.add(AssetPropertyVO.getLink("tbl_location-0", assetId.toString()));
    //            data.add(AssetPropertyVO.getCommon("tbl_location-" + tblAssetOverview.getLocationId(), tblAssetOverview.getLocationFullName(), "20", "位置信息"));
    //            links.add(AssetPropertyVO.getLink("tbl_location-" + tblAssetOverview.getLocationId(), "tbl_location-0"));
    //        }
    //
    //        // 供应商信息
    //        if (StringUtils.isNotEmpty(tblAssetOverview.getVendor())) {
    //            TblVendor tblVendor = tblVendorService.selectTblVendorById(Long.valueOf(tblAssetOverview.getVendor()));
    //            if (tblVendor != null) {
    //                categories.add(AssetPropertyVO.getCategorie("供应商"));
    //                data.add(AssetPropertyVO.getCommon("tbl_vendor-0", "供应商", "30", "供应商"));
    //                links.add(AssetPropertyVO.getLink("tbl_vendor-0", assetId.toString()));
    //                String value = "联系人：" + tblVendor.getVendorManageName() + "，联系电话：" + tblVendor.getVendorPhone();
    //                data.add(AssetPropertyVO.getCommon("tbl_vendor-" + tblVendor.getId(), tblVendor.getVendorName(), value, "20", "供应商"));
    //                links.add(AssetPropertyVO.getLink("tbl_vendor-" + tblVendor.getId(), "tbl_vendor-0"));
    //            }
    //        }
    //
    //        // 输出信息
    //        TblOutput tblOutput = new TblOutput();
    //        tblOutput.setAssetId(assetId);
    //        List<TblOutput> tblOutputs = tblOutputService.selectTblOutputList(tblOutput);
    //        if (CollectionUtil.isNotEmpty(tblOutputs)) {
    //            categories.add(AssetPropertyVO.getCategorie("输出信息"));
    //            data.add(AssetPropertyVO.getCommon("tbl_output-0", "输出", "30", "输出信息"));
    //            links.add(AssetPropertyVO.getLink("tbl_output-0", assetId.toString()));
    //            tblOutputs.forEach(e -> {
    //                AssetPropertyVO.Output output = new AssetPropertyVO.Output();
    //                // BeanUtils.copyProperties(e, output);
    //                output.setId("tbl_output-" + e.getId());
    //                output.setName(e.getOutputName());
    //                output.setSymbolSize("20");
    //                output.setCategory("输出信息");
    //                output.setValue("输出物存储设备：" + e.getOutputAssetName());
    //                output.setUrl("Output");
    //                output.getParams().put("id",e.getId());
    //                data.add(output);
    //                links.add(AssetPropertyVO.getLink("tbl_output-" + e.getId(), "tbl_output-0"));
    //            });
    //        }
    //
    //        // 管理属性
    //        TblManagement tblManagement = new TblManagement();
    //        tblManagement.setAssetId(assetId);
    //        List<TblManagement> tblManagements = tblManagementService.selectTblManagementList(tblManagement);
    //        if (CollectionUtil.isNotEmpty(tblManagements)) {
    //            categories.add(AssetPropertyVO.getCategorie("管理属性"));
    //            data.add(AssetPropertyVO.getCommon("tbl_management-0", "管理属性", "30", "管理属性"));
    //            links.add(AssetPropertyVO.getLink("tbl_management-0", assetId.toString()));
    //            tblManagements.forEach(e -> {
    //                AssetPropertyVO.Management management = new AssetPropertyVO.Management();
    //                // BeanUtils.copyProperties(e, management);
    //                management.setId("tbl_management-" + e.getId());
    //                management.setName(e.getManagerName());
    //                management.setSymbolSize("20");
    //                management.setCategory("管理属性");
    //                management.setValue("联系方式：" + e.getManagerPhone() + "，所在部门：" + e.getManagerDept());
    //                data.add(management);
    //                links.add(AssetPropertyVO.getLink("tbl_management-" + e.getId(), "tbl_management-0"));
    //            });
    //        }
    //
    //        // 网络属性
    //        TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
    //        tblNetworkIpMac.setAssetId(assetId);
    //        List<TblNetworkIpMac> tblNetworkIpMacs = tblNetworkIpMacService.selectTblNetworkIpMacList(tblNetworkIpMac);
    //        if (CollectionUtil.isNotEmpty(tblNetworkIpMacs)) {
    //            categories.add(AssetPropertyVO.getCategorie("网络属性"));
    //            data.add(AssetPropertyVO.getCommon("tbl_network_ip_mac-0", "网络属性", "30", "网络属性"));
    //            links.add(AssetPropertyVO.getLink("tbl_network_ip_mac-0", assetId.toString()));
    //            tblNetworkIpMacs.forEach(e -> {
    //                AssetPropertyVO.NetworkIpMac networkIpMac = new AssetPropertyVO.NetworkIpMac();
    //                // BeanUtils.copyProperties(e, networkIpMac);
    //                networkIpMac.setId("tbl_network_ip_mac-" + e.getId());
    //                networkIpMac.setName(e.getIpv4());
    //                networkIpMac.setSymbolSize("20");
    //                networkIpMac.setCategory("网络属性");
    //                networkIpMac.setValue("MAC地址：" + e.getMac() + "，网络区域：" + e.getDomainFullName());
    //                data.add(networkIpMac);
    //                links.add(AssetPropertyVO.getLink("tbl_network_ip_mac-" + e.getId(), "tbl_network_ip_mac-0"));
    //            });
    //        }
    //
    //        assetPropertyVO.setData(data);
    //        assetPropertyVO.setLinks(links);
    //        assetPropertyVO.setCategories(categories);
    //        return assetPropertyVO;
    //    }
    @GetMapping(value = "/property/{assetId}")
    public AjaxResult getAssetProperty(@PathVariable("assetId") String assetId, @RequestParam("dictType") String dictType) {
        return AjaxResult.success(eChartService.getAssetChart(assetId, dictType));
    }

    @PostMapping("/getAssetChartByDim1/{assetId}")
    public AssetPropertyVO getAssetChartByDim1(@PathVariable("assetId") String id, @RequestBody List<DictVo> dictList) {
        AssetPropertyVO assetPropertyVO = new AssetPropertyVO();
        List<Object> data = new ArrayList<>();
        List<AssetPropertyVO.Link> links = new ArrayList<>();
        List<AssetPropertyVO.Categorie> categories = new ArrayList<>();
        for (DictVo dictVo : dictList) {
            SysDictData dict = sysDictDataMapper.selectDictData(dictVo.getDictType(), dictVo.getDictValue());
            List<AssetPropertyVO.Common> commons = eChartAssemble.invoke(id, dict);
            if (StringUtils.isNotEmpty(commons)) {
                data.addAll(commons);
            }
        }
        assetPropertyVO.setData(data);
        assetPropertyVO.setLinks(links);
        assetPropertyVO.setCategories(categories);
        return assetPropertyVO;
    }

    @PostMapping("/getAssetChartByDim/{assetId}")
    public AjaxResult getAssetChartByDim(@PathVariable("assetId") Long assetId, @RequestBody String[] dimList) {
        AjaxResult ajaxResult = new AjaxResult();
        AssetChartVo assetChartVo = tblAssetOverviewService.getAssetChartVoByDim(assetId, dimList);
        ajaxResult.put("assetChartVo", assetChartVo);
        return ajaxResult;
    }

    public String getRouterName(Long assetClass) {
        SysMenu sysMenu = sysMenuService.selectMenuById(routerMap.get(assetClass));
        return StringUtils.capitalize(sysMenu.getPath());
    }

    /**
     * 根据ip获取资产数据
     */
    @GetMapping(value = "/getAssetInfoByIp/{ip}")
    public AjaxResult getBusinessInfo(@PathVariable("ip") String ip) {
        List<TblAssetOverview> tblAssetOverview = tblAssetOverviewService.selectTblAssetInfoByIP(ip);
        return AjaxResult.success(tblAssetOverview);
    }

}
