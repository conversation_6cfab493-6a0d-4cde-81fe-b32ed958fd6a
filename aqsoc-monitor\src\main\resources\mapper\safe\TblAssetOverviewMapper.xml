<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblAssetOverviewMapper">

    <resultMap type="com.ruoyi.safe.domain.TblAssetOverview" id="TblAssetOverviewResult">
        <result property="assetId" column="asset_id"/>
        <result property="assetCode" column="asset_code"/>
        <result property="assetName" column="asset_name"/>
        <result property="assetType" column="asset_type"/>
        <result property="assetTypeDesc" column="asset_type_desc"/>
        <result property="assetClass" column="asset_class"/>
        <result property="assetClassDesc" column="asset_class_desc"/>
        <result property="tags" column="tags"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="orgnId" column="orgn_id"/>
        <result property="locationId" column="location_id"/>
        <result property="locationDetail" column="location_detail"/>
        <result property="locationFullName" column="location_full_name"/>
        <result property="deptName" column="dept_name"/>
        <result property="domainId" column="domain_id"/>
        <result property="ip" column="ipv4"/>
        <result property="mac" column="mac"/>
        <result property="mangerName" column="manager"/>
        <result property="state" column="state"/>
        <result property="uodTime" column="uod_time"/>
        <result property="domainId" column="domain_id"/>
        <result property="domainName" column="domain_name"/>
        <result property="laseScanState" column="last_scan_state"/>
        <result property="lastScanTime" column="last_scan_time"/>
        <result property="vulnNum" column="vuln_num"/>
        <result property="vnlnUpdateTime" column="vuln_update_time"/>
        <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="selectTblAssetOverviewVo">
        select asset_id,
               asset_code,
               asset_name,
               asset_type,
               asset_type_desc,
               asset_class,
               asset_class_desc,
               tags,
               a.user_id,
               a.dept_id,
               a.orgn_id,
               a.location_id,
               a.location_detail,
               a.create_time,
               b.location_full_name,
               d.dept_name,
               a.uod_time,
               a.state,
               (select GROUP_CONCAT(e.ipv4) from tbl_network_ip_mac e where a.asset_id = e.asset_id)   as ip,
               (SELECT GROUP_CONCAT(manager_name) FROM tbl_management f where f.asset_id = a.asset_id) as manager
        from tbl_asset_overview a
                 left join sys_dept d on d.dept_id = a.dept_id
                 left join tbl_location b on a.location_id = b.location_id
    </sql>
    <sql id="selectTblAssetOverviewVo2">
        select a.asset_id,
               asset_code,
               asset_name,
               asset_type,
               asset_type_desc,
               asset_class,
               asset_class_desc,
               tags,
               a.user_id,
               a.dept_id,
               a.orgn_id,
               a.location_id,
               a.location_detail,
               b.location_full_name,
               d.dept_name,
               a.uod_time,
               a.state,
               a.create_time,
               net.domain_id,
               net.ipv4
        from tbl_asset_overview a
                 left join sys_dept d on d.dept_id = a.dept_id
                 left join tbl_location b on a.location_id = b.location_id
                 left join tbl_network_ip_mac net on net.asset_id = a.asset_id and net.main_ip = '1'
    </sql>

    <select id="selectTblAssetOverviewList" parameterType="com.ruoyi.safe.domain.TblAssetOverview" resultMap="TblAssetOverviewResult">
        <include refid="selectTblAssetOverviewVo2"/>
        <where>
            <if test="assetId != null ">and a.asset_id = #{assetId}</if>
            <if test="assetCode != null  and assetCode != ''">and a.asset_code = #{assetCode}</if>
            <if test="assetName != null  and assetName != ''">and a.asset_name like concat('%', #{assetName}, '%')</if>
            <if test="assetType != null  and assetType != ''">and a.asset_type = #{assetType}</if>
            <if test="assetTypeDesc != null  and assetTypeDesc != ''">and a.asset_type_desc = #{assetTypeDesc}</if>
            <if test="assetClass != null  and assetClass != ''">and a.asset_class = #{assetClass}</if>
            <if test="assetClassDesc != null  and assetClassDesc != ''">and a.asset_class_desc = #{assetClassDesc}</if>
            <if test="userId != null ">and a.user_id = #{userId}</if>
            <if test="deptId != null ">and (a.dept_id = #{deptId} OR find_in_set(#{deptId},d.ancestors))</if>
            <if test="orgnId != null ">and a.orgn_id = #{orgnId}</if>
            <if test="locationId != null ">and a.location_id = #{locationId}</if>
            <if test="locationDetail != null  and locationDetail != ''"> and location_detail = #{locationDetail}</if>
            <if test="state != null and state != ''">and a.state = #{state}</if>
            <if test="domainId != null and domainId">and domain_id = #{domainId}</if>
            <if test="ip != null and ip">and ipv4 like concat('%',#{ip},'%')</if>
            <if test="coverChild != null and coverChild">or find_in_set(#{locationId},b.ancestors)</if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
            <if test="startTime != null"> and a.create_time &gt;= #{startTime}</if>
            <if test="endTime != null"> and a.create_time &lt;= #{endTime}</if>
            <if test="assetClassList != null and assetClassList.size()>0">
                and a.asset_class in
                <foreach collection="assetClassList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by a.asset_id desc
    </select>

    <select id="selectTblAssetOverviewAssetClass" parameterType="java.lang.String" resultMap="TblAssetOverviewResult">
        <include refid="selectTblAssetOverviewVo2"/>
        where a.asset_class in
        <foreach collection="assetClass" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!--资产检索-->
    <select id="selectTblAssetOverviewretRieval" parameterType="java.lang.String" resultMap="TblAssetOverviewResult">
        select a.asset_id from tbl_asset_overview a
        left join tbl_network_domain net on net.asset_id = a.asset_id
        <where>
            1=1
            <if test="search!=null">a.asset_name like concat('%',#{search},'%') or net.ipv4 like
                concat('%',#{search},'%')
            </if>
            <if test="assetClass">and a.asset_class in</if>
            <foreach collection="assetClass" open="(" close=")" separator="," item="c">#{c}</foreach>
        </where>
    </select>

    <select id="selectTblAssetOverviewByAssetIdsAndAssetOverView" parameterType="java.lang.String"
            resultMap="TblAssetOverviewResult">
        <include refid="selectTblAssetOverviewVo"/>
        <where>
            <if test="assetIds != null">
                and a.asset_id in
                <foreach collection="assetIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="assetOverview.assetCode != null  and assetOverview.assetCode != ''">and asset_code =
                #{assetOverview.assetCode}
            </if>
            <if test="assetOverview.assetName != null  and assetOverview.assetName != ''">and asset_name like
                concat('%', #{assetOverview.assetName}, '%')
            </if>
            <if test="assetOverview.assetType != null  and assetOverview.assetType != ''">and asset_type =
                #{assetOverview.assetType}
            </if>
            <if test="assetOverview.assetTypeDesc != null  and assetOverview.assetTypeDesc != ''">and asset_type_desc =
                #{assetOverview.assetTypeDesc}
            </if>
            <if test="assetOverview.assetClass != null  and assetOverview.assetClass != ''">and asset_class =
                #{assetOverview.assetClass}
            </if>
            <if test="assetOverview.assetClassDesc != null  and assetOverview.assetClassDesc != ''">and asset_class_desc
                = #{assetOverview.assetClassDesc}
            </if>
            <if test="assetOverview.userId != null ">and a.user_id = #{assetOverview.userId}</if>
            <if test="assetOverview.deptId != null ">and a.dept_id = #{assetOverview.deptId}</if>
            <if test="assetOverview.orgnId != null ">and a.orgn_id = #{assetOverview.orgnId}</if>
            <if test="assetOverview.locationId != null ">and location_id = #{assetOverview.locationId}</if>
            <if test="state != null and state != ''">and a.state = #{state}</if>
        </where>
    </select>

    <select id="selectTblAssetOverviewAndBroadSystem" parameterType="java.lang.String" resultMap="TblAssetOverviewResult">
        select ao.asset_id, ao.asset_code, ao.asset_name, ao.asset_type, ao.asset_type_desc, ao.asset_class,
        ao.asset_class_desc,ao.uod_time,ao.state from tbl_asset_overview as ao
        <where>
            <if test="assetCode != null  and assetCode != ''">and asset_code = #{assetCode}</if>
            <if test="assetName != null  and assetName != ''">and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="state != null and state != ''">and ao.state = 1</if>
            <if test="assetClass != null and assetClass.length > 0">
                and ao.asset_class in
                <foreach collection="assetClass" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="systemId != null">
                and ao.asset_id not in ( select asset_id from tbl_broad_system_asset where system_id = #{systemId}
                <if test="assetClass != null and assetClass.length > 0">
                    and ao.asset_class in
                    <foreach collection="assetClass" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>
        </where>
    </select>

    <select id="selectTblAssetOverviewAndBoundary" parameterType="java.lang.String" resultMap="TblAssetOverviewResult">
    select ao.asset_id, ao.asset_code, ao.asset_name, ao.manger, ao.asset_type, ao.asset_type_desc, ao.asset_class,
        ao.asset_class_desc,ao.uod_time,ao.state,net.domain_id,net.ipv4 from tbl_asset_overview ao left join tbl_network_ip_mac net on net.asset_id = ao.asset_id
        <where>
            <if test="assetClass!=null and assetClass != ''">
                ao.asset_class in
                <foreach collection="assetClass" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="boundaryId!=null and boundaryId != ''">
                and ao.asset_id not in
                (select asset_id from tbl_zone_boundary_asset where boundary_id = #{boundaryId})
            </if>
            <if test="domainId!=null and domainId!=''">and net.domain_id = #{domainId}</if>
        </where>

    </select>

    <select id="selectInfoByAssetId" parameterType="java.lang.Long" resultMap="TblAssetOverviewResult">
        select a.asset_id,
               a.tags,
               a.location_id,
               a.location_detail,
               l.location_full_name,
               a.dept_id,
               d.dept_name,
               a.uod_time,
               a.state
        from tbl_asset_overview as a
                 left join tbl_location as l on l.location_id = a.location_id
                 left join sys_dept as d on d.dept_id = a.dept_id
        where a.asset_id = #{assetId}
    </select>

    <select id="selectAssetByIP" parameterType="java.lang.String" resultMap="TblAssetOverviewResult">
        select tao.asset_id, tao.state,tao.asset_name,tao.asset_type_desc,tao.asset_class_desc,tao.dept_id from tbl_asset_overview tao where tao.asset_id in (select asset_id from tbl_network_ip_mac where ipv4 = #{ip})
    </select>

    <select id="selectInfoByAssetIds" parameterType="java.lang.Long" resultMap="TblAssetOverviewResult">
        select a.asset_id, a.tags, a.location_id, a.location_detail, l.location_full_name, a.dept_id, d.dept_name, a.uod_time, a.state,
        n.ipv4, n.mac, n.domain_id,n.last_scan_state,n.last_scan_time,
        n.vuln_num,n.vuln_update_time
        from tbl_asset_overview as a
        left join tbl_location as l on l.location_id = a.location_id
        left join tbl_network_ip_mac as n on n.asset_id = a.asset_id and n.main_ip='1'
        left join sys_dept as d on d.dept_id = a.dept_id
        where a.asset_id in
        <foreach collection="assetIds" item="assetId" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </select>

    <select id="selectTblAssetOverviewByAssetId" parameterType="java.lang.Long" resultMap="TblAssetOverviewResult">
        <include refid="selectTblAssetOverviewVo"/>
        where a.asset_id = #{assetId}
    </select>

    <select id="selectIsServer" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(1) from tbl_server where asset_id = #{assetId}
    </select>

    <select id="selectTblAssetOverviewByAssetIds" parameterType="java.lang.String" resultMap="TblAssetOverviewResult">
        <include refid="selectTblAssetOverviewVo"/>
        where a.asset_id in
        <foreach collection="assetIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectTblAssetOverviewCount" parameterType="com.ruoyi.safe.domain.TblAssetOverview" resultType="java.lang.Integer">
        select count(*) from tbl_asset_overview as a
        <where>
            <if test="assetCode != null  and assetCode != ''">and a.asset_code = #{assetCode}</if>
            <if test="assetName != null  and assetName != ''">and a.asset_name like concat('%', #{assetName}, '%')</if>
            <if test="assetType != null  and assetType != ''">and a.asset_type = #{assetType}</if>
            <if test="assetClass != null  and assetClass != ''">and a.asset_class = #{assetClass}</if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
            <if test="state != null and state != ''">and a.state = #{state}</if>
        </where>
    </select>

    <select id="countByClass" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(asset_id) from tbl_asset_overview
        <where>
            <if test="classid != null">asset_class = #{classid}</if>
            <if test="state != null and state != ''">and a.state = #{state}</if>
        </where>
    </select>

    <select id="checkAssetCodeUnique" parameterType="java.lang.String" resultMap="TblAssetOverviewResult">
        select asset_id, asset_code from tbl_asset_overview where asset_code = #{assetCode} limit 1
    </select>

    <select id="checkBatchAssetCodesExist" resultType="java.lang.String">
        select asset_code from tbl_asset_overview
        where asset_code in
        <foreach collection="assetCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <insert id="insertTblAssetOverview" parameterType="com.ruoyi.safe.domain.TblAssetOverview">
        insert into tbl_asset_overview
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="assetCode != null">asset_code,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="assetType != null">asset_type,</if>
            <if test="assetTypeDesc != null">asset_type_desc,</if>
            <if test="assetClass != null">asset_class,</if>
            <if test="assetClassDesc != null">asset_class_desc,</if>
            <if test="tags != null">tags,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="orgnId != null">orgn_id,</if>
            <if test="locationId != null">location_id,</if>
            <if test="locationDetail != null">location_detail,</if>
            <if test="state != null">state,</if>
            <if test="uodTime != null">uod_time,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="assetCode != null">#{assetCode},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="assetType != null">#{assetType},</if>
            <if test="assetTypeDesc != null">#{assetTypeDesc},</if>
            <if test="assetClass != null">#{assetClass},</if>
            <if test="assetClassDesc != null">#{assetClassDesc},</if>
            <if test="tags != null">#{tags},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="orgnId != null">#{orgnId},</if>
            <if test="locationId != null">#{locationId},</if>
            <if test="locationDetail != null">#{locationDetail},</if>
            <if test="state != null">#{state},</if>
            <if test="uodTime != null">#{uodTime},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateTblAssetOverview" parameterType="com.ruoyi.safe.domain.TblAssetOverview">
        update tbl_asset_overview
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetCode != null">asset_code = #{assetCode},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="assetType != null">asset_type = #{assetType},</if>
            <if test="assetTypeDesc != null">asset_type_desc = #{assetTypeDesc},</if>
            <if test="assetClass != null">asset_class = #{assetClass},</if>
            <if test="assetClassDesc != null">asset_class_desc = #{assetClassDesc},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="orgnId != null">orgn_id = #{orgnId},</if>
            <if test="locationId != null">location_id = #{locationId},</if>
            <if test="locationDetail != null">location_detail = #{locationDetail},</if>
            <if test="state != null">`state` = #{state},</if>
            <if test="threatSum != null">threat_sum = #{threatSum},</if>
            <if test="uodTime != null">uod_time = #{uodTime}</if>
        </trim>
        where asset_id = #{assetId}
    </update>

    <delete id="deleteTblAssetOverviewById" parameterType="java.lang.Long">
        delete from tbl_asset_overview where asset_id = #{assetId}
    </delete>

    <delete id="deleteTblAssetOverviewByIds" parameterType="java.lang.String">
        delete from tbl_asset_overview where asset_id in
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>

    <select id="selectAssetsBySysAndType" parameterType="map" resultMap="TblAssetOverviewResult">
        select a.asset_id, a.asset_code, a.asset_name, a.asset_type, a.asset_type_desc,a.uod_time,a.state,
        a.asset_class, a.asset_class_desc
        ,c.dept_name,(select GROUP_CONCAT(e.ipv4) from tbl_network_ip_mac e where a.asset_id=e.asset_id) as ip
        ,(SELECT GROUP_CONCAT(manager_name) FROM tbl_management f where f.asset_id=a.asset_id) as manager
        from tbl_asset_overview a
        left join tbl_asset_class b on a.asset_type = b.id
        left join sys_dept c on a.dept_id = c.dept_id
        where (b.id= #{assetType} or b.pid= #{assetType})
        and a.asset_id not in
        (select a.asset_id from tbl_broad_system_asset a
        left join tbl_asset_class b on a.asset_type = b.id
        where system_id = #{systemId} and (b.id= #{assetType} or b.pid=#{assetType})
        )
        <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
    </select>
    <select id="getAssetTypeStatistics" resultType="java.util.HashMap">
        SELECT
            case
                WHEN asset_class = 4 THEN '服务器'
                WHEN asset_class = 3 THEN '安全设备'
                WHEN asset_class = 2 THEN '网络设备'
                WHEN asset_class = 7 THEN '业务系统' END name,
            count( asset_class_desc ) AS value
        FROM
            tbl_asset_overview
        WHERE
            asset_class IN ( 7, 2, 3, 4 )
        GROUP BY
            asset_class
        ORDER BY
            asset_class DESC
    </select>

    <select id="getAssetRiskThreats" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            SUM( totalData.loopholeNum ) AS totalLoopholeNum,
            SUM( totalData.`NONE` ) AS totalNone,
            SUM( totalData.`LOW` ) AS totalLow,
            SUM( totalData.`MEDIUM` ) AS totalMedium,
            SUM( totalData.`HIGH` ) AS totalHtgh
        FROM
            (
                SELECT
                    COUNT( vd.id ) loopholeNum,
                    SUM(
                            IF
                            ( vd.severity = 1, 1, 0 )) `NONE`,
                    SUM(
                            IF
                            ( vd.severity = 2, 1, 0 )) `LOW`,
                    SUM(
                            IF
                            ( vd.severity = 3, 1, 0 )) `MEDIUM`,
                    SUM(
                            IF
                            ( vd.severity = 4, 1, 0 )) `HIGH`
                FROM
                    monitor_bss_vuln_deal vd
                        LEFT JOIN tbl_work_order wo ON FIND_IN_SET( vd.id, wo.event_ids )
                        AND wo.work_type = '1'
                        LEFT JOIN tbl_work_history wh ON wh.work_id = wo.id
                        AND wh.id = ( SELECT twh.id FROM tbl_work_history twh WHERE twh.work_id = wo.id ORDER BY twh.create_time DESC LIMIT 1 )
                LEFT JOIN tbl_network_ip_mac im ON vd.host_ip = im.ipv4
                LEFT JOIN tbl_asset_overview t2 ON t2.asset_id = im.asset_id
                LEFT JOIN sys_dept sd ON sd.dept_id = t2.dept_id
        WHERE
            vd.handle_state=0 and vd.severity != 0 and sd.dept_id = #{deptId}
           OR sd.dept_id IN (
            SELECT
                t.dept_id
            FROM
                sys_dept t
            WHERE
                FIND_IN_SET( #{deptId}, t.ancestors )) UNION ALL
        SELECT
            COUNT( wvd.id ) loopholeNum,
            SUM(
                    IF
                    ( wvd.severity = 1, 1, 0 )) `NONE`,
            SUM(
                    IF
                    ( wvd.severity = 2, 1, 0 )) `LOW`,
            SUM(
                    IF
                    ( wvd.severity = 3, 1, 0 )) `MEDIUM`,
            SUM(
                    IF
                    ( wvd.severity = 4, 1, 0 )) `HIGH`
        FROM
            monitor_bss_webvuln_deal wvd
                LEFT JOIN tbl_work_order wo ON FIND_IN_SET( wvd.id, wo.event_ids )
                AND wo.work_type = '2'
                LEFT JOIN tbl_work_history wh ON wh.work_id = wo.id
                AND wh.id = ( SELECT twh.id FROM tbl_work_history twh WHERE twh.work_id = wo.id ORDER BY twh.create_time DESC LIMIT 1 )
            LEFT JOIN tbl_business_application tba ON wvd.web_url = tba.url
            LEFT JOIN sys_dept td ON tba.dept_id = td.dept_id
        WHERE
            wvd.handle_state=0 and wvd.severity != 0 and td.dept_id = #{deptId}
           OR td.dept_id IN (
            SELECT
            t.dept_id
            FROM
            sys_dept t
            WHERE
            FIND_IN_SET( #{deptId}, t.ancestors ))
            ) AS totalData
    </select>

    <!-- 根据资产ID获取位置全名 -->
    <select id="selectLocationFullNameByAssetId" parameterType="java.lang.Long" resultType="java.lang.String">
        SELECT GROUP_CONCAT(l.location_full_name SEPARATOR ';') AS location_full_names
        FROM tbl_location l
        WHERE FIND_IN_SET(l.location_id, (
            SELECT location_id FROM tbl_asset_overview WHERE asset_id = #{assetId}
        ))
        AND l.location_full_name IS NOT NULL
        AND l.location_full_name != ''
    </select>
</mapper>
