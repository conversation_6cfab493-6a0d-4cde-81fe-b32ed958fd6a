package com.ruoyi.safe.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.exception.Asset.AssetDeleteException;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.CollectionUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtil;
import com.ruoyi.dict.domain.NetworkDomain;
import com.ruoyi.dict.service.INetworkDomainService;
import com.ruoyi.safe.domain.*;
import com.ruoyi.safe.echart.EChartAssemble;
import com.ruoyi.safe.mapper.AssetTypeMapper;
import com.ruoyi.safe.mapper.TblAssetOverviewMapper;
import com.ruoyi.safe.mapper.TblNetworkIpMacMapper;
import com.ruoyi.safe.service.*;
import com.ruoyi.safe.vo.AssetClassVo;
import com.ruoyi.safe.vo.AssetPropertyVO;
import com.ruoyi.safe.vo.AssetTypeVo;
import com.ruoyi.safe.vo.chart_vo.AssetChartVo;
import com.ruoyi.safe.vo.chart_vo.Category;
import com.ruoyi.safe.vo.chart_vo.Link;
import com.ruoyi.safe.vo.chart_vo.Node;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 资产总表Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-07
 */
@Service
public class TblAssetOverviewServiceImpl implements ITblAssetOverviewService {
    @Autowired
    private TblAssetOverviewMapper tblAssetOverviewMapper;

    @Autowired
    private ITblMachroomService tblMachroomService;
    @Autowired
    private ITblNetworkDevicesService tblNetworkDevicesService;
    @Autowired
    private ITblSafetyService tblSafetyService;
    @Autowired
    private ITblServerService tblServerService;
    @Autowired
    private ITblTerminalService tblTerminalService;
    @Autowired
    private ITblSystemSoftwareService tblSystemSoftwareService;
    @Autowired
    private ITblBusinessApplicationService tblBusinessApplicationService;
    @Autowired
    private ITblDatabaseService tblDatabaseService;
    @Autowired
    private ITblDataResourceService tblDataResourceService;
    @Autowired
    private ITblPasswordProductService tblPasswordProductService;
    @Autowired
    private ITblManageService tblManageService;
    @Autowired
    private ITblZoneBoundaryService tblZoneBoundaryService;
    @Autowired
    private ITblOutputService tblOutputService;
    @Autowired
    private ITblManagementService tblManagementService;
    @Autowired
    private ITblNetworkIpMacService tblNetworkIpMacService;
    @Autowired
    private ITblMapperService tblMapperService;
    @Autowired
    private ITblComponentService tblComponentService;
    @Autowired
    private ITblDeployService tblDeployService;
    @Autowired
    private ITblVendorService tblVendorService;
    @Autowired
    private Snowflake snowflake;
    @Autowired
    private EChartAssemble eChartAssemble;
    @Autowired
    private IDictionaryService dictionaryService;

    @Autowired
    private INetworkDomainService networkDomainService;
    @Autowired
    private AssetTypeMapper tblAssetsTypeMapper;
    @Autowired
    private TblNetworkIpMacMapper tblNetworkIpMacMapper;
    @Autowired
    private ITblApplicationServerService tblApplicationServerService;

    @Autowired
    private ITblApplicationSafeService tblApplicationSafeService;
    @Autowired
    private ITblApplicationNetwareService tblApplicationNetwareService;
    /**
     * 查询资产总表
     *
     * @param id 资产总表主键
     * @return 资产总表
     */
    @Override
    public Integer selectIsServer(Long id) {
        return tblAssetOverviewMapper.selectIsServer(id);
    }

    /**
     * 查询资产总表
     *
     * @param assetId 资产主键
     * @return 资产总表
     */
    @Override
    public TblAssetOverview selectTblAssetOverviewByAssetId(Long assetId) {
        return tblAssetOverviewMapper.selectTblAssetOverviewByAssetId(assetId);
    }

    /**
     * 查询资产总表
     *
     * @param assetIds 资产主键
     * @return 资产总表
     */
    @Override
    public List<TblAssetOverview> selectTblAssetOverviewByAssetIds(Long[] assetIds) {
        return tblAssetOverviewMapper.selectTblAssetOverviewByAssetIds(Arrays.stream(assetIds).collect(Collectors.toList()));
    }

    @Override
    public List<TblAssetOverview> selectTblAssetOverviewByAssetIdsAndAssetOverView(List assetIds, TblAssetOverview assetOverview) {
        return tblAssetOverviewMapper.selectTblAssetOverviewByAssetIdsAndAssetOverView(assetIds, assetOverview);
    }

    @Override
    public List<TblAssetOverview> selectAllList(TblAssetOverview tblAssetOverview) {
        return tblAssetOverviewMapper.selectTblAssetOverviewList(tblAssetOverview);
    }

    /**
     * 查询资产总表列表
     *
     * @param tblAssetOverview 资产总表
     * @return 资产总表
     */
    @Override
    @DataScope(deptAlias = "a", userAlias = "a")
    public List<TblAssetOverview> selectTblAssetOverviewList(TblAssetOverview tblAssetOverview) {
        List<TblAssetOverview> tblAssetOverviews = tblAssetOverviewMapper.selectTblAssetOverviewList(tblAssetOverview);
        packAssetOverview(tblAssetOverviews);
        return tblAssetOverviews;
    }

    /**
     * 为资产装配责任人与网络信息
     * @param tblAssetOverviews
     */
    public void packAssetOverview(List<TblAssetOverview> tblAssetOverviews){
        List<Long> assetIds = new ArrayList<>();
        List<Long> domainIds = new ArrayList<>();
        if(ObjectUtils.isNotEmpty(tblAssetOverviews))
            for (TblAssetOverview assetOverview : tblAssetOverviews) {
                assetIds.add(assetOverview.getAssetId());
                if(ObjectUtils.isNotEmpty(assetOverview.getDomainId()))
                    domainIds.add(assetOverview.getDomainId());
            }
        List<TblManagement> tblManagements  =null;
        if(ObjectUtils.isNotEmpty(assetIds)){
            tblManagements = tblManagementService.selectTblManagementByAssetIds(assetIds.toArray(new Long[]{}));
        }
        List<NetworkDomain> networkDomains = null;
        if(ObjectUtils.isNotEmpty(domainIds))
            networkDomains = networkDomainService.selectNetworkDomainByDomainIds(domainIds.toArray(new Long[0]),new NetworkDomain());
        CollectionUtils.associationOneMapMany(tblAssetOverviews,new CollectionUtils.Polymerization[]{
                CollectionUtils.getPolymerizationOneToMany(tblAssetOverviews,tblManagements,TblAssetOverview::getAssetId,TblManagement::getAssetId,(asset,manages)->{
                    String collect = manages.stream().map(TblManagement::getManagerName).collect(Collectors.joining(","));
                    asset.setMangerName(collect);
                }),
                CollectionUtils.getPolymerization(tblAssetOverviews,networkDomains,TblAssetOverview::getDomainId,NetworkDomain::getDomainId,(asset,domain)->{
                    asset.setDomainName(domain.getDomainName());
                })
        });
    }
    @Override
    public List<TblAssetOverview> selectTblAssetOverviewAssetClass(Long[] assetClass) {
        List<TblAssetOverview> tblAssetOverviews = tblAssetOverviewMapper.selectTblAssetOverviewAssetClass(assetClass);
        packAssetOverview(tblAssetOverviews);
        return tblAssetOverviews;
    }

    /**
     * 查询资产总表，过滤虚拟系统已添加资产
     *
     * @param assetClassVo 资产分类
     * @return 资产总表集合
     */
    @Override
    public List<TblAssetOverview> selectTblAssetOverviewAndBroadSystem(AssetClassVo assetClassVo) {
        List<TblAssetOverview> tblAssetOverviews = tblAssetOverviewMapper.selectTblAssetOverviewAndBroadSystem(assetClassVo);
        packAssetOverview(tblAssetOverviews);
        return tblAssetOverviews;
    }

    @Override
    public List<TblAssetOverview> selectTblAssetOverviewAndBoundary(AssetClassVo assetClassVo) {
        List<TblAssetOverview> tblAssetOverviews = tblAssetOverviewMapper.selectTblAssetOverviewAndBoundary(assetClassVo);
        packAssetOverview(tblAssetOverviews);
        return tblAssetOverviews;
    }

    @Override
    public List<TblAssetOverview> selectAssetsBySysAndType(AssetTypeVo assetTypeVo) {
        return tblAssetOverviewMapper.selectAssetsBySysAndType(assetTypeVo);
    }

    /**
     * 查询资产总数
     *
     * @param tblAssetOverview 资产总表
     * @return 资产总数
     */
    @Override
    @DataScope(deptAlias = "a", userAlias = "a")
    public int selectTblAssetOverviewCount(TblAssetOverview tblAssetOverview) {
        return tblAssetOverviewMapper.selectTblAssetOverviewCount(tblAssetOverview);
    }

    @Override
    public Integer countByClass(Long classid) {
        return tblAssetOverviewMapper.countByClass(classid);
    }

    /**
     * 检查资产编码唯一性
     *
     * @param tblAssetOverview 资产总表
     * @return true：唯一 false：不唯一
     */
    @Override
    public boolean checkAssetCodeUnique(TblAssetOverview tblAssetOverview) {
        Long assetId = StringUtils.isNull(tblAssetOverview.getAssetId()) ? -1L : tblAssetOverview.getAssetId();
        TblAssetOverview info = tblAssetOverviewMapper.checkAssetCodeUnique(tblAssetOverview.getAssetCode());
        return StringUtils.isNull(info) || info.getAssetId().equals(assetId);
    }

    /**
     * 新增资产总表
     *
     * @param tblAssetOverview 资产总表
     * @return 结果
     */
    @Override
    public int insertTblAssetOverview(TblAssetOverview tblAssetOverview) {
//        if(Arrays.asList(2L,3L,4L,5L).contains(tblAssetOverview.getAssetClass())){
//            TblNetworkIpMac tblNetworkIpMac = BeanUtil.copyProperties(tblAssetOverview, TblNetworkIpMac.class);
//            tblNetworkIpMac.setIpv4(tblAssetOverview.getIp());
//            tblNetworkIpMac.setMainIp("1");
//            TblNetworkIpMac checkIP = new TblNetworkIpMac();
//            checkIP.setMainIp("1");
//            checkIP.setDomainId(tblNetworkIpMac.getDomainId());
//            checkIP.setIpv4(tblNetworkIpMac.getIpv4());
//            if(checkIp(checkIP, Arrays.asList(tblAssetOverview.getAssetId())))
//            tblNetworkIpMac.setIpv6("1");
//                tblNetworkIpMacService.insertTblNetworkIpMac(tblNetworkIpMac);
//        }
        if (ObjectUtils.isEmpty(tblAssetOverview.getAssetClassDesc())) {
            AssetClass assetClass = tblAssetsTypeMapper.selectAssetTypeById(tblAssetOverview.getAssetClass());
            tblAssetOverview.setAssetClassDesc(assetClass.getTypeName());
        }
        if (ObjectUtils.isEmpty(tblAssetOverview.getAssetName()) || tblAssetOverview.getAssetName() == null) {
            tblAssetOverview.setAssetName("业务基本信息");
        }
        // if (null == tblAssetOverview.getState()) tblAssetOverview.setState("1");
        // if (null == tblAssetOverview.getUodTime()) tblAssetOverview.setUodTime(DateUtils.getNowDate());
        if(tblAssetOverview.getCreateTime() == null){
            tblAssetOverview.setCreateTime(DateUtil.date());
        }
        return tblAssetOverviewMapper.insertTblAssetOverview(tblAssetOverview);
    }

    /**
     * 检查ip是否唯一
     * @param checkIp
     * @param exAssetIds  排除资产id
     */
    private boolean checkIp(TblNetworkIpMac checkIp,List<Long> exAssetIds){
        if(ObjectUtils.isNotEmpty(checkIp.getIpv4())){
            //是否属于此网段
            if(ObjectUtils.isNotEmpty(checkIp.getDomainId())){
                NetworkDomain networkDomain = networkDomainService.selectNetworkDomainByDomainId(checkIp.getDomainId());
                String segIp = networkDomain.getIparea();//网段
                if(ObjectUtils.isNotEmpty(segIp)){
                    int ip_mask = 3;//子网掩码  默认掩码
                    String[] ip_seg = segIp.split("/");
                    ip_mask = Integer.parseInt(ip_seg[1]) / 8;
                    String[] assetIP = checkIp.getIpv4().split("\\.");
                    ip_seg = ip_seg[0].split("\\.");
                    for(int i=0;i<ip_mask;i++){
                        if (!ip_seg[i].equals(assetIP[i])) {
                            throw new ServiceException("IP:"+checkIp.getIpv4()+" 不属于当前网络区域的网段("+networkDomain.getIparea()+")划分！");
                        }
                    }
                }
                List<TblNetworkIpMac> networkIp = tblNetworkIpMacService.selectTblNetworkIpMacList(checkIp);
                if(ObjectUtils.isNotEmpty(networkIp)){
                    TblAssetOverview asset = tblAssetOverviewMapper.selectTblAssetOverviewByAssetId(networkIp.get(0).getAssetId());
                    if(ObjectUtils.isNotEmpty(asset)&&!exAssetIds.contains(asset.getAssetId())){
                        throw new ServiceException("网络区域["+networkDomain.getDomainName()+"]中，ip："+checkIp.getIpv4()+" 已被["+asset.getAssetName()+"]占用！");
                    }
                }
            }else {
                throw new ServiceException("请先选择网络区域！");
            }
            return true;
        }
        return false;
    }

    /**
     * 修改资产总表
     *
     * @param tblAssetOverview 资产总表
     * @return 结果
     */
    @Override
    public int updateTblAssetOverview(TblAssetOverview tblAssetOverview) {
//        if(Arrays.asList(2L,3L,4L,5L).contains(tblAssetOverview.getAssetClass())){
//            TblNetworkIpMac tblNetworkIpMac = BeanUtil.copyProperties(tblAssetOverview, TblNetworkIpMac.class);
//            tblNetworkIpMac.setIpv4(tblAssetOverview.getIp());
//            tblNetworkIpMac.setIpv6("1");
//            TblNetworkIpMac checkIP = new TblNetworkIpMac();
//            checkIP.setMainIp("1");
//            checkIP.setDomainId(tblNetworkIpMac.getDomainId());
//            checkIP.setIpv4(tblNetworkIpMac.getIpv4());
//            if(checkIp(checkIP, Arrays.asList(tblAssetOverview.getAssetId()))){
//                tblNetworkIpMacService.deleteTblNetworkIpMacByAssetId(tblAssetOverview.getAssetId());
//                tblNetworkIpMacService.insertTblNetworkIpMac(tblNetworkIpMac);
//            }
//        }
//        if (tblAssetOverview.getAssetType() == null) {
//            tblAssetOverview.setAssetType(tblAssetOverview.getAssetClass());
//            tblAssetOverview.setAssetTypeDesc(tblAssetOverview.getAssetClassDesc());
//        }
        return tblAssetOverviewMapper.updateTblAssetOverview(tblAssetOverview);
    }

    /**
     * 删除资产总表信息
     *
     * @param assetId 资产总表主键
     * @return 结果
     */
    @Override
    public int deleteTblAssetOverviewById(Long assetId) {
        checkUse(assetId);
        tblManagementService.deleteTblManagementById(assetId);
        tblNetworkIpMacMapper.deleteTblNetworkIpMacByAssetId(assetId);
        return tblAssetOverviewMapper.deleteTblAssetOverviewById(assetId);
    }

    /**
     * 批量删除资产总表
     *
     * @param assetIds 需要删除的资产总表主键
     * @return 结果
     */
    @Override
    public int deleteTblAssetOverviewByIds(Long[] assetIds) {
        for (Long id : assetIds) {
            checkUse(id);
        }
        tblManagementService.deleteTblManagementByIds(assetIds);
        tblNetworkIpMacMapper.deleteTblNetworkIpMacByAssetIds(assetIds);
        for (Long assetId : assetIds) {
            TblApplicationServer tblApplication = new TblApplicationServer();
            tblApplication.setServerId(assetId);
            tblApplicationServerService.deleteTblApplicationServer(tblApplication);
            TblApplicationNetware tblApplicationNetware = new TblApplicationNetware();
            tblApplicationNetware.setNetwareId(assetId);
            tblApplicationNetwareService.deleteTblApplicationNetware(tblApplicationNetware);
            TblApplicationSafe tblApplicationsafe = new TblApplicationSafe();
            tblApplicationsafe.setSafeId(assetId);
            tblApplicationSafeService.deleteTblApplicationSafe(tblApplicationsafe);
        }
        return tblAssetOverviewMapper.deleteTblAssetOverviewByIds(assetIds);
    }

    //检查维度
    private static List<String> checkDim = new ArrayList<>();
    static {
        checkDim.add("mapper");//模块组成
        checkDim.add("deploy");//模块部署
        checkDim.add("business_dim");//业务  即虚拟系统
    }
    /**
     * 检查资产引用关系
     * @param assetId
     * @return
     */
    public void checkUse(Long assetId) {
        Iterator<String> iterator = checkDim.iterator();
        while (iterator.hasNext()){
            String dictval = iterator.next();
            String next = "asset_dim-"+ dictval;
            Method assetMethod = EChartAssemble.METHOD_MAP.get(next);
            SysDictData dictData = dictionaryService.dictionaryData("asset_dim", dictval);
            List<AssetPropertyVO.Common> assetDimInfo = null;
            try {
                assetDimInfo = (List<AssetPropertyVO.Common>)assetMethod.invoke(eChartAssemble, Long.valueOf(assetId), dictData);
            } catch (ReflectiveOperationException e) {
                e.printStackTrace();
            }
            if (ObjectUtils.isNotEmpty(assetDimInfo)){
                TblAssetOverview tblAssetOverview = selectAssetInfo(assetId);
                throw new AssetDeleteException("删除除失败，资产["+tblAssetOverview.getAssetName()+"]已被其它资产引用！");
            }
        }
    }

    @Override
    public TblAssetOverview selectAssetInfo(Long assetId) {
        TblAssetOverview tblAssetOverview = tblAssetOverviewMapper.selectTblAssetOverviewByAssetId(assetId);
        Assert.notNull(tblAssetOverview, "未查询到该资产");
        TblAssetOverview assetOverview = null;
        if (AssetClassEnum.MACHROOM.isEnum(tblAssetOverview.getAssetClass())) {
            assetOverview = tblMachroomService.selectTblMachroomByAssetId(assetId);
        }
        if (AssetClassEnum.NETWORKDEVICES.isEnum(tblAssetOverview.getAssetClass())) {
            assetOverview = tblNetworkDevicesService.selectTblNetworkDevicesByAssetId(assetId);
        }
        if (AssetClassEnum.SAFETY.isEnum(tblAssetOverview.getAssetClass())) {
            assetOverview = tblSafetyService.selectTblSafetyByAssetId(assetId);
        }
        if (AssetClassEnum.SERVER.isEnum(tblAssetOverview.getAssetClass())) {
            assetOverview = tblServerService.selectTblServerByAssetId(assetId);
        }
        if (AssetClassEnum.TERMINAL.isEnum(tblAssetOverview.getAssetClass())) {
            assetOverview = tblTerminalService.selectTblTerminalByAssetId(assetId);
        }
        if (AssetClassEnum.SYSTEMSOFTWARE.isEnum(tblAssetOverview.getAssetClass())) {
            assetOverview = tblSystemSoftwareService.selectTblSystemSoftwareByAssetId(assetId);
        }
        if (AssetClassEnum.BUSINESSAPPLICATION.isEnum(tblAssetOverview.getAssetClass())) {
            assetOverview = tblBusinessApplicationService.selectTblBusinessApplicationByAssetId(assetId);
        }
        if (AssetClassEnum.DATABASE.isEnum(tblAssetOverview.getAssetClass())) {
            assetOverview = tblDatabaseService.selectTblDatabaseByAssetId(assetId);
        }
        if (AssetClassEnum.DATARESOURCE.isEnum(tblAssetOverview.getAssetClass())) {
            assetOverview = tblDataResourceService.selectTblDataResourceByAssetId(assetId);
        }
        if (AssetClassEnum.PASSWORDPRODUCT.isEnum(tblAssetOverview.getAssetClass())) {
            assetOverview = tblPasswordProductService.selectTblPasswordProductByAssetId(assetId);
        }
        if (AssetClassEnum.MANAGE.isEnum(tblAssetOverview.getAssetClass())) {
            assetOverview = tblManageService.selectTblManageByAssetId(assetId);
        }
        if (AssetClassEnum.ZONEBOUNDARY.isEnum(tblAssetOverview.getAssetClass())) {
            assetOverview = tblZoneBoundaryService.selectTblZoneBoundaryByAssetId(assetId);
        }
        if (assetOverview != null) {
//            BeanUtil.copyProperties(tblAssetOverview, assetOverview);
            CopyOptions copyOptions = new CopyOptions();
            copyOptions.ignoreNullValue();
            if(StrUtil.isBlank(tblAssetOverview.getAssetTypeDesc())){
                copyOptions.setIgnoreProperties("assetTypeDesc");
            }
            cn.hutool.core.bean.BeanUtil.copyProperties(tblAssetOverview,assetOverview,copyOptions);
        }
        return assetOverview;
    }

    public List<? extends TblAssetOverview> selectAssetInfoByClassAndName(TblAssetOverview overview) {
        List<? extends TblAssetOverview> assetOverview = null;
        TblAssetOverview tblAssetOverview;
        if (AssetClassEnum.MACHROOM.isEnum(overview.getAssetClass())) {
            tblAssetOverview = new TblMachroom();
            tblAssetOverview.setAssetName(overview.getAssetName());
            assetOverview = tblMachroomService.selectTblMachroomList((TblMachroom) tblAssetOverview);
        }
        if (AssetClassEnum.NETWORKDEVICES.isEnum(overview.getAssetClass())) {
            tblAssetOverview = new TblNetworkDevices();
            tblAssetOverview.setAssetName(overview.getAssetName());
            assetOverview = tblNetworkDevicesService.selectTblNetworkDevicesList((TblNetworkDevices) tblAssetOverview);
        }
        if (AssetClassEnum.SAFETY.isEnum(overview.getAssetClass())) {
            tblAssetOverview = new TblSafety();
            tblAssetOverview.setAssetName(overview.getAssetName());
            assetOverview = tblSafetyService.selectTblSafetyList((TblSafety) tblAssetOverview);
        }
        if (AssetClassEnum.SERVER.isEnum(overview.getAssetClass())) {
            tblAssetOverview = new TblServer();
            tblAssetOverview.setAssetName(overview.getAssetName());
            assetOverview = tblServerService.selectTblServerList2((TblServer) tblAssetOverview);
        }
        if (AssetClassEnum.TERMINAL.isEnum(overview.getAssetClass())) {
            tblAssetOverview = new TblTerminal();
            tblAssetOverview.setAssetName(overview.getAssetName());
            assetOverview = tblTerminalService.selectTblTerminalList((TblTerminal) tblAssetOverview);
        }
        if (AssetClassEnum.SYSTEMSOFTWARE.isEnum(overview.getAssetClass())) {
            tblAssetOverview = new TblSystemSoftware();
            tblAssetOverview.setAssetName(overview.getAssetName());
            assetOverview = tblSystemSoftwareService.selectTblSystemSoftwareList((TblSystemSoftware) tblAssetOverview);
        }
        if (AssetClassEnum.BUSINESSAPPLICATION.isEnum(overview.getAssetClass())) {
            tblAssetOverview = new TblBusinessApplication();
            tblAssetOverview.setAssetName(overview.getAssetName());
            assetOverview = tblBusinessApplicationService.selectTblBusinessApplicationList((TblBusinessApplication) tblAssetOverview);
        }
        if (AssetClassEnum.DATABASE.isEnum(overview.getAssetClass())) {
            tblAssetOverview = new TblDatabase();
            tblAssetOverview.setAssetName(overview.getAssetName());
            assetOverview = tblDatabaseService.selectTblDatabaseList((TblDatabase) tblAssetOverview);
        }
        if (AssetClassEnum.DATARESOURCE.isEnum(overview.getAssetClass())) {
            tblAssetOverview = new TblDataResource();
            tblAssetOverview.setAssetName(overview.getAssetName());
            assetOverview = tblDataResourceService.selectTblDataResourceList((TblDataResource) tblAssetOverview);
        }
        if (AssetClassEnum.PASSWORDPRODUCT.isEnum(overview.getAssetClass())) {
            tblAssetOverview = new TblPasswordProduct();
            tblAssetOverview.setAssetName(overview.getAssetName());
            assetOverview = tblPasswordProductService.selectTblPasswordProductList((TblPasswordProduct) tblAssetOverview);
        }
        if (AssetClassEnum.MANAGE.isEnum(overview.getAssetClass())) {
            tblAssetOverview = new TblManage();
            tblAssetOverview.setAssetName(overview.getAssetName());
            assetOverview = tblManageService.selectTblManageList((TblManage) tblAssetOverview);
        }
        if (AssetClassEnum.ZONEBOUNDARY.isEnum(overview.getAssetClass())) {
            tblAssetOverview = new TblZoneBoundary();
            tblAssetOverview.setAssetName(overview.getAssetName());
            assetOverview = tblZoneBoundaryService.selectTblZoneBoundaryList((TblZoneBoundary) tblAssetOverview);
        }
        return assetOverview;
    }


    @Override
    public AssetPropertyVO selectAssetProperty(TblAssetOverview tblAssetOverview) {
        Long assetId = tblAssetOverview.getAssetId();
        AssetPropertyVO assetPropertyVO = new AssetPropertyVO();
        List<Object> data = new ArrayList<>();
        List<AssetPropertyVO.Link> links = new ArrayList<>();
        List<AssetPropertyVO.Categorie> categories = new ArrayList<>();
        AssetPropertyVO.Categorie categorie = null;
        AssetPropertyVO.Common common = null;

        common = new AssetPropertyVO.Common();
        common.setId(assetId.toString());
        common.setName(tblAssetOverview.getAssetName());
        common.setValue("资产分类：" + tblAssetOverview.getAssetClassDesc() + (StringUtils.isNotEmpty(tblAssetOverview.getAssetTypeDesc()) ? "，资产类型：" + tblAssetOverview.getAssetTypeDesc() : ""));
        common.setSymbolSize("80");
        common.setCategory("资产");
        data.add(common);
        categories.add(AssetPropertyVO.getCategorie("资产"));

        if (AssetClassEnum.BUSINESSAPPLICATION.isEnum(tblAssetOverview.getAssetClass())) {
            TblMapper tblMapper = new TblMapper();
            tblMapper.setApplicationId(assetId);
            List<TblMapper> tblMappers = tblMapperService.selectTblMapperList(tblMapper);
            if (CollectionUtil.isNotEmpty(tblMappers)) {
                categories.add(AssetPropertyVO.getCategorie("模块"));
                long id = snowflake.nextId();
                data.add(AssetPropertyVO.getCommon("tbl_mapper-" + id, "模块", "60", "模块"));
                links.add(AssetPropertyVO.getLink("tbl_mapper-" + id, assetId.toString()));
                tblMappers.forEach(e -> {
                    AssetPropertyVO.Mapper mapper = new AssetPropertyVO.Mapper();
                    BeanUtils.copyProperties(e, mapper);
                    mapper.setId("tbl_mapper-" + e.getModuleId());
                    mapper.setName(e.getModuleName());
                    mapper.setSymbolSize("40");
                    mapper.setCategory("模块");
                    mapper.setValue("模块版本：" + e.getModuleVersion() + "，模块描述：" + e.getModuleDesc());
                    data.add(mapper);
                    links.add(AssetPropertyVO.getLink("tbl_mapper-" + e.getModuleId(), "tbl_mapper-" + id));

                    // 模块-软件组成信息
                    TblComponent tblComponent = new TblComponent();
                    tblComponent.setModuleId(e.getModuleId());
                    List<TblComponent> tblComponents = tblComponentService.selectTblComponentList(tblComponent);
                    if (CollectionUtil.isNotEmpty(tblComponents)) {
                        long componentId = snowflake.nextId();
                        data.add(AssetPropertyVO.getCommon("tbl_component-" + componentId, "组成", "40", "组成"));
                        links.add(AssetPropertyVO.getLink("tbl_component-" + componentId, "tbl_mapper-" + e.getModuleId()));
                        tblComponents.forEach(c -> {
                            AssetPropertyVO.Component component = new AssetPropertyVO.Component();
                            // BeanUtils.copyProperties(c, component);
                            component.setId("tbl_component-" + c.getComponentId());
                            component.setName(c.getProcName());
                            component.setSymbolSize("30");
                            component.setCategory("组成");
                            component.setValue("版本：" + c.getProcVersion());
                            data.add(component);
                            links.add(AssetPropertyVO.getLink("tbl_component-" + c.getComponentId(), "tbl_component-" + componentId));
                        });
                    }

                    // 模块-部署信息
                    TblDeploy tblDeploy = new TblDeploy();
                    tblDeploy.setModuelId(e.getModuleId());
                    tblDeploy.setPrdid("default");
                    List<TblDeploy> tblDeploys = tblDeployService.selectTblDeployList(tblDeploy);
                    if (CollectionUtil.isNotEmpty(tblDeploys)) {
                        long deployId = snowflake.nextId();
                        data.add(AssetPropertyVO.getCommon("tbl_deploy-" + deployId, "部署", "40", "部署"));
                        links.add(AssetPropertyVO.getLink("tbl_deploy-" + deployId, "tbl_mapper-" + e.getModuleId()));
                        tblDeploys.forEach(d -> {
                            AssetPropertyVO.Deploy deploy = new AssetPropertyVO.Deploy();
                            // BeanUtils.copyProperties(d, deploy);
                            deploy.setId("tbl_deploy-" + d.getDeployId());
                            deploy.setName(d.getAssetName());
                            deploy.setSymbolSize("30");
                            deploy.setCategory("部署");
                            deploy.setValue("部署位置(路径)：" + d.getDeployPosition() + "，端口：" + d.getPort());
                            data.add(deploy);
                            links.add(AssetPropertyVO.getLink("tbl_deploy-" + d.getDeployId(), "tbl_deploy-" + deployId));
                        });
                    }
                });
                categories.add(AssetPropertyVO.getCategorie("组成"));
                categories.add(AssetPropertyVO.getCategorie("部署"));
            }
        }

        // 位置信息
        if (tblAssetOverview.getLocationId() != null && tblAssetOverview.getLocationFullName() != null) {
            categories.add(AssetPropertyVO.getCategorie("位置信息"));
            long id = snowflake.nextId();
            data.add(AssetPropertyVO.getCommon("tbl_location-" + id, "位置信息", "60", "位置信息"));
            links.add(AssetPropertyVO.getLink("tbl_location-" + id, assetId.toString()));
            data.add(AssetPropertyVO.getCommon("tbl_location-" + id + "-" + tblAssetOverview.getLocationId(), tblAssetOverview.getLocationFullName(), "40", "位置信息"));
            links.add(AssetPropertyVO.getLink("tbl_location-" + id + "-" + tblAssetOverview.getLocationId(), "tbl_location-" + id));
        }

        // 供应商信息
        if (ObjectUtils.isNotEmpty(tblAssetOverview.getVendor())) {
            TblVendor tblVendor = tblVendorService.selectTblVendorById(tblAssetOverview.getVendor());
            if (tblVendor != null) {
                categories.add(AssetPropertyVO.getCategorie("供应商"));
                long id = snowflake.nextId();
                data.add(AssetPropertyVO.getCommon("tbl_vendor-" + id, "供应商", "60", "供应商"));
                links.add(AssetPropertyVO.getLink("tbl_vendor-" + id, assetId.toString()));
                String value = "联系人：" + tblVendor.getVendorManageName() + "，联系电话：" + tblVendor.getVendorPhone();
                data.add(AssetPropertyVO.getCommon("tbl_vendor-" + id + "-" + tblVendor.getId(), tblVendor.getVendorName(), value, "40", "供应商"));
                links.add(AssetPropertyVO.getLink("tbl_vendor-" + id + "-" + tblVendor.getId(), "tbl_vendor-" + id));
            }
        }

        // 输出信息
        TblOutput tblOutput = new TblOutput();
        tblOutput.setAssetId(assetId);
        List<TblOutput> tblOutputs = tblOutputService.selectTblOutputList(tblOutput);
        if (CollectionUtil.isNotEmpty(tblOutputs)) {
            categories.add(AssetPropertyVO.getCategorie("输出信息"));
            long id = snowflake.nextId();
            data.add(AssetPropertyVO.getCommon("tbl_output-" + id, "输出", "60", "输出信息"));
            links.add(AssetPropertyVO.getLink("tbl_output-" + id, assetId.toString()));
            tblOutputs.forEach(e -> {
                AssetPropertyVO.Output output = new AssetPropertyVO.Output();
                // BeanUtils.copyProperties(e, output);
                output.setId("tbl_output-" + e.getId());
                output.setName(e.getOutputName());
                output.setSymbolSize("40");
                output.setCategory("输出信息");
                output.setValue("输出物存储设备：" + e.getOutputAssetName());
                data.add(output);
                links.add(AssetPropertyVO.getLink("tbl_output-" + e.getId(), "tbl_output-" + id));
            });
        }

        // 管理属性
        TblManagement tblManagement = new TblManagement();
        tblManagement.setAssetId(assetId);
        List<TblManagement> tblManagements = tblManagementService.selectTblManagementList(tblManagement);
        if (CollectionUtil.isNotEmpty(tblManagements)) {
            categories.add(AssetPropertyVO.getCategorie("管理属性"));
            long id = snowflake.nextId();
            data.add(AssetPropertyVO.getCommon("tbl_management-" + id, "管理属性", "60", "管理属性"));
            links.add(AssetPropertyVO.getLink("tbl_management-" + id, assetId.toString()));
            tblManagements.forEach(e -> {
                AssetPropertyVO.Management management = new AssetPropertyVO.Management();
                // BeanUtils.copyProperties(e, management);
                management.setId("tbl_management-" + e.getId());
                management.setName(e.getManagerName());
                management.setSymbolSize("40");
                management.setCategory("管理属性");
                management.setValue("联系方式：" + e.getManagerPhone() + "，所在部门：" + e.getManagerDept());
                data.add(management);
                links.add(AssetPropertyVO.getLink("tbl_management-" + e.getId(), "tbl_management-" + id));
            });
        }

        // 网络属性
        TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
        tblNetworkIpMac.setAssetId(assetId);
        List<TblNetworkIpMac> tblNetworkIpMacs = tblNetworkIpMacService.selectTblNetworkIpMacList(tblNetworkIpMac);
        if (CollectionUtil.isNotEmpty(tblNetworkIpMacs)) {
            categories.add(AssetPropertyVO.getCategorie("网络属性"));
            long id = snowflake.nextId();
            data.add(AssetPropertyVO.getCommon("tbl_network_ip_mac-" + id, "网络属性", "60", "网络属性"));
            links.add(AssetPropertyVO.getLink("tbl_network_ip_mac-" + id, assetId.toString()));
            tblNetworkIpMacs.forEach(e -> {
                AssetPropertyVO.NetworkIpMac networkIpMac = new AssetPropertyVO.NetworkIpMac();
                // BeanUtils.copyProperties(e, networkIpMac);
                networkIpMac.setId("tbl_network_ip_mac-" + e.getId());
                networkIpMac.setName(e.getIpv4());
                networkIpMac.setSymbolSize("40");
                networkIpMac.setCategory("网络属性");
                networkIpMac.setValue("MAC地址：" + e.getMac() + "，网络区域：" + e.getDomainFullName());
                data.add(networkIpMac);
                links.add(AssetPropertyVO.getLink("tbl_network_ip_mac-" + e.getId(), "tbl_network_ip_mac-" + id));
            });
        }

        assetPropertyVO.setData(data);
        assetPropertyVO.setLinks(links);
        assetPropertyVO.setCategories(categories);
        return assetPropertyVO;
    }

    @Override
    public AssetChartVo getAssetChartVo(Long assetId) {
        AssetChartVo assetChartVo = new AssetChartVo();
        List<Node> nodes = new ArrayList<>();
        List<Link> links = new ArrayList<>();
        List<Category> categories = new ArrayList<>();
        TblAssetOverview tblAssetOverview = selectAssetInfo(assetId);
        Node node = new Node();
        node.setId(assetId.toString());
        node.setName(tblAssetOverview.getAssetName());
        node.setSymbolSize("40");
        node.setValue("资产分类：" + tblAssetOverview.getAssetClassDesc() + (StringUtils.isNotEmpty(tblAssetOverview.getAssetTypeDesc()) ? "，资产类型：" + tblAssetOverview.getAssetTypeDesc() : ""));
        nodes.add(node);
        categories.add(new Category("资产"));

        if (AssetClassEnum.BUSINESSAPPLICATION.isEnum(tblAssetOverview.getAssetClass())) {
            TblMapper tblMapper = new TblMapper();
            tblMapper.setApplicationId(assetId);
            List<TblMapper> tblMappers = tblMapperService.selectTblMapperList(tblMapper);
            if (CollectionUtil.isNotEmpty(tblMappers)) {
                categories.add(new Category("模块"));
                tblMappers.forEach(e -> {
                    Node mapperNode = new Node();
                    mapperNode.setId("tbl_mapper-" + e.getModuleId());
                    mapperNode.setName(e.getModuleName());
                    mapperNode.setCategory("模块");
                    nodes.add(mapperNode);
                    links.add(new Link("模块", "tbl_mapper-" + e.getModuleId(), assetId.toString()));

                    // 模块-软件组成信息
                    TblComponent tblComponent = new TblComponent();
                    tblComponent.setModuleId(e.getModuleId());
                    List<TblComponent> tblComponents = tblComponentService.selectTblComponentList(tblComponent);
                    if (CollectionUtil.isNotEmpty(tblComponents)) {
                        tblComponents.forEach(c -> {
                            Node componentNode = new Node();
                            componentNode.setId("tbl_component-" + c.getComponentId());
                            componentNode.setName(c.getProcName());
                            componentNode.setCategory("组成");
                            nodes.add(componentNode);
                            links.add(new Link("组成", "tbl_component-" + c.getComponentId(), "tbl_mapper-" + e.getModuleId()));
                        });
                    }

                    // 模块-部署信息
                    TblDeploy tblDeploy = new TblDeploy();
                    tblDeploy.setModuelId(e.getModuleId());
                    tblDeploy.setPrdid("default");
                    List<TblDeploy> tblDeploys = tblDeployService.selectTblDeployList(tblDeploy);
                    if (CollectionUtil.isNotEmpty(tblDeploys)) {
                        tblDeploys.forEach(d -> {
                            Node deployNode = new Node();
                            deployNode.setId("tbl_deploy-" + d.getDeployId());
                            deployNode.setName(d.getAssetName());
                            deployNode.setCategory("部署");
                            nodes.add(deployNode);
                            links.add(new Link("部署", "tbl_deploy-" + d.getDeployId(), "tbl_mapper-" + e.getModuleId()));
                        });
                    }
                });
                categories.add(new Category("组成"));
                categories.add(new Category("部署"));
            }
        }

        // 位置信息
        if (tblAssetOverview.getLocationId() != null && tblAssetOverview.getLocationFullName() != null) {
            categories.add(new Category("位置信息"));
            nodes.add(new Node("tbl_location-" + tblAssetOverview.getLocationId(), tblAssetOverview.getLocationFullName(), tblAssetOverview.getLocationFullName(), "20", "位置信息"));
            links.add(new Link("位置信息", "tbl_location-" + tblAssetOverview.getLocationId(), assetId.toString()));
        }

        // 供应商信息
        if (ObjectUtils.isNotEmpty(tblAssetOverview.getVendor())) {
            TblVendor tblVendor = tblVendorService.selectTblVendorById(tblAssetOverview.getVendor());
            if (tblVendor != null) {
                categories.add(new Category("供应商"));
                String value = "联系人：" + tblVendor.getVendorManageName() + "，联系电话：" + tblVendor.getVendorPhone();
                nodes.add(new Node("tbl_vendor-" + tblVendor.getId(), tblVendor.getVendorName(), value, "20", "供应商"));
                links.add(new Link("供应商", "tbl_vendor-" + tblVendor.getId(), assetId.toString()));
            }
        }
        // 输出信息
        TblOutput tblOutput = new TblOutput();
        tblOutput.setAssetId(assetId);
        List<TblOutput> tblOutputs = tblOutputService.selectTblOutputList(tblOutput);
        if (CollectionUtil.isNotEmpty(tblOutputs)) {
            categories.add(new Category("输出信息"));
            tblOutputs.forEach(e -> {
                Node outputNode = new Node();
                outputNode.setId("tbl_output-" + e.getId());
                outputNode.setName(e.getOutputName());
                outputNode.setCategory("输出信息");
                outputNode.setValue("输出物存储设备：" + e.getOutputAssetName());
                nodes.add(outputNode);
                links.add(new Link("输出信息", "tbl_output-" + e.getId(), assetId.toString()));
            });
        }


        // 管理属性
        TblManagement tblManagement = new TblManagement();
        tblManagement.setAssetId(assetId);
        List<TblManagement> tblManagements = tblManagementService.selectTblManagementList(tblManagement);
        if (CollectionUtil.isNotEmpty(tblManagements)) {
            categories.add(new Category("管理属性"));
            tblManagements.forEach(e -> {
                Node managementNode = new Node();
                managementNode.setId("tbl_management-" + e.getId());
                managementNode.setName(e.getManagerName());
                managementNode.setCategory("管理属性");
                managementNode.setValue("联系方式：" + e.getManagerPhone() + "，所在部门：" + e.getManagerDept());
                nodes.add(managementNode);
                links.add(new Link("管理属性", "tbl_management-" + e.getId(), assetId.toString()));
            });
        }


        // 网络属性
        TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
        tblNetworkIpMac.setAssetId(assetId);
        List<TblNetworkIpMac> tblNetworkIpMacs = tblNetworkIpMacService.selectTblNetworkIpMacList(tblNetworkIpMac);
        if (CollectionUtil.isNotEmpty(tblNetworkIpMacs)) {
            categories.add(new Category("网络属性"));
            tblNetworkIpMacs.forEach(e -> {
                Node networkIpMacNode = new Node();
                networkIpMacNode.setId("tbl_network_ip_mac-" + e.getId());
                networkIpMacNode.setName(e.getIpv4());
                networkIpMacNode.setCategory("网络属性");
                networkIpMacNode.setValue("MAC地址：" + e.getMac() + "，网络区域：" + e.getDomainFullName());
                nodes.add(networkIpMacNode);
                links.add(new Link("网络属性", "tbl_network_ip_mac-" + e.getId(), assetId.toString()));
            });
        }
        assetChartVo.setNodes(nodes);
        assetChartVo.setLinks(links);
        assetChartVo.setCategories(categories);
        return assetChartVo;
    }

    @Override
    public AssetChartVo getAssetChartVoByDim(Long assetId, String[] dimList) {
        AssetChartVo assetChartVo = new AssetChartVo();
        List<Node> nodes = new ArrayList<>();
        List<Link> links = new ArrayList<>();
        List<Category> categories = new ArrayList<>();
        Set<String> set = new HashSet<>();
        Collections.addAll(set, dimList);
        TblAssetOverview tblAssetOverview = selectAssetInfo(assetId);
        Node node = new Node();
        node.setId(assetId.toString());
        node.setName(tblAssetOverview.getAssetName());
        node.setSymbolSize("40");
        node.setValue("资产分类：" + tblAssetOverview.getAssetClassDesc() + (StringUtils.isNotEmpty(tblAssetOverview.getAssetTypeDesc()) ? "，资产类型：" + tblAssetOverview.getAssetTypeDesc() : ""));
        nodes.add(node);
        categories.add(new Category("资产"));

        if (AssetClassEnum.BUSINESSAPPLICATION.isEnum(tblAssetOverview.getAssetClass()) && (set.contains("模块"))) {
            TblMapper tblMapper = new TblMapper();
            tblMapper.setApplicationId(assetId);
            List<TblMapper> tblMappers = tblMapperService.selectTblMapperList(tblMapper);
            if (CollectionUtil.isNotEmpty(tblMappers)) {
                categories.add(new Category("模块"));
                tblMappers.forEach(e -> {
                    Node mapperNode = new Node();
                    mapperNode.setId("tbl_mapper-" + e.getModuleId());
                    mapperNode.setName(e.getModuleName());
                    mapperNode.setCategory("模块");
                    nodes.add(mapperNode);
                    links.add(new Link("模块","tbl_mapper-" + e.getModuleId(), assetId.toString()));

                    // 模块-软件组成信息
                    TblComponent tblComponent = new TblComponent();
                    tblComponent.setModuleId(e.getModuleId());
                    List<TblComponent> tblComponents = tblComponentService.selectTblComponentList(tblComponent);
                    if (CollectionUtil.isNotEmpty(tblComponents)) {
                        tblComponents.forEach(c -> {
                            Node componentNode = new Node();
                            componentNode.setId("tbl_component-" + c.getComponentId());
                            componentNode.setName(c.getProcName());
                            componentNode.setCategory("组成");
                            nodes.add(componentNode);
                            links.add(new Link("组成","tbl_component-" + c.getComponentId(), "tbl_mapper-" + e.getModuleId()));
                        });
                    }

                    // 模块-部署信息
                    TblDeploy tblDeploy = new TblDeploy();
                    tblDeploy.setModuelId(e.getModuleId());
                    tblDeploy.setPrdid("default");
                    List<TblDeploy> tblDeploys = tblDeployService.selectTblDeployList(tblDeploy);
                    if (CollectionUtil.isNotEmpty(tblDeploys)) {
                        tblDeploys.forEach(d -> {
                            Node deployNode = new Node();
                            deployNode.setId("tbl_deploy-" + d.getDeployId());
                            deployNode.setName(d.getAssetName());
                            deployNode.setCategory("部署");
                            nodes.add(deployNode);
                            links.add(new Link("部署", "tbl_deploy-" + d.getDeployId(), "tbl_mapper-" + e.getModuleId()));
                        });
                    }
                });
                categories.add(new Category("组成"));
                categories.add(new Category("部署"));
            }
        }

        // 位置信息
        if (set.contains("位置信息") &&  tblAssetOverview.getLocationId() != null && tblAssetOverview.getLocationFullName() != null) {
            categories.add(new Category("位置信息"));
            nodes.add(new Node("tbl_location-" + tblAssetOverview.getLocationId(), tblAssetOverview.getLocationFullName(), tblAssetOverview.getLocationFullName(), "20", "位置信息"));
            links.add(new Link("位置信息","tbl_location-" + tblAssetOverview.getLocationId(), assetId.toString()));
        }

        // 供应商信息
        if (set.contains("供应商") && ObjectUtils.isNotEmpty(tblAssetOverview.getVendor())) {
            TblVendor tblVendor = tblVendorService.selectTblVendorById(tblAssetOverview.getVendor());
            if (tblVendor != null) {
                categories.add(new Category("供应商"));
                String value = "联系人：" + tblVendor.getVendorManageName() + "，联系电话：" + tblVendor.getVendorPhone();
                nodes.add(new Node("tbl_vendor-" + tblVendor.getId(), tblVendor.getVendorName(), value, "20", "供应商"));
                links.add(new Link("供应商","tbl_vendor-" + tblVendor.getId(), assetId.toString()));
            }
        }

        if (set.contains("输出信息")) {
            // 输出信息
            TblOutput tblOutput = new TblOutput();
            tblOutput.setAssetId(assetId);
            List<TblOutput> tblOutputs = tblOutputService.selectTblOutputList(tblOutput);
            if (CollectionUtil.isNotEmpty(tblOutputs)) {
                categories.add(new Category("输出信息"));
                tblOutputs.forEach(e -> {
                    Node outputNode = new Node();
                    outputNode.setId("tbl_output-" + e.getId());
                    outputNode.setName(e.getOutputName());
                    outputNode.setCategory("输出信息");
                    outputNode.setValue("输出物存储设备：" + e.getOutputAssetName());
                    nodes.add(outputNode);
                    links.add(new Link("输出信息","tbl_output-" + e.getId(), assetId.toString()));
                });
            }
        }

        if (set.contains("管理属性")) {
            // 管理属性
            TblManagement tblManagement = new TblManagement();
            tblManagement.setAssetId(assetId);
            List<TblManagement> tblManagements = tblManagementService.selectTblManagementList(tblManagement);
            if (CollectionUtil.isNotEmpty(tblManagements)) {
                categories.add(new Category("管理属性"));
                tblManagements.forEach(e -> {
                    Node managementNode = new Node();
                    managementNode.setId("tbl_management-" + e.getId());
                    managementNode.setCategory("管理属性");
                    managementNode.setValue("联系方式：" + e.getManagerPhone() + "，所在部门：" + e.getManagerDept());
                    nodes.add(managementNode);
                    links.add(new Link("管理属性", "tbl_management-" + e.getId(), assetId.toString()));
                });
            }
        }

        if (set.contains("网络属性")) {
            // 网络属性
            TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
            tblNetworkIpMac.setAssetId(assetId);
            List<TblNetworkIpMac> tblNetworkIpMacs = tblNetworkIpMacService.selectTblNetworkIpMacList(tblNetworkIpMac);
            if (CollectionUtil.isNotEmpty(tblNetworkIpMacs)) {
                categories.add(new Category("网络属性"));
                tblNetworkIpMacs.forEach(e -> {
                    Node networkIpMacNode = new Node();
                    networkIpMacNode.setId("tbl_network_ip_mac-" + e.getId());
                    networkIpMacNode.setName(e.getIpv4());
                    networkIpMacNode.setCategory("网络属性");
                    networkIpMacNode.setValue("MAC地址：" + e.getMac() + "，网络区域：" + e.getDomainFullName());
                    nodes.add(networkIpMacNode);
                    links.add(new Link("网络属性","tbl_network_ip_mac-" + e.getId(), assetId.toString()));
                });
            }
        }
        assetChartVo.setNodes(nodes);
        assetChartVo.setLinks(links);
        assetChartVo.setCategories(categories);
        return  assetChartVo;
    }

    @Override
    public List<TblAssetOverview> selectInfoByAssetIds(Long[] assetIds) {
        return tblAssetOverviewMapper.selectInfoByAssetIds(assetIds);
    }

    @Override
    public TblAssetOverview selectInfoByAssetId(Long assetId) {
        return tblAssetOverviewMapper.selectInfoByAssetId(assetId);
    }

    @Override
    public List<TblAssetOverview> selectDetailsTblAssetOverview(List<Long> assetIds) {
        ArrayList<TblAssetOverview> allAsset = new ArrayList<>();
        Optional<List<? extends TblAssetOverview>> assetOverview;
        assetOverview = Optional.ofNullable(tblMachroomService.selectTblMachroomByAssetIds(assetIds.toArray(new Long[]{})));
        assetOverview.ifPresent(assets->allAsset.addAll(assets));
        assetOverview = Optional.ofNullable(tblNetworkDevicesService.selectTblNetworkDevicesByAssetIds(assetIds.toArray(new Long[]{})));
        assetOverview.ifPresent(assets->allAsset.addAll(assets));
        assetOverview = Optional.ofNullable(tblSafetyService.selectTblSafetyByAssetIds(assetIds.toArray(new Long[]{})));
        assetOverview.ifPresent(assets->allAsset.addAll(assets));
        assetOverview = Optional.ofNullable(tblServerService.selectTblServerByAssetIds(assetIds.toArray(new Long[]{})));
        assetOverview.ifPresent(assets->allAsset.addAll(assets));
        assetOverview = Optional.ofNullable(tblTerminalService.selectTblTerminalByAssetIds(assetIds.toArray(new Long[]{})));
        assetOverview.ifPresent(assets->allAsset.addAll(assets));
        assetOverview = Optional.ofNullable(tblSystemSoftwareService.selectTblSystemSoftwareByAssetIds(assetIds.toArray(new Long[]{})));
        assetOverview.ifPresent(assets->allAsset.addAll(assets));
        assetOverview = Optional.ofNullable(tblBusinessApplicationService.selectTblBusinessApplicationByAssetIds(assetIds.toArray(new Long[]{})));
        assetOverview.ifPresent(assets->allAsset.addAll(assets));
        assetOverview = Optional.ofNullable(tblDatabaseService.selectTblDatabaseByAssetIds(assetIds.toArray(new Long[]{})));
        assetOverview.ifPresent(assets->allAsset.addAll(assets));
        assetOverview = Optional.ofNullable(tblDataResourceService.selectTblDataResourceByAssetIds(assetIds.toArray(new Long[]{})));
        assetOverview.ifPresent(assets->allAsset.addAll(assets));
        assetOverview = Optional.ofNullable(tblPasswordProductService.selectTblPasswordProductByAssetIds(assetIds.toArray(new Long[]{})));
        assetOverview.ifPresent(assets->allAsset.addAll(assets));
        assetOverview = Optional.ofNullable(tblManageService.selectTblManageByAssetIds(assetIds.toArray(new Long[]{})));
        assetOverview.ifPresent(assets->allAsset.addAll(assets));
        assetOverview = Optional.ofNullable(tblZoneBoundaryService.selectTblZoneBoundaryByAssetIds(assetIds.toArray(new Long[]{})));
        assetOverview.ifPresent(assets->allAsset.addAll(assets));
        return allAsset;
    }

    @Override
    public List<TblAssetOverview> selectTblAssetInfoByIP(String ip) {
        return tblAssetOverviewMapper.selectAssetByIP(ip);
    }

    @Override
    public List<AssetClass> selectedAssetTypeChildrenByIdAPid(Long type) {
        return tblAssetsTypeMapper.selectedAssetTypeChildrenByIdAPid(type);
    }

    @Override
    public List<TblAssetOverview> selectTblAssetOverviewListNotRole(TblAssetOverview tblAssetOverview) {
        List<TblAssetOverview> tblAssetOverviews = tblAssetOverviewMapper.selectTblAssetOverviewList(tblAssetOverview);
        packAssetOverview(tblAssetOverviews);
        return tblAssetOverviews;
    }

    /**
     * 根据资产ID获取位置全名
     *
     * @param assetId 资产ID
     * @return 位置全名（多个位置用分号分隔）
     */
    @Override
    public String getLocationFullNameByAssetId(Long assetId) {
        if (assetId == null) {
            return null;
        }
        try {
            return tblAssetOverviewMapper.selectLocationFullNameByAssetId(assetId);
        } catch (Exception e) {
            // 记录日志但不抛出异常，避免影响主要业务流程
            System.err.println("获取位置全名失败，assetId: " + assetId + ", 错误: " + e.getMessage());
            return null;
        }
    }
}
