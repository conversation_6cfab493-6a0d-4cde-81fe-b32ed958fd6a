# LocationId 多值处理功能开发任务

## 任务背景
在 aqsoc-main 项目中，TblServerController 的 getInfo 方法和 TblAssetOverviewController 的 getAssetInfo 方法返回的 locationId 字段可能包含多个值，格式如 "53,58,59,60"。需要根据这些 ID 查询对应的完整位置名称。

## 技术方案
采用 Controller 层显式调用模式，避免影响现有 Service 方法的其他调用方。

## 执行计划

### 第一步：Mapper 层改造
- [ ] 在 TblAssetOverviewMapper.java 接口中新增方法
- [ ] 在 TblAssetOverviewMapper.xml 中实现 SQL 查询

### 第二步：Service 层改造  
- [ ] 在 ITblAssetOverviewService.java 接口中新增方法
- [ ] 在 TblAssetOverviewServiceImpl.java 中实现方法

### 第三步：Controller 层改造
- [ ] 修改 TblServerController.getInfo() 方法
- [ ] 修改 TblAssetOverviewController.getAssetInfo() 方法

### 第四步：测试验证
- [ ] SQL 查询验证
- [ ] 功能测试
- [ ] 性能测试

## 技术细节

### SQL 设计
使用 FIND_IN_SET + GROUP_CONCAT 实现多值查询：
```sql
SELECT GROUP_CONCAT(l.location_full_name SEPARATOR ';') AS location_full_names
FROM tbl_location l
WHERE FIND_IN_SET(l.location_id, (
    SELECT location_id FROM tbl_asset_overview WHERE asset_id = #{assetId}
));
```

### 异常处理
- assetId 为 null 的情况
- locationId 为空或 null 的情况  
- 位置不存在的情况

## 预期结果
- TblServerController.getInfo() 返回对象包含 locationFullName 字段
- TblAssetOverviewController.getAssetInfo() 返回对象包含 locationFullName 字段
- 多个位置名称用分号分隔显示
- 不影响现有功能和其他调用方
