package com.ruoyi.threaten.domain.vo;

/**
 * 攻击方向刷新结果VO
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public class AttackDirectionRefreshVO 
{
    /** 威胁告警ID */
    private Long alarmId;
    
    /** 刷新后的攻击方向 */
    private String attackDirection;
    
    public AttackDirectionRefreshVO() 
    {
    }
    
    public AttackDirectionRefreshVO(Long alarmId, String attackDirection) 
    {
        this.alarmId = alarmId;
        this.attackDirection = attackDirection;
    }
    
    public Long getAlarmId() 
    {
        return alarmId;
    }
    
    public void setAlarmId(Long alarmId) 
    {
        this.alarmId = alarmId;
    }
    
    public String getAttackDirection() 
    {
        return attackDirection;
    }
    
    public void setAttackDirection(String attackDirection) 
    {
        this.attackDirection = attackDirection;
    }
    
    @Override
    public String toString() 
    {
        return "AttackDirectionRefreshVO{" +
                "alarmId=" + alarmId +
                ", attackDirection='" + attackDirection + '\'' +
                '}';
    }
}
