<template>
  <div class="custom-container">
    <div class="custom-content-container-right">
      <div class="custom-content-search-box">
        <el-form
          ref="queryForm"
          :model="queryParams"
          size="small"
          :inline="true"
          label-position="right"
          label-width="70px"
        >
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="最近告警时间" label-width="98px">
                <el-date-picker
                  v-model="rangeTime"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                />
              </el-form-item>
            </el-col>
            <!--            <el-col :span="6">
              <el-form-item label="告警等级" prop="alarmLevel">
                <el-select
                  clearable
                  v-model="queryParams.alarmLevel"
                  placeholder="请选择告警等级"
                >
                  <el-option
                    v-for="dict in dict.type.threaten_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>-->
            <el-col :span="6">
              <el-form-item label="处置状态" prop="">
                <el-select v-model="queryParams.handleState" placeholder="请选择处置状态" clearable>
                  <el-option
                    v-for="(item,index) in handleStateOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="告警类型" prop="threatenType">
                <el-cascader
                  v-model="queryParams.threatenType"
                  :options="threatenDict"
                  clearable
                  :props="{ label: 'dictLabel', value: 'dictValue' }"
                  placeholder="请选择告警类型"
                >
                  <template slot-scope="{ node, data }">
                    <span>{{ data.dictLabel }}</span>
                    <span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
                  </template>
                </el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="custom-search-btn">
                <el-button
                  class="btn1"
                  size="small"
                  @click="handleQuery"
                >查询
                </el-button>
                <el-button class="btn2" size="small" @click="resetQuery">重置</el-button>
                <el-button v-if="!showAll" class="btn2" size="small" icon="el-icon-arrow-down" @click="showAll=true">
                  展开
                </el-button>
                <el-button v-else class="btn2" size="small" icon="el-icon-arrow-up" @click="showAll=false">收起
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="showAll" :gutter="10">
            <el-col :span="6">
              <el-form-item label="告警名称" prop="threatenName">
                <el-input
                  v-model="queryParams.threatenName"
                  placeholder="请输入告警名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="源IP" prop="srcIp">
                <el-input
                  v-model="queryParams.srcIp"
                  placeholder="请输入源IP"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="目标IP" prop="destIp">
                <el-input
                  v-model="queryParams.destIp"
                  placeholder="请输入目标IP"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="showAll" :gutter="10">
            <el-col :span="6">
              <el-form-item label="数据来源" prop="dataSource">
                <el-select
                  v-model="queryParams.dataSource"
                  placeholder="请选择数据来源"
                  clearable
                  @change="$forceUpdate()"
                >
                  <el-option :key="1" label="探测" :value="1" />
                  <el-option :key="2" label="手动" :value="2" />
                  <el-option :key="8" label="流量" :value="8" />
                  <el-option :key="9" label="探针" :value="9" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所属部门" prop="deptId">
                <dept-select v-model="queryParams.deptId" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="同步状态">
                <el-select v-model="queryParams.synchronizationStatus" placeholder="请选择同步状态" filterable clearable>
                  <el-option
                    v-for="dict in dict.type.synchronization_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="showAll" :gutter="10">
            <el-col :span="6">
              <el-form-item label="通报状态" prop="flowState">
                <el-select v-model="queryParams.flowState" placeholder="请选择通报状态" clearable>
                  <el-option
                    v-for="(item,index) in flowStateOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="处置人">
                <el-input
                  v-model="queryParams.disposer"
                  placeholder="请输入处置人"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所属探针">
                <el-select v-model="queryParams.deviceConfigId" filterable clearable placeholder="请选择">
                  <el-option
                    v-for="item in deviceConfigList"
                    :key="item.id"
                    :label="item.deviceName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="showAll" :gutter="10">
            <el-col :span="6">
              <el-form-item label="攻击方向">
                <el-select v-model="queryParams.attackDirection" placeholder="请选择攻击方向" filterable clearable>
                  <el-option
                    v-for="dict in dict.type.attack_direction"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <!--      <div class="custom-content-search-chunk" style="margin-bottom: 8px">
        <attack-stage ref="atcAge" @handleClick="handleAtcAgeClick"/>
      </div>-->
      <div
        class="custom-content-container"
        :style="showAll ? { height: 'calc(100% - 298px)' } :{ height: 'calc(100% - 208px)' }"
      >
        <div class="common-header">
          <div><span class="common-head-title">告警列表</span></div>
          <div style="width: 60%; margin-left: 10%">
            <!--            <attack-stage-text ref="atcAge" @handleClick="handleAtcAgeClick" />-->
            <attack-stage-text ref="atcAge" />
          </div>
          <div class="common-head-right">
            <el-row :gutter="10">
              <el-col :span="1.5">
                <el-col :span="1.5">
                  <el-button
                    v-hasPermi="['system:threadten:add']"
                    type="primary"
                    size="small"
                    @click="handleAdd"
                  >新增
                  </el-button>
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    class="btn1"
                    size="small"
                    @click="handleBlocking"
                  >批量阻断</el-button>
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    v-hasPermi="['system:threadten:import']"
                    class="btn1"
                    size="small"
                    @click="handleImport"
                  >导入
                  </el-button>
                </el-col>
                <el-button
                  v-hasPermi="['system:threadten:export']"
                  class="btn1"
                  size="small"
                  @click="handleExport"
                >导出
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
        <el-table
          ref="multipleTable"
          v-loading="loading"
          height="100%"
          :data="threatenWarnList"
          @row-click="handleRowClick"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <!--          <el-table-column type="index" width="100" label="序号"/>-->
          <el-table-column label="最近告警时间" width="200" prop="updateTime" />
          <el-table-column label="告警名称" prop="threatenName" min-width="260" />
          <el-table-column label="告警类型" prop="threatenType" width="150" />
          <el-table-column label="告警等级" prop="alarmLevel" width="150">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.threaten_type" :value="scope.row.alarmLevel" />
            </template>
          </el-table-column>
          <el-table-column label="源IP" prop="srcIp" width="180">
            <template slot-scope="scope">
              <div style="display: flex; align-items: center; justify-content: flex-start">
                <span>{{ scope.row.srcIp }}</span>
                <img v-if="scope.row.isBlocking" style="width: 24px;margin-left: 10px" src="@/assets/images/block.png" alt="">
              </div>
            </template>
          </el-table-column>
          <el-table-column label="目标IP/应用" width="150" prop="destIp" />
          <el-table-column label="处置人" prop="disposer" width="150" :formatter="disposerFormatter" />
          <el-table-column label="关联业务系统" prop="businessApplicationList" width="200">
            <template slot-scope="scope">
              <el-tooltip
                v-if="scope.row.businessApplications && scope.row.businessApplications.length > 0"
                placement="bottom-end"
                effect="light"
              >
                <div slot="content">
                  <div
                    v-for="(item,tagIndex) in scope.row.businessApplications"
                    v-if="tagIndex <= 5"
                    :key="item.assetId"
                    class="overflow-tag"
                  >
                    <el-tag type="primary"><span>{{ item.assetName }}</span></el-tag>
                  </div>
                  <div v-if="scope.row.businessApplications.length > 5">
                    <el-tag type="primary"><span>...</span></el-tag>
                  </div>
                </div>
                <el-tag type="primary" class="asset-tag">
                  <span>{{ handleApplicationTagShow(scope.row.businessApplications) }}</span></el-tag>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="通报状态" prop="flowState" width="150" :formatter="flowStateFormatter" />
          <el-table-column label="处置状态" prop="handleState" width="150" :formatter="handleStateFormatter" />
          <el-table-column label="所属部门" prop="deptName" width="150" />
          <el-table-column label="数据来源" prop="dataSource" width="150">
            <template slot-scope="scope">
              <span v-if="scope.row.dataSource == '1'">探测</span>
              <span v-else-if="scope.row.dataSource == '2'">手动</span>
              <span v-else-if="scope.row.dataSource == '8'">流量</span>
              <span v-else-if="scope.row.dataSource == '9'">探针</span>
            </template>
          </el-table-column>
          <el-table-column label="发现次数" prop="alarmNum" width="150" />
          <el-table-column label="同步状态" prop="synchronizationStatus" width="150">
            <template slot-scope="scope">
              <span v-if="scope.row.synchronizationStatus === '0'">未同步</span>
              <span v-else-if="scope.row.synchronizationStatus === '1'">已同步</span>
            </template>
          </el-table-column>
          <el-table-column label="攻击方向" prop="attackDirection" width="180">
            <template slot-scope="scope">
              <div style="display: flex; align-items: center; justify-content: space-between">
                <dict-tag :options="dict.type.attack_direction" :value="scope.row.attackDirection" />
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-refresh"
                  :loading="scope.row.refreshing"
                  title="刷新攻击方向"
                  style="margin-left: 8px; color: #409EFF;"
                  @click="handleRefreshAttackDirection(scope.row, $event)"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="250"
            fixed="right"
            :show-overflow-tooltip="false"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                v-hasPermi="['system:threadten:query']"
                size="mini"
                type="text"
                @click="handleDetail(scope.row)"
              >详情
              </el-button>
              <el-button
                v-if="scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')"
                v-hasPermi="['system:threadten:edit']"
                size="mini"
                type="text"
                @click="handleEdit(scope.row)"
              >编辑
              </el-button>
              <el-button
                v-if="scope.row.workId==null && !(scope.row.handleState === '3')"
                v-hasPermi="['system:threadten:remove']"
                size="mini"
                type="text"
                class="table-delBtn"
                @click="handleDelete(scope.row)"
              >删除
              </el-button>
              <el-button
                v-if="scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')"
                v-hasPermi="['system:threadten:edit']"
                size="mini"
                type="text"
                @click="showHandle(scope.row)"
              >处置
              </el-button>
              <el-button
                v-if="!(scope.row.handleState === '1' || scope.row.handleState === '3') && (scope.row.flowState == null || scope.row.flowState === '99')"
                size="mini"
                type="text"
                @click="addOrUpdateFlowHandle(null,null,scope.row)"
              >创建通报
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <!-- 处置威胁情报对话框! -->
    <el-dialog
      title="快速处置"
      :visible.sync="showHandleDialog"
      width="600px"
      append-to-body
    >
      <el-form ref="handleStateForm" :model="handleForm" :rules="handleRules" label-width="106px">
        <el-form-item label="处置状态" prop="handleState">
          <el-select v-model="handleForm.handleState" clearable placeholder="请选择处置状态">
            <el-option
              v-for="dict in dict.type.handle_state"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="处置说明" prop="handleDesc">
          <el-input v-model="handleForm.handleDesc" type="textarea" :rows="2" placeholder="请输入处置说明" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitHandleForm">确 定</el-button>
        <el-button @click="showHandleDialog = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 添加或修改威胁情报对话框! -->
    <el-dialog
      :title="title"
      :visible.sync="openThrenten"
      width="80%"
      append-to-body
      :before-close="handleClose"
    >
      <el-row>
        <el-form ref="form" :model="form" :rules="rules" label-width="106px" :disabled="!editable">
          <el-col :span="24" class="mb8">
            <el-divider direction="vertical" />
            <div class="my-title">基本信息</div>
          </el-col>
          <el-col :span="24">
            <el-form-item label="告警名称" prop="threatenName">
              <el-input v-model="form.threatenName" placeholder="请输入告警名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="告警等级" prop="alarmLevel">
              <el-select v-model="form.alarmLevel" placeholder="请选择告警等级" clearable>
                <el-option
                  v-for="dict in dict.type.threaten_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="告警类型" prop="threatenType">
              <el-cascader
                v-model="form.threatenType"
                :options="threatenDict"
                clearable
                placeholder="请选择告警类型"
                :props="{ label: 'dictLabel', value: 'dictValue' }"
                style="width: 100%"
              >
                <template slot-scope="{ node, data }">
                  <span>{{ data.dictLabel }}</span>
                  <span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
                </template>
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="告警原因" prop="reason">
              <el-input
                v-model="form.reason"
                :autosize="{minRows: 3, maxRows: 3}"
                type="textarea"
                placeholder="请输入告警原因"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="处置建议" prop="handSuggest">
              <el-input
                v-model="form.handSuggest"
                :autosize="{minRows: 3, maxRows: 3}"
                type="textarea"
                placeholder="请输入告警建议"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="告警时间" prop="createTime">
              <el-date-picker
                v-model="form.createTime"
                type="date"
                placeholder="选择告警时间"
                format="yyyy 年 MM 月 dd 日"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最近告警时间" prop="updateTime">
              <el-date-picker
                v-model="form.updateTime"
                type="date"
                placeholder="选择最近告警时间"
                format="yyyy 年 MM 月 dd 日"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="威胁标签" prop="label">
              <DynamicTag v-model="form.label" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联设备" prop="associaDevice">
              <el-input
                v-model="form.associaDevice"
                placeholder="请输入关联设备"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" class="mb8">
            <el-divider direction="vertical" />
            <div class="my-title">攻击关系</div>
          </el-col>
          <el-col :span="12">
            <el-form-item label="源IP" prop="srcIp">
              <el-input
                v-model="form.srcIp"
                style="width: 50%"
                placeholder="请输入源IP"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="源IP端口" prop="srcPort">
              <el-input
                v-model="form.srcPort"
                style="width: 30%"
                placeholder="请输入源IP端口"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标IP/应用" prop="destIp">
              <el-input
                v-model="form.destIp"
                style="width: 50%"
                placeholder="请输入目标IP"
              />
              <!--目标ip查询后有多个及显示资产数据，提交表单传(assetId： 资产id,deptId：部门id)-->
              <el-select
                v-show="assetInfoList.length >= 2"
                v-model="form.assetId"
                style="width: 50%;"
                placeholder="请确认疑似资产"
              >
                <el-option
                  v-for="item in assetInfoList"
                  :key="item.assetId"
                  :label="item.value"
                  :value="item.assetId"
                />
              </el-select>
              <el-select v-show="false" v-model="form.deptId" style="width: 50%;" placeholder="请选择资产">
                <el-option v-for="item in assetInfoList" :label="item.value" :value="item.deptId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标IP端口" prop="destPort">
              <el-input
                v-model="form.destPort"
                style="width: 30%"
                placeholder="请输入目标IP端口"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="form.fileUrl!=null||editable">
            <el-col :span="24" class="mb8">
              <el-divider direction="vertical" />
              <div class="my-title">文件上传</div>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上传文件" prop="fileUrl">
                <file-upload
                  v-model="form.fileUrl"
                  :dis-upload="!editable"
                  :limit="5"
                  :file-type="['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']"
                />
              </el-form-item>
            </el-col>
          </el-col>
        </el-form>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="editable"
          type="primary"
          @click="submitForm"
        >确 定
        </el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-if="openDialog"
      :title="title"
      :visible.sync="openDialog"
      width="80%"
      append-to-body
    >
      <el-tabs v-model="activeName">
        <el-tab-pane label="事件详情" name="detail">
          <alarm-detail
            v-if="openDialog"
            :asset-data="assetData"
            @openDetail="openDetail"
          />
        </el-tab-pane>
        <el-tab-pane v-if="assetData.srcIp" label="攻击IP关联事件" name="attack">
          <attack-detail :detail-type="'attack'" :host-ip="assetData.srcIp" :current-asset-data="assetData" />
        </el-tab-pane>
        <el-tab-pane v-if="assetData.destIp" label="受害IP关联事件" name="suffer">
          <suffer-detail :detail-type="'suffer'" :host-ip="assetData.destIp" :current-asset-data="assetData" />
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <el-dialog title="导入威胁告警" :visible.sync="importDialog" width="800px" append-to-body>
      <import-threaten v-if="importDialog" @closeDialog="closeDialog" />
    </el-dialog>

    <el-dialog title="查看服务器资产" :visible.sync="serverOpen" width="80%" append-to-body>
      <server-add v-if="serverOpen" :asset-id="assetId" :editable="editable" @cancel="closeAssetDialog()" />
    </el-dialog>

    <el-dialog title="查看安全设备资产" :visible.sync="safeOpen" width="80%" append-to-body>
      <safe-add v-if="safeOpen" :asset-id="assetId" :editable="editable" @cancel="closeAssetDialog()" />
    </el-dialog>

    <el-dialog title="查看告警策略" :visible.sync="viewStrategy" width="400" append-to-body>
      <view-strategy v-if="viewStrategy" @close="viewStrategy=false" />
    </el-dialog>

    <el-dialog title="告警策略配置" :visible.sync="threatenConfigFlag" width="800" append-to-body>
      <threaten-config-list v-if="threatenConfigFlag" @close="threatenConfigFlag=false" />
    </el-dialog>

    <el-dialog title="批量阻断" :visible.sync="blockingDialogVisible" width="400">
      <el-form ref="blockingForm" :model="blockingForm" :rules="blockingRules" class="blocking-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="阻断ip" prop="block_ip">
              <span slot="label">
                阻断ip
                <template>
                  <el-tooltip placement="top">
                    <div slot="content">默认加载选择的事件的源IP，多个则以“;”隔开</div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </template>
              </span>
              <el-input v-model="blockingForm.block_ip" placeholder="请输入ip">
                <el-popover
                  slot="suffix"
                  placement="bottom"
                  width="100"
                  trigger="hover"
                >
                  <ul>
                    <li v-for="(ip, index) in blockingIpList" :key="index">{{ ip }}</li>
                  </ul>
                  <i slot="reference" class="el-icon-more" />
                </el-popover>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="阻断时长" prop="duration_time">
              <el-select v-model="blockingForm.duration_time" placeholder="请选择阻断时长">
                <el-option
                  v-for="item in blockingDuration"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="备注" prop="remarks">
            <el-input v-model="blockingForm.remarks" type="textarea" maxlength="500" show-word-limit placeholder="请输入阻断描述" />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="blockingSubmit">确 定</el-button>
        <el-button @click="blockingDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <FlowBox v-if="flowVisible" ref="FlowBox" @close="closeFlow" />
    <flow-template-select :show.sync="flowTemplateSelectVisible" @change="flowTemplateSelectChange" />

    <publish-click-dialog
      :publish-dialog-visible="publishDialogVisible"
      title="发布告警事件"
      width="30%"
      @updateVisible="(val) => { this.publishDialogVisible = val}"
    />
  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import { getMulTypeDict } from '../../../../api/system/dict/data'
import { getDeptSystem } from '../../../../api/monitor2/applicationAssets'
import { getAlarm, delAlarm, listAlarm, addAlarm, updateAlarm, addBlockIp, refreshAttackDirection } from '../../../../api/threaten/threatenWarn'
import { getAssetInfoByIp } from '../../../../api/safe/overview'
import DynamicTag from '../../../../components/DynamicTag'
import AlarmDetail from '../../../basis/securityWarn/alarmDetail'
import importThreaten from '@/views/basis/securityWarn/importThreaten.vue'
import ThreatenConfigList from '@/views/basis/securityWarn/threatenConfigList.vue'
import ServerAdd from '../../../hhlCode/component/application/adds/serverAdd'
import SafeAdd from '../../../hhlCode/component/application/adds/safeAdd'
import ViewStrategy from '../../../basis/securityWarn/viewStrategy'
import PublishClickDialog from '../../../basis/securityWarn/publishClickDialog'
import FlowBox from '../../../zeroCode/workFlow/components/FlowBox'
import FlowTemplateSelect from '../../../../components/FlowTemplateSelect'
import AttackStage from '../../../threat/overview/attackStage'
import AttackViewList from './attackViewList'
import SufferViewList from './sufferViewList'
import attackDetail from './detail/index.vue'
import sufferDetail from './detail/index.vue'
import DeptSelect from '@/views/components/select/deptSelect.vue'
import { uniqueArr } from '@/utils'
import { FlowEngineInfo } from '@/api/lowCode/FlowEngine'
import { listUser } from '@/api/system/user'
import AttackStageText from '@/views/threat/overview/attackStageText.vue'
import {
  listDeviceConfig
} from '@/api/ffsafe/deviceConfig'

export default {
  name: 'EventList',
  components: {
    AttackStageText,
    DeptSelect,
    SufferViewList,
    AttackViewList,
    AttackStage,
    FlowTemplateSelect,
    FlowBox,
    PublishClickDialog,
    attackDetail,
    sufferDetail,
    ThreatenConfigList, ViewStrategy, SafeAdd, ServerAdd, importThreaten, AlarmDetail, DynamicTag
  },
  dicts: ['threaten_type', 'attack_stage', 'attack_result', 'handle_state', 'synchronization_status', 'attack_direction'],
  props: {
    propsActiveName: {
      type: String
    },
    propsQueryParams: {
      type: Object,
      default: function() {
        return null
      }
    },
    currentBtn: {
      type: Number,
      default: null
    }
  },
  data() {
    const validateBlockIp = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('IP不能为空'))
      }
      // let pattern = /^((1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\d){1,2})\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\d){1,2}|0)\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\d){1,2}|0)\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\d){1,2}|0))$/;
      const pattern = /^\s*((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\s*;\s*((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))*\s*$/
      if (!pattern.test(value)) {
        return callback(new Error('请输入正确的IP'))
      }
      return callback()
    }
    return {
      userList: [],
      showHandleDialog: false,
      handleForm: {
        id: '',
        handleDesc: '',
        handleState: ''
      },
      handleRules: {
        handleState: [
          { required: true, message: '请选择处理状态', trigger: 'blur' }
        ]
      },
      showAll: false,
      threatenDict: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        handleState: '0'
      },
      deptOptions: [],
      rangeTime: [],
      loading: false,
      threatenWarnList: [],
      total: 0,
      title: '',
      openThrenten: false,
      form: {},
      rules: {
        threatenName: [
          { required: false, min: 0, max: 500, message: '告警名称不能超过500字符', trigger: 'blur' },
          { required: true, message: '请输入告警名称', trigger: 'blur' },
          {
            required: true,
            pattern: /^[^\s]+/,
            message: '不能以空格开头！',
            trigger: 'blur'
          }
        ],
        alarmLevel: [
          { required: true, message: '请输入告警等级', trigger: 'blur' }
        ],
        threatenType: [
          { required: true, message: '请输入告警类型', trigger: 'blur' }
        ],
        reason: [
          { required: false, min: 0, max: 2000, message: '告警原因不能超过2000字符', trigger: 'blur' },
          { required: true, message: '请输入告警原因', trigger: 'blur' }
        ],
        handSuggest: [
          { required: false, min: 0, max: 2000, message: '告警建议不能超2000字符', trigger: 'blur' },
          { required: true, message: '请输入告警建议', trigger: 'blur' }
        ],
        logTime: [
          { required: true, message: '请输入日志时间', trigger: 'blur' }
        ],
        createTime: [
          { required: true, message: '请输入告警时间', trigger: 'blur' }
        ],
        srcIp: [
          { required: false, min: 0, max: 30, message: '源IP不能超过30字符', trigger: 'blur' },
          {
            required: true,
            pattern: '^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}$',
            message: 'IP地址不能为空或格式不正确',
            trigger: 'blur'
          }
        ],
        srcPort: [
          { required: false, min: 0, max: 11, message: '源IP端口不能超过11字符', trigger: 'blur' },
          { required: true, pattern: '^[0-9]*[1-9][0-9]*$', message: '源IP端口不能为空或格式不正确', trigger: 'blur' }
        ],
        destIp: [
          { required: false, min: 0, max: 30, message: '目标IP不能超过30字符', trigger: 'blur' },
          {
            required: true,
            pattern: '^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}$',
            message: 'IP地址不能为空或格式不正确',
            trigger: 'blur'
          }
        ],
        destPort: [
          { required: false, min: 0, max: 11, message: '目标IP端口不能超过11字符', trigger: 'blur' },
          { required: true, pattern: '^[0-9]*[1-9][0-9]*$', message: '目标IP端口不能为空或格式不正确', trigger: 'blur' }
        ],
        mateRule: [
          { required: false, min: 0, max: 200, message: '分析规则不能超过200字符', trigger: 'blur' }
        ],
        associaDevice: [
          { required: false, min: 0, max: 200, message: '关联设备不能超过200字符', trigger: 'blur' }
        ],
        attackType: [
          { required: false, min: 0, max: 100, message: '攻击方式不能超过100字符', trigger: 'blur' },
          { required: true, message: '请输入攻击方式', trigger: 'blur' }
        ],
        attackStage: [
          { required: false, min: 0, max: 100, message: '攻击链阶段不能超过100字符', trigger: 'blur' },
          { required: true, message: '请输入攻击链阶段', trigger: 'blur' }
        ],
        attackResult: [
          { required: false, min: 0, max: 100, message: '攻击结果不能超过100字符', trigger: 'blur' },
          { required: true, message: '请输入攻击结果', trigger: 'blur' }
        ]
      },
      blockingForm: {},
      blockingRules: {
        block_ip: [
          // 可同时传多个，用";"隔开
          { validator: validateBlockIp, trigger: 'blur' }
        ],
        duration_time: [
          { required: true, message: '请选择阻断时长', trigger: 'blur' }
        ],
        remarks: [
          { required: false, min: 0, max: 500, message: '备注不能超过500字符', trigger: 'blur' }
        ]
      },
      blockingIpList: [],
      blockingDialogVisible: false, // 批量阻断弹窗
      editable: true,
      assetInfoList: [],
      openDialog: false,
      assetData: {},
      importDialog: false,
      serverOpen: false,
      assetId: null,
      safeOpen: false,
      threatenConfigFlag: false,
      viewStrategy: false,
      publishDialogVisible: false,
      flowVisible: false,
      flowTemplateSelectVisible: false,
      flowStateOptions: [
        {
          label: '待审核',
          value: 0
        },
        {
          label: '待处置',
          value: 1
        },
        {
          label: '待反馈审核',
          value: 2
        },
        {
          label: '待验证',
          value: 3
        },
        {
          label: '已完成',
          value: 4
        },
        {
          label: '待提交',
          value: -1
        },
        {
          label: '未分配',
          value: 99
        }
      ],
      handleStateOptions: [
        {
          label: '未处置',
          value: '0'
        },
        {
          label: '已处置',
          value: '1'
        },
        {
          label: '忽略',
          value: '2'
        },
        {
          label: '处置中',
          value: '3'
        }
      ],
      activeName: 'detail',
      syncStateOptions: [
        {
          label: '未同步',
          value: 0
        },
        {
          label: '已同步',
          value: 1
        }
      ],
      blockingDuration: [
        {
          label: '30分钟',
          value: '30m'
        },
        {
          label: '24小时',
          value: '24h'
        },
        {
          label: '48小时',
          value: '48h'
        },
        {
          label: '7天',
          value: '168h'
        },
        {
          label: '永久',
          value: '永久'
        }
      ],
      multipleSelection: [],
      deviceConfigList: []
    }
  },
  watch: {
    // 监听目标ip
    'form.destIp'(value, oldValue) {
      var rg = /^(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}$/
      var reg = rg.test(value)
      if (reg) {
        // 根据ip获取资产数据
        getAssetInfoByIp(value).then(response => {
          if (response.data.length) {
            const assetData = response.data
            assetData.forEach(item => item.value = item.assetName + '-' + item.assetTypeDesc)
            if (value !== oldValue && oldValue) {
              this.form.assetId = ''
              this.form.deptId = ''
            }
            // 资产数据有多条显示下拉框，只有一条不显示
            if (assetData.length === 1) {
              this.form.assetId = assetData[0].assetId
              this.form.deptId = assetData[0].deptId
            }
            if (assetData.length > 1 && !this.form.assetId) {
              this.form.assetId = ''
              this.form.deptId = ''
            }
            this.assetInfoList = assetData
          } else {
            this.assetInfoList = []
            return this.$message.warning('未查询到资产数据')
          }
        })
      } else {
        this.assetInfoList = []
        this.form.assetId = ''
        this.form.deptId = ''
      }
    },
    propsActiveName() {
      this.init()
    },
    propsQueryParams: {
      handler(val) {
        this.handlePropsQuery(val)
      }
    },
    /* rangeTime(val) {
      console.log(val)
    },*/
    'blockingForm.block_ip': {
      handler(value) {
        if (value) {
          this.blockingIpList = value.split(';').map(ip => ip.trim()).filter(ip => ip)
        }
      },
      immediate: true
    }
  },
  created() {
    this.getDeviceConfigList()
  },
  mounted() {
    if (!this.$route.query || Object.keys(this.$route.query).length < 1) {
      this.init()
    } else {
      this.handlePropsQuery(this.$route.query)
    }
  },
  methods: {
    init() {
      // this.resetQuery()
      this.getThreatenDict()
      this.handleQuery()
      this.getDeptsData()
      this.getUserList()
    },
    getUserList() {
      listUser({ pageNum: 1, pageSize: 1000 }).then(res => {
        if (res.rows) {
          this.userList = res.rows
        }
      })
    },
    handleQuery() {
      this.propsQueryParams.alarmLevel = this.queryParams.alarmLevel
      this.propsQueryParams.referenceId = this.queryParams.referenceId
      // this.$emit('update:currentBtn',this.queryParams.alarmLevel?parseInt(this.queryParams.alarmLevel) : null)
      this.queryParams = { ...this.queryParams, ...this.propsQueryParams }
      if (this.rangeTime != null) {
        this.queryParams.startTime = parseTime(this.rangeTime[0])
        this.queryParams.endTime = parseTime(this.rangeTime[1])
      } else {
        this.queryParams.startTime = null
        this.queryParams.endTime = null
      }
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10

      if (!this.queryParams.startTime) {
        this.queryParams.startTime = parseTime(new Date().setHours(-168, 0, 0, 0), '{y}-{m}-{d} 00:00:00') // 一周前，时间部分为 00:00:00
      }
      if (!this.queryParams.endTime) {
        this.queryParams.endTime = parseTime(new Date().setHours(23, 59, 59, 999), '{y}-{m}-{d} 23:59:59') // 当前日期，时间部分为 23:59:59
      }
      this.rangeTime = [this.queryParams.startTime, this.queryParams.endTime]
      this.total = 0
      this.getList()
      this.$nextTick(() => {
        const data = JSON.parse(JSON.stringify(this.queryParams))
        if (data.threatenType != null) {
          data.threatenType = data.threatenType.join('/')
        }
        this.$refs.atcAge.initAttackStage(data)
      })
    },
    // 获取告警类型多级字典数据
    getThreatenDict() {
      getMulTypeDict({
        dictType: 'threaten_alarm_type'
      }).then(res => {
        this.threatenDict = res.data
      })
    },
    // 获取部门数据
    getDeptsData() {
      getDeptSystem().then(res => this.deptOptions = res.data)
    },
    handleChange(val) {
      // 获取所属部门最后id
      if (val) {
        this.queryParams.deptId = val[val.length - 1]
      } else {
        this.queryParams.deptId = ''
      }
    },
    resetQuery() {
      this.queryParams = {
        threatenName: null,
        threatenType: null,
        alarmLevel: null,
        referenceId: null,
        srcIp: null,
        destIp: null,
        handleState: '0',
        flowState: null,
        updateTime: null,
        attackDirection: null,
        pageNum: 1,
        pageSize: 10
      }
      const atcAge = this.$refs.atcAge
      if (atcAge) {
        atcAge.currentSelectedCard = null
      }
      this.rangeTime = null
      this.handleQuery()
    },
    // 新增威胁情报
    handleAdd() {
      this.openThrenten = true
      this.form = {}
      this.editable = true
      this.title = '新增威胁情报'
      this.$set(this.form, 'assetId', '') // 解决el-select无法视图与数据的更新
    },
    // 导入功能
    handleImport() {
      this.importDialog = true
    },
    handleExport() {
      this.download(
        '/system/threadten/export',
        {
          ...this.queryParams
        },
        `威胁告警_${new Date().getTime()}.xlsx`
      )
    },

    // 获取列表数据查询
    handleRowClick(row, column, event) {
      // 获取告警详情单个单元格数据进行筛选
      if (row && row.id) {
        if (column.property) {
          if (column.property === 'flowState') {
            this.queryParams[column.property] = !row[column.property] ? 99 : Number(row[column.property])
            listAlarm({
              [column.property]: !row[column.property] ? 99 : Number(row[column.property]),
              pageNum: 1,
              pageSize: 10,
              startTime: parseTime(this.rangeTime[0]),
              endTime: parseTime(this.rangeTime[1])
            }).then(response => {
              this.threatenWarnList = response.rows
              this.threatenWarnList.forEach(item => {
                item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc
                if (item.assetType == 'null-null') {
                  item.assetType = null
                }
              })
              this.total = response.total
              this.loading = false
            })
            return
          } else if (column.property === 'threatenType') {
            this.queryParams[column.property] = row[column.property].split('/')
          } else if (column.property === 'alarmLevel') {
            this.queryParams[column.property] = row[column.property].toString()
          } else {
            this.queryParams[column.property] = row[column.property]
          }
          listAlarm({
            [column.property]: row[column.property],
            pageNum: 1,
            pageSize: 10,
            startTime: parseTime(this.rangeTime[0]),
            endTime: parseTime(this.rangeTime[1])
          }).then(response => {
            this.threatenWarnList = response.rows
            this.threatenWarnList.forEach(item => {
              item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc
              if (item.assetType == 'null-null') {
                item.assetType = null
              }
            })
            this.total = response.total
            this.loading = false
          })
        }
      }
    },

    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },

    flowStateFormatter(row, column, cellValue, index) {
      let name = '未分配'
      const match = this.flowStateOptions.find(item => item.value == cellValue)
      if (match) {
        name = match.label
      }
      return name
    },
    disposerFormatter(row, column, cellValue, index) {
      let name = ''
      if (cellValue) {
        this.userList.forEach(e => {
          if (e.userId == cellValue) {
            name = e.nickName
          }
        })
        return name
      }
      return name
    },

    handleStateFormatter(row, column, cellValue, index) {
      let name = '未处置'
      const match = this.handleStateOptions.find(item => item.value == cellValue)
      if (match) {
        name = match.label
      }
      return name
    },
    handleDetail(row) {
      this.assetData = { ...row }
      this.title = '查看告警详情'
      this.openDetail(true)
    },
    showHandle(row) {
      // 获取事件详情单个单元格数据进行筛选
      if (row.handleState === '1' || row.handleState === '2') {
        this.handleForm.handleState = parseInt(row.handleState)
        this.handleForm.handleDesc = row.handleDesc
      } else {
        this.handleForm = {
          handleDesc: '',
          handleState: ''
        }
      }
      this.handleForm.id = row.id
      this.showHandleDialog = true
    },
    handleEdit(row) {
      getAlarm(row.id).then(res => {
        this.form = { ...res.data }
        if (this.form.alarmLevel != null) {
          this.form.alarmLevel = (this.form.alarmLevel).toString()
        }
        if (this.form.threatenType != null) {
          this.form.threatenType = this.form.threatenType.split('/')
        }
        if (this.form.attackNum != null) {
          this.form.attackNum = (this.form.attackNum).toString()
        }
        if (this.form.srcPort != null) {
          this.form.srcPort = (this.form.srcPort).toString()
        }
        if (this.form.destPort != null) {
          this.form.destPort = (this.form.destPort).toString()
        }
        this.title = '修改威胁情报'
        this.openThrenten = true
      })
    },
    handleDelete(row) {
      const ids = row.id
      const title = row.threatenName
      this.$modal.confirm('是否确认删除告警名称为【' + title + '】的数据项?').then(() => {
        return delAlarm(ids)
      }).then(() => {
        this.$message.success('删除成功')
        this.getList()
      }).catch(() => {

      })
    },
    addOrUpdateFlowHandle(id, flowState, row) {
      const data = {
        id: id || '',
        formType: 1,
        opType: flowState ? 0 : '-1',
        status: flowState,
        row: row,
        isWork: true
      }
      data.row.workType = '2'
      data.row.eventType = 3
      data.originType = 'event'
      this.currentFlowData = data
      this.loading = true
      this.getConfigKey('default.flowTemplateId').then(res => {
        const flowId = res.msg
        if (flowId) {
          this.getFlowEngineInfo(flowId)
        } else {
          this.flowTemplateSelectVisible = true
        }
      }).finally(() => {
        this.loading = false
      })
    },
    getFlowEngineInfo(val) {
      FlowEngineInfo(val).then(res => {
        if (res.data && res.data.flowTemplateJson) {
          const data = JSON.parse(res.data.flowTemplateJson)
          if (!data[0].flowId) {
            this.$message.error('该流程模板异常,请重新选择')
          } else {
            this.currentFlowData.flowId = data[0].flowId
            this.flowVisible = true
            this.$nextTick(() => {
              this.$refs.FlowBox.init(this.currentFlowData)
            })
          }
        }
      }).finally(() => {
        this.loading = false
      })
    },
    getList() {
      this.loading = true
      const queryParams = {
        ...this.queryParams
      }
      if (queryParams.threatenType != null) {
        queryParams.threatenType = queryParams.threatenType.join('/')
      }
      // 同步请求类型统计数据
      this.$emit('getList', { ...queryParams })
      listAlarm(queryParams).then(response => {
        this.threatenWarnList = response.rows
        this.threatenWarnList.forEach(item => {
          item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc
          if (item.assetType == 'null-null') {
            item.assetType = null
          }
          if (item.deptName) {
            const deptNameArr = uniqueArr(item.deptName.split(','))
            item.deptName = deptNameArr.join(',')
          }
        })
        this.total = response.total
        this.loading = false
      })
    },
    handleClose(done) {
      done()
      this.form = {}
      this.$refs.form.resetFields()
    },
    submitHandleForm() {
      this.$refs['handleStateForm'].validate(valid => {
        if (valid) {
          updateAlarm(this.handleForm).then(res => {
            this.$message.success('处置成功')
            this.handleForm = {}
            this.showHandleDialog = false
            this.getList()
          })
        }
      })
    },
    submitForm() {
      if (this.form.threatenType != null) {
        this.form.threatenType = this.form.threatenType.join('/')
      }
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id == null) {
            addAlarm(this.form).then(res => {
              this.$message.success('新增成功')
              this.form = {}
              this.openThrenten = false
              this.getList()
            })
          } else {
            updateAlarm(this.form).then(res => {
              this.$message.success('修改成功')
              this.form = {}
              this.openThrenten = false
              this.getList()
            })
          }
        }
      })
    },
    cancel() {
      this.openThrenten = false
      this.$refs.form.resetFields()
    },
    openDetail(val) {
      this.openDialog = val
    },
    closeDialog() {
      this.importDialog = false
      this.handleQuery()
    },
    closeAssetDialog() {
      this.serverOpen = false
      this.safeOpen = false
      this.networkOpen = false
    },
    closeFlow(isrRefresh) {
      this.flowVisible = false
      if (isrRefresh) this.getList()
    },
    flowTemplateSelectChange(val) {
      this.flowTemplateSelectVisible = false
      this.flowVisible = true
      this.currentFlowData.flowId = val
      this.$nextTick(() => {
        this.$refs.FlowBox.init(this.currentFlowData)
      })
    },
    handleAtcAgeClick(atcAge) {
      this.queryParams.attackSeg = atcAge
      this.handleQuery()
    },
    handlePropsQuery(val) {
      if (val && Object.keys(val).length > 0) {
        if (val.attackSeg && this.$refs.atcAge) {
          this.$refs.atcAge.currentSelectedCard = val.attackSeg
        }
        this.queryParams = val
        if (val.startTime && val.endTime) {
          this.rangeTime = [val.startTime, val.endTime]
        }
        if (val.handle == '1') {
          this.queryParams.handleState = '1'
        } else if (val.handle == '0') {
          this.queryParams.handleState = '0'
        }
        if (val.datasource) {
          this.queryParams.dataSource = parseInt(val.datasource)
        }
        this.getThreatenDict()
        this.getDeptsData()
        this.handleQuery()
      }
    },
    handleApplicationTagShow(applicationList) {
      if (!applicationList || applicationList.length < 1) {
        return ''
      }
      let result = applicationList[0].assetName
      if (applicationList.length > 1) {
        result += '...'
      }
      return result
    },

    handleBlocking() {
      if (this.multipleSelection.length < 1) return this.$message.warning('请选择要阻断的ip')
      this.blockingDialogVisible = true
      let arr = this.multipleSelection.map(item => item.srcIp)
      arr = Array.from(new Set(arr))
      this.$set(this.blockingForm, 'block_ip', arr.join(';'))
    },
    blockingSubmit() {
      this.$refs['blockingForm'].validate(valid => {
        if (valid) {
          addBlockIp(this.blockingForm).then(res => {
            this.$message.success('添加成功')
          }).finally(() => {
            this.blockingDialogVisible = false
            this.$refs.multipleTable.clearSelection()
            this.multipleSelection = []
          })
        }
      })
    },
    getDeviceConfigList() {
      listDeviceConfig({ queryAllData: true }).then(res => {
        this.deviceConfigList = res.rows
      })
    },

    // 刷新攻击方向
    handleRefreshAttackDirection(row, event) {
      // 阻止事件冒泡，避免触发行点击事件
      if (event) {
        event.stopPropagation()
      }

      // 防止重复点击
      if (row.refreshing) {
        return
      }

      this.$set(row, 'refreshing', true)

      refreshAttackDirection([row.id]).then(response => {
        if (response.code === 200 && response.data && response.data.length > 0) {
          // 更新当前行的攻击方向
          const refreshResult = response.data[0]
          this.$set(row, 'attackDirection', refreshResult.attackDirection)
          this.$message.success('攻击方向刷新成功')
        } else {
          this.$message.error('攻击方向刷新失败')
        }
      }).catch(error => {
        console.error('刷新攻击方向失败:', error)
        this.$message.error('攻击方向刷新失败：' + (error.message || '未知错误'))
      }).finally(() => {
        this.$set(row, 'refreshing', false)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-divider {
  background: #0E94EA;
}

.el-divider--vertical {
  display: inline-block;
  width: 5px;
  height: 2em;
  margin: 0 8px 0 0;
  vertical-align: middle;
  position: relative;
}

.my-title {
  display: inline-block;
  vertical-align: center;
}

.asset-tag {
  margin-left: 5px;
}

.asset-tag {
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
}

.el-tooltip__popper {
  font-size: 12px;
  max-width: 300px;
}

.overflow-tag:not(:first-child) {
  margin-top: 5px;
}
.blocking-form {
  ::v-deep .el-form-item__label {
    float: none;
  }
}

::v-deep .el-tabs__content {
  overflow: hidden;
}
::v-deep .el-dialog__body {
  max-height: 80vh;
  overflow-y: auto;
}
</style>
