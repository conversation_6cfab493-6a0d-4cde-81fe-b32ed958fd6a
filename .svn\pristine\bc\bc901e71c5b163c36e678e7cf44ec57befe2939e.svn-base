import request from '@/utils/request'

// 查询威胁情报列表
export function listAlarm(query) {
  return request({
    url: '/system/threadten/list',
    method: 'get',
    params: query
  })
}

// 查询威胁情报详细
export function getAlarm(id) {
  return request({
    url: '/system/threadten/' + id,
    method: 'get'
  })
}

// 新增威胁情报
export function addAlarm(data) {
  return request({
    url: '/system/threadten',
    method: 'post',
    data: data
  })
}

// 修改威胁情报
export function updateAlarm(data) {
  return request({
    url: '/system/threadten',
    method: 'put',
    data: data
  })
}

// 删除威胁情报
export function delAlarm(ids) {
  return request({
    url: '/system/threadten/' + ids,
    method: 'delete'
  })
}

//增加阻断IP
export function addBlockIp(data) {
  return request({
    url: '/system/threadten/addBlockIp',
    method: 'post',
    data: data
  })
}

// 刷新攻击方向
export function refreshAttackDirection(alarmIds) {
  return request({
    url: '/system/threadten/refreshAttackDirection',
    method: 'post',
    data: alarmIds
  })
}
