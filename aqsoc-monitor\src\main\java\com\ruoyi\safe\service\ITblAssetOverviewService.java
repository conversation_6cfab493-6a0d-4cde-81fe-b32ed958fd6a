package com.ruoyi.safe.service;

import com.ruoyi.safe.domain.AssetClass;
import com.ruoyi.safe.domain.TblAssetOverview;
import com.ruoyi.safe.vo.AssetClassVo;
import com.ruoyi.safe.vo.AssetPropertyVO;
import com.ruoyi.safe.vo.AssetTypeVo;
import com.ruoyi.safe.vo.chart_vo.AssetChartVo;

import java.util.List;

/**
 * 资产总表Service接口
 *
 * <AUTHOR>
 * @date 2022-11-07
 */
public interface ITblAssetOverviewService
{
    /**
     * 查询资产总表
     *
     * @param id 资产总表主键
     * @return 资产总表
     */
    public Integer selectIsServer(Long id);

    /**
     * 查询资产总表
     *
     * @param assetId 资产主键
     * @return 资产总表
     */
    public TblAssetOverview selectTblAssetOverviewByAssetId(Long assetId);

    /**
     * 查询资产总表
     *
     * @param assetIds 资产主键
     * @return 资产总表
     */
    public List<TblAssetOverview> selectTblAssetOverviewByAssetIds(Long[] assetIds);

    public List<TblAssetOverview> selectTblAssetOverviewByAssetIdsAndAssetOverView( List assetIds, TblAssetOverview assetOverview);

    /**
     * 查询资产总表列表
     *
     * @param tblAssetOverview 资产总表
     * @return 资产总表集合
     */
    List<TblAssetOverview> selectAllList(TblAssetOverview tblAssetOverview);
    public List<TblAssetOverview> selectTblAssetOverviewList(TblAssetOverview tblAssetOverview);
    public List<TblAssetOverview> selectTblAssetOverviewAssetClass(Long[] assetClass);

    /**
     * 查询资产总表，过滤虚拟系统已添加资产
     *
     * @param assetClassVo 资产分类
     * @return 资产总表集合
     */
    public List<TblAssetOverview> selectTblAssetOverviewAndBroadSystem(AssetClassVo assetClassVo);
    public List<TblAssetOverview> selectTblAssetOverviewAndBoundary(AssetClassVo assetClassVo);

    public List<TblAssetOverview> selectAssetsBySysAndType(AssetTypeVo assetTypeVo);

    /**
     * 查询资产总数
     *
     * @param tblAssetOverview 资产总表
     * @return 资产总数
     */
    public int selectTblAssetOverviewCount(TblAssetOverview tblAssetOverview);

    public Integer countByClass(Long classid);

    /**
     * 检查资产编码唯一性
     *
     * @param tblAssetOverview 资产总表
     * @return true：唯一 false：不唯一
     */
    public boolean checkAssetCodeUnique(TblAssetOverview tblAssetOverview);

    /**
     * 新增资产总表
     *
     * @param tblAssetOverview 资产总表
     * @return 结果
     */
    public int insertTblAssetOverview(TblAssetOverview tblAssetOverview);

    /**
     * 修改资产总表
     *
     * @param tblAssetOverview 资产总表
     * @return 结果
     */
    public int updateTblAssetOverview(TblAssetOverview tblAssetOverview);

    /**
     * 删除资产总表信息
     *
     * @param assetId 资产总表主键
     * @return 结果
     */
    public int deleteTblAssetOverviewById(Long assetId);

    /**
     * 批量删除资产总表
     *
     * @param assetIds 需要删除的资产总表主键集合
     * @return 结果
     */
    public int deleteTblAssetOverviewByIds(Long[] assetIds);

    /**
     * 查询资产详细信息
     *
     * @param assetId 资产总表主键
     * @return 结果
     */
    public TblAssetOverview selectAssetInfo(Long assetId);
    public List<? extends TblAssetOverview> selectAssetInfoByClassAndName(TblAssetOverview overview);

    /**
     * 查询资产属性关系
     */
    public AssetPropertyVO selectAssetProperty(TblAssetOverview tblAssetOverview);

    public AssetChartVo getAssetChartVo(Long assetId);

    public AssetChartVo getAssetChartVoByDim(Long assetId, String[] dimList);

    List<TblAssetOverview> selectInfoByAssetIds(Long[] assetIds);

    TblAssetOverview selectInfoByAssetId(Long assetId);
    List<TblAssetOverview> selectDetailsTblAssetOverview(List<Long> assetIds);
    /* 根据ip获取资产数据 */
    List<TblAssetOverview> selectTblAssetInfoByIP(String ip);

    List<AssetClass> selectedAssetTypeChildrenByIdAPid(Long type);

    List<TblAssetOverview> selectTblAssetOverviewListNotRole(TblAssetOverview tblAssetOverview);

    /**
     * 根据资产ID获取位置全名
     *
     * @param assetId 资产ID
     * @return 位置全名（多个位置用分号分隔）
     */
    String getLocationFullNameByAssetId(Long assetId);
}
