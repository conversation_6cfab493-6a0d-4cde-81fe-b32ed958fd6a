package com.ruoyi.safe.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.safe.domain.TblAssetOverview;
import com.ruoyi.safe.vo.AssetClassVo;
import com.ruoyi.safe.vo.AssetTypeVo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;

/**
 * 资产总表Mapper接口
 *
 * <AUTHOR>
 * @date 2022-11-07
 */
public interface TblAssetOverviewMapper
{
    /**
     * 判断是不是服务器
     *
     * @param id 资产总表主键
     * @return 资产总表
     */
    public Integer selectIsServer(Long id);

    public TblAssetOverview selectTblAssetOverviewByAssetId(Long assetId);

    /**
     * 查询资产总表列表
     *
     * @param tblAssetOverview 资产总表
     * @return 资产总表集合
     */
    public List<TblAssetOverview> selectTblAssetOverviewList(TblAssetOverview tblAssetOverview);
    public List<TblAssetOverview> selectTblAssetOverviewAssetClass(@Param("assetClass") Long[] assetClass);
    public List<TblAssetOverview> selectTblAssetOverviewByAssetIdsAndAssetOverView(@Param("assetIds") List<Long> assetIds,@Param("assetOverview") TblAssetOverview assetOverview);
    /**
     * 资产检索
     */
    public List<TblAssetOverview> selectTblAssetOverviewretRieval(@Param("search")String search,@Param("assetClass")List<Long> assetClass);

    /**
     * 查询资产总表，过滤虚拟系统已添加资产
     *
     * @param assetClassVo 资产分类
     * @return 资产总表集合
     */
    public List<TblAssetOverview> selectTblAssetOverviewAndBroadSystem(AssetClassVo assetClassVo);
    public List<TblAssetOverview> selectTblAssetOverviewAndBoundary(AssetClassVo assetClassVo);

    public List<TblAssetOverview> selectTblAssetOverviewByAssetIds(@Param("assetIds") List<Long> assetIds);

    /**
     * 查询资产总数
     *
     * @param tblAssetOverview 资产总表
     * @return 资产总数
     */
    public int selectTblAssetOverviewCount(TblAssetOverview tblAssetOverview);

    public Integer countByClass(Long classid);

    /**
     * 查询位置信息通过资产ID
     *
     * @param assetId 资产ID
     * @return 资产总表
     */
    public TblAssetOverview selectInfoByAssetId(Long assetId);

    /**
     * 根据网络信息查询资产数据
     * @param ip
     * @return
     */
    public List<TblAssetOverview> selectAssetByIP(String ip);

    /**
     * 查询位置信息通过资产ID
     *
     * @param assetIds 资产ID数组
     * @return 资产总表集合
     */
    public List<TblAssetOverview> selectInfoByAssetIds(@Param("assetIds") Long[] assetIds);

    /**
     * 检查资产编码唯一性
     *
     * @param assetCode 资产编码
     * @return 结果
     */
    public TblAssetOverview checkAssetCodeUnique(String assetCode);

    /**
     * 批量检查资产编码是否存在
     *
     * @param assetCodes 资产编码集合
     * @return 已存在的资产编码集合
     */
    public List<String> checkBatchAssetCodesExist(@Param("assetCodes") Collection<String> assetCodes);

    /**
     *
     * 新增资产总表
     *
     * @param tblAssetOverview 资产总表
     * @return 结果
     */
    public int insertTblAssetOverview(TblAssetOverview tblAssetOverview);

    /**
     * 修改资产总表
     *
     * @param tblAssetOverview 资产总表
     * @return 结果
     */
    public int updateTblAssetOverview(TblAssetOverview tblAssetOverview);

    /**
     * 删除资产总表
     *
     * @param assetIds 资产总表主键
     * @return 结果
     */
    public int deleteTblAssetOverviewById(Long assetIds);

    /**
     * 批量删除资产总表
     *
     * @param assetIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTblAssetOverviewByIds(Long[] assetIds);

    /**
     * 根据资产类型查询资产总表，过滤虚拟系统已添加资产
     */
    public List<TblAssetOverview> selectAssetsBySysAndType(AssetTypeVo assetTypeVo);

    public List<HashMap> getAssetTypeStatistics(TblAssetOverview tblAssetOverview);

    JSONObject getAssetRiskThreats(@Param("deptId") Long deptId);

    /**
     * 根据资产ID获取位置全名
     *
     * @param assetId 资产ID
     * @return 位置全名（多个位置用分号分隔）
     */
    String selectLocationFullNameByAssetId(@Param("assetId") Long assetId);
}
