{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\components\\SystemList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\components\\SystemList\\index.vue", "mtime": 1756459866243}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgc2VsZWN0ZWRTeXN0ZW1UeXBlOiBudWxsIC8vIOa3u+WKoOeKtuaAgeWPmOmHjwogICAgfQogIH0sCiAgcHJvcHM6IHsKICAgIHN5c3RlbVR5cGVzOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfSwKICAgIHN5c3RlbVR5cGVWYWw6IHsKICAgICAgdHlwZTogW1N0cmluZywgTnVtYmVyXSwKICAgICAgZGVmYXVsdDogbnVsbAogICAgfSwKICB9LAogIG1ldGhvZHM6IHsKICAgIHNlbGVjdFN5c3RlbVR5cGUodmFsKSB7CiAgICAgIGlmICh0aGlzLnNlbGVjdGVkU3lzdGVtVHlwZSA9PT0gdmFsKSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZFN5c3RlbVR5cGUgPSBudWxsOyAvLyDlj5bmtojpgInkuK0KICAgICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6c3lzdGVtVHlwZVZhbCcsIG51bGwpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuc2VsZWN0ZWRTeXN0ZW1UeXBlID0gdmFsOyAvLyDpgInkuK0KICAgICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6c3lzdGVtVHlwZVZhbCcsIHZhbCk7CiAgICAgIH0KICAgICAgdGhpcy4kZW1pdCgnZmlsdGVyU2VsZWN0Jyk7CiAgICB9LAogICAgcmVzZXRTZWxlY3Rpb24oKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRTeXN0ZW1UeXBlID0gbnVsbDsgLy8g6YeN572u6YCJ5oup54q25oCBCiAgICAgIC8vIHRoaXMuJGVtaXQoJ3VwZGF0ZTpzeXN0ZW1UeXBlVmFsJywgbnVsbCk7IC8vIOmAmuefpeeItue7hOS7tumAieaLqeW3sumHjee9rgogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/SystemList", "sourcesContent": ["<template>\n  <ul class=\"system-list\">\n    <li v-for=\"dict in systemTypes\" :key=\"dict.value\" :class=\"{ 'selected': dict.dictValue === selectedSystemType }\">\n      <span @click=\"selectSystemType(dict.dictValue)\">{{ `${dict.dictLabel}${dict.count != null ? '('+dict.count+')' : ''}` }}</span>\n    </li>\n  </ul>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      selectedSystemType: null // 添加状态变量\n    }\n  },\n  props: {\n    systemTypes: {\n      type: Array,\n      required: true\n    },\n    systemTypeVal: {\n      type: [String, Number],\n      default: null\n    },\n  },\n  methods: {\n    selectSystemType(val) {\n      if (this.selectedSystemType === val) {\n        this.selectedSystemType = null; // 取消选中\n        this.$emit('update:systemTypeVal', null);\n      } else {\n        this.selectedSystemType = val; // 选中\n        this.$emit('update:systemTypeVal', val);\n      }\n      this.$emit('filterSelect');\n    },\n    resetSelection() {\n      this.selectedSystemType = null; // 重置选择状态\n      // this.$emit('update:systemTypeVal', null); // 通知父组件选择已重置\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n.system-list {\n  display: flex;\n  flex-flow: wrap;\n}\n\n.system-list li {\n  margin-right: 15px;\n  font-size: 14px;\n  color: #4382FD;\n}\n\n.system-list li.selected {\n  padding: 0 5px;\n  color: #FFFFFF; // 设置选中后的颜色\n  background: #4382FD;\n}\n\n.system-list span {\n  cursor: pointer;\n}\n</style>\n"]}]}