{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\eventList.vue?vue&type=template&id=9f5cded6&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\eventList.vue", "mtime": 1756451963650}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}