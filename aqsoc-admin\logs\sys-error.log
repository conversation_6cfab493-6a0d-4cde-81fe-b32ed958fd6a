2025-09-01 12:00:45.697 [async-task-pool96] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=64, attackId=128, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
