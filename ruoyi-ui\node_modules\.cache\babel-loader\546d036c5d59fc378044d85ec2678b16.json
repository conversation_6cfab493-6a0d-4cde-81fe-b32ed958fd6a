{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\application\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\application\\index.vue", "mtime": 1756459866164}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi93c2gvYXVnbWVudF93b3Jrc3BhY2UvYXFzb2MtbWFpbi9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4uanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLm1hcC5qcyIpOwp2YXIgX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJFOi93c2gvYXVnbWVudF93b3Jrc3BhY2UvYXFzb2MtbWFpbi9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyLmpzIikpOwp2YXIgX3JlZ2VuZXJhdG9yUnVudGltZTIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkU6L3dzaC9hdWdtZW50X3dvcmtzcGFjZS9hcXNvYy1tYWluL3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL3JlZ2VuZXJhdG9yUnVudGltZS5qcyIpKTsKdmFyIF9hc3luY1RvR2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRTovd3NoL2F1Z21lbnRfd29ya3NwYWNlL2Fxc29jLW1haW4vcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvYXN5bmNUb0dlbmVyYXRvci5qcyIpKTsKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJFOi93c2gvYXVnbWVudF93b3Jrc3BhY2UvYXFzb2MtbWFpbi9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyLmpzIikpOwp2YXIgX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJFOi93c2gvYXVnbWVudF93b3Jrc3BhY2UvYXFzb2MtbWFpbi9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkLmpzIikpOwp2YXIgX2FwcGxpY2F0aW9uID0gcmVxdWlyZSgiQC9hcGkvc2FmZS9hcHBsaWNhdGlvbiIpOwpyZXF1aXJlKCJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyIpOwp2YXIgX3Blcm1pc3Npb24gPSByZXF1aXJlKCJAL3V0aWxzL3Blcm1pc3Npb24iKTsKdmFyIF92dWV4ID0gcmVxdWlyZSgidnVleCIpOwp2YXIgX2F1dGggPSByZXF1aXJlKCJAL3V0aWxzL2F1dGgiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJBcHBsaWNhdGlvbiIsCiAgZGljdHM6IFsncHJvdGVjdGlvbl9ncmFkZScsICdhcHBjaGVja19zdGF0ZScsICdhcHBfbG9naW5fdHlwZScsICdhcHBfZGVwbG95JywgJ3NlcnZlX2dyb3VwJywgJ2FwcF9zdGF0ZScsICdjb25zdHJ1Y3RfdHlwZScsICdzeXN0ZW1fdHlwZScsICdhc3NldF9zdGF0ZScsICdwcm90ZWN0aW9uX2dyYWRlJywgJ2FwcGNoZWNrX3N0YXRlJ10sCiAgY29tcG9uZW50czogewogICAgQXBwbGljYXRpb25EZXRhaWxzOiBmdW5jdGlvbiBBcHBsaWNhdGlvbkRldGFpbHMoKSB7CiAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gKDAsIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkMi5kZWZhdWx0KShyZXF1aXJlKCdAL3ZpZXdzL2hobENvZGUvY29tcG9uZW50L2FwcGxpY2F0aW9uRGV0YWlscy52dWUnKSk7CiAgICAgIH0pOwogICAgfSwKICAgIEFwcGxpY2F0aW9uRGlhbG9nOiBmdW5jdGlvbiBBcHBsaWNhdGlvbkRpYWxvZygpIHsKICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSgpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiAoMCwgX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQyLmRlZmF1bHQpKHJlcXVpcmUoJ0Avdmlld3MvaGhsQ29kZS9jb21wb25lbnQvYXBwbGljYXRpb24vYXBwbGljYXRpb25JbmZvLnZ1ZScpKTsKICAgICAgfSk7CiAgICB9LAogICAgaW1wb3J0VGhyZWF0ZW5JbmZvOiBmdW5jdGlvbiBpbXBvcnRUaHJlYXRlbkluZm8oKSB7CiAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gKDAsIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkMi5kZWZhdWx0KShyZXF1aXJlKCdAL3ZpZXdzL2Jhc2lzL3NlY3VyaXR5V2Fybi9pbXBvcnRUaHJlYXRlbkluZm8nKSk7CiAgICAgIH0pOwogICAgfSwKICAgIERlcHRTZWxlY3RTeXN0ZW06IGZ1bmN0aW9uIERlcHRTZWxlY3RTeXN0ZW0oKSB7CiAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gKDAsIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkMi5kZWZhdWx0KShyZXF1aXJlKCdAL3ZpZXdzL3NhZmUvYXBwbGljYXRpb24vY29tcG9uZW50L2RlcHRTZWxlY3RTeXN0ZW0nKSk7CiAgICAgIH0pOwogICAgfSwKICAgIHR5cGVUcmVlOiBmdW5jdGlvbiB0eXBlVHJlZSgpIHsKICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSgpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiAoMCwgX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQyLmRlZmF1bHQpKHJlcXVpcmUoJ0Avdmlld3MvY29tcG9uZW50cy90eXBlVHJlZScpKTsKICAgICAgfSk7CiAgICB9LAogICAgdmVuZG9yU2VsZWN0OiBmdW5jdGlvbiB2ZW5kb3JTZWxlY3QoKSB7CiAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gKDAsIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkMi5kZWZhdWx0KShyZXF1aXJlKCdAL3ZpZXdzL2NvbXBvbmVudHMvc2VsZWN0L3ZlbmRvclNlbGVjdCcpKTsKICAgICAgfSk7CiAgICB9LAogICAgdXBsb2FkRmlsZVRhYmxlOiBmdW5jdGlvbiB1cGxvYWRGaWxlVGFibGUoKSB7CiAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gKDAsIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkMi5kZWZhdWx0KShyZXF1aXJlKCdAL3ZpZXdzL2NvbXBvbmVudHMvdGFibGUvdXBsb2FkRmlsZVRhYmxlJykpOwogICAgICB9KTsKICAgIH0sCiAgICBTeXN0ZW1MaXN0OiBmdW5jdGlvbiBTeXN0ZW1MaXN0KCkgewogICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuICgwLCBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZDIuZGVmYXVsdCkocmVxdWlyZSgnLi4vLi4vLi4vY29tcG9uZW50cy9TeXN0ZW1MaXN0JykpOwogICAgICB9KTsKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzaG93QWxsOiBmYWxzZSwKICAgICAgdXBsb2FkOiB7CiAgICAgICAgb3BlbjogZmFsc2UsCiAgICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGC77yI55So5oi35a+85YWl77yJCiAgICAgICAgdGl0bGU6ICIiLAogICAgICAgIC8vIOW8ueWHuuWxguagh+mimO+8iOeUqOaIt+WvvOWFpe+8iQogICAgICAgIGNsZWFyOiAiMCIsCiAgICAgICAgaXNVcGxvYWRpbmc6IGZhbHNlLAogICAgICAgIC8vIOaYr+WQpuemgeeUqOS4iuS8oAogICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArICgwLCBfYXV0aC5nZXRUb2tlbikoKQogICAgICAgIH0sCiAgICAgICAgLy8g6K6+572u5LiK5Lyg55qE6K+35rGC5aS06YOoCiAgICAgICAgdXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9zYWZlL2FwcGxpY2F0aW9uL2ltcG9ydERhdGFUb0ppYW5nVG9uZyIgLy8g5LiK5Lyg55qE5Zyw5Z2ACiAgICAgIH0sCiAgICAgIHdoZXRoZXJPck5vdFRvQXVkaXQ6IGZhbHNlLAogICAgICBjaGVja1Blcm1pOiBfcGVybWlzc2lvbi5jaGVja1Blcm1pLAogICAgICBjaGVja1JvbGU6IF9wZXJtaXNzaW9uLmNoZWNrUm9sZSwKICAgICAgY29udGVudDogIiIsCiAgICAgIGNsYXNzSWQ6IDcsCiAgICAgIGNsYXNzTmFtZTogJ+S4muWKoeW6lOeUqOezu+e7ny/lubPlj7AnLAogICAgICB0eXBlbGlzdDogW10sCiAgICAgIGNoaWxkcmVuOiBbXSwKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGlkczogW10sCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBjdXJyZW50TmFtZXM6IFtdLAogICAgICBhc3NldE5hbWVzOiBbXSwKICAgICAgLy8g6YCJ5Lit6KGo5pWw57uECiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgYXBwbGljYXRpb25MaXN0OiBbXSwKICAgICAgLy8g5Lia5Yqh5bqU55So57O757uf6KGo5qC85pWw5o2uCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgcGFyYW1zOiB7fSwKICAgICAgZGVwdFNlbGVjdEtleTogMCwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgaXNBc2M6ICdkZXNjJywKICAgICAgICBvcmRlckJ5Q29sdW1uOiAnY3JlYXRlVGltZScsCiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgYXNzZXRDb2RlOiBudWxsLAogICAgICAgIGFzc2V0TmFtZTogbnVsbCwKICAgICAgICBkZWdyZWVJbXBvcnRhbmNlOiBudWxsLAogICAgICAgIGRvbWFpbklkOiBudWxsLAogICAgICAgIGFwcGxpY2F0aW9uSWRzOiBbXQogICAgICB9LAogICAgICBzeXN0ZW1zVHlwZTogW10sCiAgICAgIC8vIOezu+e7n+exu+WeiwogICAgICBhc3NldFN0YXRlOiBbXSwKICAgICAgLy8g5LiK57q/54q25oCBCiAgICAgIHByb3RlY3Rpb25HcmFkZTogW10sCiAgICAgIC8vIOetieS/neetiee6pwogICAgICBhcHBjaGVja1N0YXRlOiBbXSwKICAgICAgLy8g5a6h5qC454q25oCBCiAgICAgIGh3SXNUcnVlU2h1dERvd246IFt7CiAgICAgICAgZGljdExhYmVsOiAn5pivJywKICAgICAgICBkaWN0VmFsdWU6ICcxJwogICAgICB9LCB7CiAgICAgICAgZGljdExhYmVsOiAn5ZCmJywKICAgICAgICBkaWN0VmFsdWU6ICcwJwogICAgICB9XSwKICAgICAgcGFyYW1zQXJyYXk6IFsnc3lzdGVtX3R5cGUnLCAnYXNzZXRfc3RhdGUnLCAncHJvdGVjdGlvbl9ncmFkZScsICdhcHBjaGVja19zdGF0ZSddLAogICAgICAvLyDmkJzntKLlj4LmlbAKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgcmVtYXJrTXNnOiBbewogICAgICAgICAgbWluOiAzLAogICAgICAgICAgbWF4OiAxNzAsCiAgICAgICAgICBtZXNzYWdlOiAn5L+u5pS56K6w5b2V5LiN6IO95bCR5LqOM+S4quWtl+espuS4lOS4jeiDveWkp+S6jjE3MOS4quWtl+espicsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9LCB7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfkv67mlLnorrDlvZXkuI3og73kuLrnqbonLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgY29sdW1uczogW3sKICAgICAgICBrZXk6IDAsCiAgICAgICAgbGFiZWw6ICLns7vnu5/lkI3np7AiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogMSwKICAgICAgICBsYWJlbDogIueZu+W9leWcsOWdgCIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAyLAogICAgICAgIGxhYmVsOiAi5omA5bGe6YOo6ZeoIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBrZXk6IDMsCiAgICAgICAgbGFiZWw6ICLotKPku7vkurrlkZgiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogNCwKICAgICAgICBsYWJlbDogIuiBlOezu+eUteivnSIsCiAgICAgICAgdmlzaWJsZTogZmFsc2UKICAgICAgfSwgewogICAgICAgIGtleTogNSwKICAgICAgICBsYWJlbDogIuetieS/nee6p+WIqyIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiA2LAogICAgICAgIGxhYmVsOiAi5YWz6ZSu6K6+5pa9IiwKICAgICAgICB2aXNpYmxlOiBmYWxzZQogICAgICB9LCB7CiAgICAgICAga2V5OiA3LAogICAgICAgIGxhYmVsOiAi5oqA5pyv5p625p6EIiwKICAgICAgICB2aXNpYmxlOiBmYWxzZQogICAgICB9LCB7CiAgICAgICAga2V5OiA4LAogICAgICAgIGxhYmVsOiAi55m75b2V5pa55byPIiwKICAgICAgICB2aXNpYmxlOiBmYWxzZQogICAgICB9LCB7CiAgICAgICAga2V5OiA5LAogICAgICAgIGxhYmVsOiAi6YOo572y5pa55byPIiwKICAgICAgICB2aXNpYmxlOiBmYWxzZQogICAgICB9LCB7CiAgICAgICAga2V5OiAxMCwKICAgICAgICBsYWJlbDogIuS+m+W6lOWVhiIsCiAgICAgICAgdmlzaWJsZTogZmFsc2UKICAgICAgfSwgewogICAgICAgIGtleTogMTEsCiAgICAgICAgbGFiZWw6ICLmnI3liqHlr7nosaEiLAogICAgICAgIHZpc2libGU6IGZhbHNlCiAgICAgIH0sIHsKICAgICAgICBrZXk6IDEyLAogICAgICAgIGxhYmVsOiAi5o6I5p2D55So5oi35pWwIiwKICAgICAgICB2aXNpYmxlOiBmYWxzZQogICAgICB9LCB7CiAgICAgICAga2V5OiAxMywKICAgICAgICBsYWJlbDogIuaciOWdh+a0u+i3g+S6uuaVsCIsCiAgICAgICAgdmlzaWJsZTogZmFsc2UKICAgICAgfSwgewogICAgICAgIGtleTogMTQsCiAgICAgICAgbGFiZWw6ICLkuIrnur/nirbmgIEiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogMTUsCiAgICAgICAgbGFiZWw6ICLlrqHmoLjnirbmgIEiLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGtleTogMTYsCiAgICAgICAgbGFiZWw6ICLlpIfms6giLAogICAgICAgIHZpc2libGU6IGZhbHNlCiAgICAgIH0sIHsKICAgICAgICBrZXk6IDE3LAogICAgICAgIGxhYmVsOiAi5qCH562+IiwKICAgICAgICB2aXNpYmxlOiBmYWxzZQogICAgICB9LCB7CiAgICAgICAga2V5OiAxOCwKICAgICAgICBsYWJlbDogIuS4pemHjea8j+a0niIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAxOSwKICAgICAgICBsYWJlbDogIumrmOWNsea8j+a0niIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAyMCwKICAgICAgICBsYWJlbDogIuS4reWNsea8j+a0niIsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAga2V5OiAyMSwKICAgICAgICBsYWJlbDogIklQ5Zyw5Z2AIiwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH1dLAogICAgICBlZGl0SXRlbTogImVkaXQiLAogICAgICBlZGl0YWJsZTogdHJ1ZSwKICAgICAgc3RlcDogMCwKICAgICAgY3VycmVudENvbXBvbmVudDogJ0FkZEFwcCcsCiAgICAgIGF1ZGl0QXBwOiBudWxsLAogICAgICByZW1hcmtGcm9tOiB7CiAgICAgICAgcmVtYXJrTXNnOiAnJwogICAgICB9LAogICAgICByZW1hcmtEaWFsb2c6IGZhbHNlLAogICAgICBkZWZhdWx0U2hvdzogdHJ1ZSwKICAgICAgYXBwbGljYXRpb25WZXJzaW9uOiBmYWxzZSwKICAgICAgYXBwbGljYXRpb25WaXNpYmxlOiBmYWxzZSwKICAgICAgb3B0aW9uQ29sdW1uczogWyLns7vnu5/lkI3np7AiLCAi55m75b2V5Zyw5Z2AIiwgIklQ5Zyw5Z2AIiwgIuaJgOWxnumDqOmXqCIsICLotKPku7vkurrlkZgiLCAi6IGU57O755S16K+dIiwgIuetieS/nee6p+WIqyIsICLlhbPplK7orr7mlr0iLCAi5oqA5pyv5p625p6EIiwgIueZu+W9leaWueW8jyIsICLpg6jnvbLmlrnlvI8iLCAi5L6b5bqU5ZWGIiwgIuacjeWKoeWvueixoSIsICLmjojmnYPnlKjmiLfmlbAiLCAi5pyI5Z2H5rS76LeD5Lq65pWwIiwgIuS4iue6v+eKtuaAgSIsICLlrqHmoLjnirbmgIEiLCAi5aSH5rOoIiwgIuagh+etviIsICLkuKXph43mvI/mtJ4iLCAi6auY5Y2x5ryP5rSeIiwgIuS4reWNsea8j+a0niJdLAogICAgICBjaGVja2VkQ29sdW1uczogWyLns7vnu5/lkI3np7AiLCAi5omA5bGe6YOo6ZeoIiwgIui0o+S7u+S6uuWRmCIsICLnmbvlvZXlnLDlnYAiLCAiSVDlnLDlnYAiLCAi562J5L+d57qn5YirIiwgIuS4iue6v+eKtuaAgSIsICLlrqHmoLjnirbmgIEiLCAi5Lil6YeN5ryP5rSeIiwgIumrmOWNsea8j+a0niIsICLkuK3ljbHmvI/mtJ4iXSwKICAgICAgdGFibGVLZXk6IDEKICAgIH07CiAgfSwKICBjb21wdXRlZDogKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCAoMCwgX3Z1ZXgubWFwR2V0dGVycykoWyJhY3RpdmVOYW1lcyJdKSksCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAoMCwgX2FwcGxpY2F0aW9uLmF1ZGl0Q29uZmlnKSh7CiAgICAgIHBhZ2VOdW06IDEsCiAgICAgIHBhZ2VTaXplOiAxMAogICAgfSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMud2hldGhlck9yTm90VG9BdWRpdCA9IHJlc3BvbnNlLnJvd3NbMF0uY29uZmlnVmFsdWUgIT09ICdmYWxzZSc7CiAgICB9KTsKICAgIHRoaXMuaW5pdFNlYXJjaERhdGFBbmRMaXN0RGF0YSgpOwogICAgLy8gdGhpcy5oYW5kbGVRdWVyeSgpOwogIH0sCiAgd2F0Y2g6IHsKICAgICckcm91dGVyLm5hbWUnOiBmdW5jdGlvbiAkcm91dGVyTmFtZSh2YWwpIHsKICAgICAgaWYgKHZhbCA9PSAnQXBwbGljYXRpb24nKSB7CiAgICAgICAgdGhpcy5pbml0KCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCiAgICAnJHJvdXRlLnF1ZXJ5JzogewogICAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKHZhbCkgewogICAgICAgIGlmICh2YWwgJiYgdmFsLmRlcHRJZCkgewogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQgPSBwYXJzZUludCh2YWwuZGVwdElkKTsKICAgICAgICB9CiAgICAgICAgaWYgKHZhbCAmJiB2YWwuZG9tYWluSWQpIHsKICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZG9tYWluSWQgPSB2YWwuZG9tYWluSWQ7CiAgICAgICAgfQogICAgICAgIGlmICh2YWwgJiYgdmFsLmFwcGxpY2F0aW9uSWRzKSB7CiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmFwcGxpY2F0aW9uSWRzID0gdmFsLmFwcGxpY2F0aW9uSWRzOwogICAgICAgIH0KICAgICAgICBpZiAodmFsICYmIHZhbC5zeXN0ZW1UeXBlKSB7CiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN5c3RlbVR5cGUgPSB2YWwuc3lzdGVtVHlwZTsKICAgICAgICB9CiAgICAgIH0sCiAgICAgIGRlZXA6IHRydWUsCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgLy8g5a+85YWl5Yqf6IO9CiAgICBoYW5kbGVJbXBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUltcG9ydCgpIHsKICAgICAgdGhpcy51cGxvYWQudGl0bGUgPSAi5Lia5Yqh57O757uf5a+85YWlIjsKICAgICAgdGhpcy51cGxvYWQub3BlbiA9IHRydWU7CiAgICB9LAogICAgaGFuZGxlRmlsZVVwbG9hZFByb2dyZXNzOiBmdW5jdGlvbiBoYW5kbGVGaWxlVXBsb2FkUHJvZ3Jlc3MoZXZlbnQsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gdHJ1ZTsKICAgIH0sCiAgICBoYW5kbGVGaWxlU3VjY2VzczogZnVuY3Rpb24gaGFuZGxlRmlsZVN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMudXBsb2FkLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSBmYWxzZTsKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuY2xlYXJGaWxlcygpOwogICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHRoaXMuJGFsZXJ0KCLmiJDlip/lr7zlhaUiLCAi5a+85YWl57uT5p6cIiwgewogICAgICAgIGRhbmdlcm91c2x5VXNlSFRNTFN0cmluZzogdHJ1ZQogICAgICB9KTtlbHNlIHRoaXMuJGFsZXJ0KHJlc3BvbnNlLm1zZywgIuWvvOWFpee7k+aenCIsIHsKICAgICAgICBkYW5nZXJvdXNseVVzZUhUTUxTdHJpbmc6IHRydWUKICAgICAgfSk7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICB0aGlzLmRlcHRTZWxlY3RLZXkgKz0gMTsKICAgIH0sCiAgICBzdWJtaXRGaWxlRm9ybTogZnVuY3Rpb24gc3VibWl0RmlsZUZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOwogICAgfSwKICAgIGltcG9ydFRlbXBsYXRlOiBmdW5jdGlvbiBpbXBvcnRUZW1wbGF0ZSgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnc2FmZS9hcHBsaWNhdGlvbi9pbXBvcnRUZW1wbGF0ZVRvSmlhbmdUb25nJywge30sICJhcHBsaWNhdGlvbl8iLmNvbmNhdChuZXcgRGF0ZSgpLmdldFRpbWUoKSwgIi54bHN4IikpOwogICAgfSwKICAgIC8vIOWIneWni+WMlgogICAgaW5pdFNlYXJjaERhdGFBbmRMaXN0RGF0YTogZnVuY3Rpb24gaW5pdFNlYXJjaERhdGFBbmRMaXN0RGF0YSgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0LnByZXYgPSBfY29udGV4dC5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMjsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMyLmluaXRTZWFyY2hEYXRhKCk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBfdGhpczIuaW5pdCgpOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy8g5Yid5aeL5YyW5p+l6K+i5p2h5Lu2CiAgICBpbml0U2VhcmNoRGF0YTogZnVuY3Rpb24gaW5pdFNlYXJjaERhdGEoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMigpIHsKICAgICAgICB2YXIgX2l0ZXJhdG9yLCBfc3RlcCwgcGFyYW1zLCByZXNwb25zZTsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlMiQoX2NvbnRleHQyKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDIucHJldiA9IF9jb250ZXh0Mi5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfaXRlcmF0b3IgPSAoMCwgX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIyLmRlZmF1bHQpKF90aGlzMy5wYXJhbXNBcnJheSk7CiAgICAgICAgICAgICAgX2NvbnRleHQyLnByZXYgPSAxOwogICAgICAgICAgICAgIF9pdGVyYXRvci5zKCk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBpZiAoKF9zdGVwID0gX2l0ZXJhdG9yLm4oKSkuZG9uZSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQyLm5leHQgPSAyMTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBwYXJhbXMgPSBfc3RlcC52YWx1ZTsKICAgICAgICAgICAgICBfY29udGV4dDIucHJldiA9IDU7CiAgICAgICAgICAgICAgX2NvbnRleHQyLm5leHQgPSA4OwogICAgICAgICAgICAgIHJldHVybiAoMCwgX2FwcGxpY2F0aW9uLmdldEFwcENvdW50QnlEaWN0KShwYXJhbXMpOwogICAgICAgICAgICBjYXNlIDg6CiAgICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dDIuc2VudDsKICAgICAgICAgICAgICBpZiAocGFyYW1zID09PSAnc3lzdGVtX3R5cGUnKSBfdGhpczMuc3lzdGVtc1R5cGUgPSByZXNwb25zZS5kYXRhLmNvdW50QnlEaWN0TGlzdCB8fCBbXTsKICAgICAgICAgICAgICBpZiAocGFyYW1zID09PSAnYXNzZXRfc3RhdGUnKSBfdGhpczMuYXNzZXRTdGF0ZSA9IHJlc3BvbnNlLmRhdGEuY291bnRCeURpY3RMaXN0IHx8IFtdOwogICAgICAgICAgICAgIGlmIChwYXJhbXMgPT09ICdwcm90ZWN0aW9uX2dyYWRlJykgX3RoaXMzLnByb3RlY3Rpb25HcmFkZSA9IHJlc3BvbnNlLmRhdGEuY291bnRCeURpY3RMaXN0IHx8IFtdOwogICAgICAgICAgICAgIGlmIChwYXJhbXMgPT09ICdhcHBjaGVja19zdGF0ZScpIF90aGlzMy5hcHBjaGVja1N0YXRlID0gcmVzcG9uc2UuZGF0YS5jb3VudEJ5RGljdExpc3QgfHwgW107CiAgICAgICAgICAgICAgY29uc29sZS5sb2coX3RoaXMzLmFwcGNoZWNrU3RhdGUpOwogICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gMTk7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMTY6CiAgICAgICAgICAgICAgX2NvbnRleHQyLnByZXYgPSAxNjsKICAgICAgICAgICAgICBfY29udGV4dDIudDAgPSBfY29udGV4dDJbImNhdGNoIl0oNSk7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign6K+35rGC5aSx6LSlOicsIF9jb250ZXh0Mi50MCk7CiAgICAgICAgICAgIGNhc2UgMTk6CiAgICAgICAgICAgICAgX2NvbnRleHQyLm5leHQgPSAzOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDIxOgogICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gMjY7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMjM6CiAgICAgICAgICAgICAgX2NvbnRleHQyLnByZXYgPSAyMzsKICAgICAgICAgICAgICBfY29udGV4dDIudDEgPSBfY29udGV4dDJbImNhdGNoIl0oMSk7CiAgICAgICAgICAgICAgX2l0ZXJhdG9yLmUoX2NvbnRleHQyLnQxKTsKICAgICAgICAgICAgY2FzZSAyNjoKICAgICAgICAgICAgICBfY29udGV4dDIucHJldiA9IDI2OwogICAgICAgICAgICAgIF9pdGVyYXRvci5mKCk7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5maW5pc2goMjYpOwogICAgICAgICAgICBjYXNlIDI5OgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUyLCBudWxsLCBbWzEsIDIzLCAyNiwgMjldLCBbNSwgMTZdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGluaXQ6IGZ1bmN0aW9uIGluaXQoKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gT2JqZWN0LmFzc2lnbih0aGlzLnF1ZXJ5UGFyYW1zLCB0aGlzLiRyb3V0ZS5wYXJhbXMpOwogICAgICBpZiAodGhpcy4kcm91dGUucGFyYW1zLmFkZCkgewogICAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIF90aGlzNC5oYW5kbGVBZGQoKTsKICAgICAgICAgIF90aGlzNC5mb3JtLmxvY2F0aW9uSWQgPSBfdGhpczQuJHJvdXRlLnBhcmFtcy5sb2NhdGlvbklkOwogICAgICAgICAgX3RoaXM0Lm9wZW4gPSB0cnVlOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLy/pgInkuK3pg6jpl6jkuovku7YKICAgIGRlcHRTZWxlY3Q6IGZ1bmN0aW9uIGRlcHRTZWxlY3Qobm9kZSkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmFzc2V0Q29kZSA9IG51bGw7CiAgICAgIC8vIHRoaXMucXVlcnlQYXJhbXMuYXNzZXROYW1lID0gbnVsbDsKICAgICAgaWYgKG5vZGUuaWQgIT0gbnVsbCkgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGVwdElkID0gbm9kZS5pZDsKICAgICAgfQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy/mjpLluo8KICAgIHNvcnRDaGFuZ2U6IGZ1bmN0aW9uIHNvcnRDaGFuZ2UoY29sdW1uLCBwcm9wLCBvcmRlcikgewogICAgICBpZiAoY29sdW1uLm9yZGVyICE9IG51bGwpIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmlzQXNjID0gJ2Rlc2MnOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuaXNBc2MgPSAnYXNjJzsKICAgICAgfQogICAgICBpZiAoY29sdW1uLnByb3AgPT0gJ3N0YXRlJykgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMub3JkZXJCeUNvbHVtbiA9ICJlLnN0YXRlIjsKICAgICAgfSBlbHNlIHRoaXMucXVlcnlQYXJhbXMub3JkZXJCeUNvbHVtbiA9IGNvbHVtbi5wcm9wOwogICAgICB0aGlzLmdldExpc3QodGhpcy5xdWVyeVBhcmFtcyk7CiAgICB9LAogICAgLyoqIOafpeivouS4muWKoeW6lOeUqOezu+e7n+WIl+ihqCAqL2dldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBpZiAodGhpcy4kcm91dGUucGFyYW1zKSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pZHMgPSB0aGlzLiRyb3V0ZS5wYXJhbXMuaWRzOwogICAgICB9CiAgICAgICgwLCBfYXBwbGljYXRpb24ubGlzdEFwcGxpY2F0aW9uKSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzNS5hcHBsaWNhdGlvbkxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIF90aGlzNS50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIF90aGlzNS5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pLmZpbmFsbHkoZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNS4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXM1LnRhYmxlS2V5Kys7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLmVkaXRJdGVtID0gImVkaXQiOwogICAgICB0aGlzLmVkaXRhYmxlID0gdHJ1ZTsKICAgICAgdGhpcy5zdGVwID0gMDsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovcmVzZXRRdWVyeTogZnVuY3Rpb24gcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zeXN0ZW1UeXBlID0gbnVsbDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zdGF0ZSA9IG51bGw7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJvdGVjdEdyYWRlID0gbnVsbDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pc09wZW5OZXR3b3JrID0gbnVsbDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5od0lzVHJ1ZVNodXREb3duID0gbnVsbDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jaGVja09uID0gbnVsbDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hc3NldENvZGUgPSBudWxsOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmFzc2V0TmFtZSA9IG51bGw7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMudXJsID0gbnVsbDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kb21haW5VcmwgPSBudWxsOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRvbWFpbklkID0gbnVsbDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hcHBsaWNhdGlvbklkcyA9IFtdOwogICAgICB0aGlzLiRyZWZzLnN5c3RlbUxpc3QxICYmIHRoaXMuJHJlZnMuc3lzdGVtTGlzdDEucmVzZXRTZWxlY3Rpb24oKTsKICAgICAgdGhpcy4kcmVmcy5zeXN0ZW1MaXN0MiAmJiB0aGlzLiRyZWZzLnN5c3RlbUxpc3QyLnJlc2V0U2VsZWN0aW9uKCk7CiAgICAgIHRoaXMuJHJlZnMuc3lzdGVtTGlzdDMgJiYgdGhpcy4kcmVmcy5zeXN0ZW1MaXN0My5yZXNldFNlbGVjdGlvbigpOwogICAgICB0aGlzLiRyZWZzLnN5c3RlbUxpc3Q0ICYmIHRoaXMuJHJlZnMuc3lzdGVtTGlzdDQucmVzZXRTZWxlY3Rpb24oKTsKICAgICAgdGhpcy5jbGVhclJvdXRlUXVlcnlQYXJhbXMoKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIGNsZWFyUm91dGVRdWVyeVBhcmFtczogZnVuY3Rpb24gY2xlYXJSb3V0ZVF1ZXJ5UGFyYW1zKCkgewogICAgICBpZiAodGhpcy4kcm91dGUucGFyYW1zKSB7CiAgICAgICAgdmFyIHF1ZXJ5UGFyYW1zID0gdGhpcy4kcm91dGUucGFyYW1zOwogICAgICAgIGRlbGV0ZSBxdWVyeVBhcmFtcy5pZHM7CiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgICAgcGFyYW1zOiBxdWVyeVBhcmFtcwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0uYXNzZXRJZDsKICAgICAgfSk7CiAgICAgIHRoaXMuYXNzZXROYW1lcyA9IHNlbGVjdGlvbi5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS5hc3NldE5hbWU7CiAgICAgIH0pOwogICAgICB0aGlzLmN1cnJlbnROYW1lcyA9IHNlbGVjdGlvbi5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS5hc3NldE5hbWU7CiAgICAgIH0pOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICAvL+aPkOS6pOWuoeaguAogICAgaGFuZGxlQXBwbHk6IGZ1bmN0aW9uIGhhbmRsZUFwcGx5KGFwcCkgewogICAgICB0aGlzLmF1ZGl0QXBwID0gYXBwOwogICAgICBpZiAoYXBwLmNoZWNrQnkpIHsKICAgICAgICB0aGlzLnJlbWFya0RpYWxvZyA9IHRydWU7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5zdWJtaXRNc2coKTsKICAgICAgfQogICAgfSwKICAgIGZvcm1Nc2dTdWJtaXQ6IGZ1bmN0aW9uIGZvcm1Nc2dTdWJtaXQoKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWydyZW1hcmtGcm9tJ10udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBfdGhpczYuc3VibWl0TXNnKCk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBzdWJtaXRNc2c6IGZ1bmN0aW9uIHN1Ym1pdE1zZygpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgICgwLCBfYXBwbGljYXRpb24uYXBwbHlBcHBsaWNhdGlvbikoewogICAgICAgIGFzc2V0SWQ6IHRoaXMuYXVkaXRBcHAuYXNzZXRJZCwKICAgICAgICByZW1hcms6IHRoaXMucmVtYXJrRnJvbS5yZW1hcmtNc2cKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXM3LiRtb2RhbC5tc2dTdWNjZXNzKCLlt7Lnu4/mj5DkuqTlrqHmoLjvvIEiKTsKICAgICAgICBfdGhpczcuZ2V0TGlzdCgpOwogICAgICAgIF90aGlzNy5jYWxsT2ZmTXNnKCk7CiAgICAgIH0pOwogICAgfSwKICAgIGNhbGxPZmZNc2c6IGZ1bmN0aW9uIGNhbGxPZmZNc2coKSB7CiAgICAgIHRoaXMuYXVkaXRBcHAgPSBudWxsOwogICAgICB0aGlzLnJlbWFya0Zyb20ucmVtYXJrTXNnID0gJyc7CiAgICAgIHRoaXMucmVtYXJrRGlhbG9nID0gZmFsc2U7CiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqL2hhbmRsZUFkZDogZnVuY3Rpb24gaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMudGl0bGUgPSAn5re75Yqg5bqU55So5L+h5oGvJzsKICAgICAgdGhpcy5wYXJhbXMgPSB7fTsKICAgICAgdGhpcy5hcHBsaWNhdGlvblZpc2libGUgPSB0cnVlOwogICAgICAvLyB0aGlzLiR0YWIub3BlblBhZ2UoCiAgICAgIC8vICAgIua3u+WKoOW6lOeUqOS/oeaBryIsCiAgICAgIC8vICAgIi9hc3NldC1sZWRnZXIvbW9uaXRvcjIvYXBwbGljYXRpb24vaW5mbyIsCiAgICAgIC8vICAge30KICAgICAgLy8gKTsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovaGFuZGxlVXBkYXRlOiBmdW5jdGlvbiBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHZhciBlZGl0ID0gYXJndW1lbnRzLmxlbmd0aCA+IDEgJiYgYXJndW1lbnRzWzFdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMV0gOiB0cnVlOwogICAgICB2YXIgZGF0YSA9IGFyZ3VtZW50cy5sZW5ndGggPiAyID8gYXJndW1lbnRzWzJdIDogdW5kZWZpbmVkOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIGlmIChyb3cuY2hlY2tPbiA9PT0gInBhc3MiICYmIChkYXRhID09PSB1bmRlZmluZWQgfHwgZGF0YSA9PT0gbnVsbCkpIHsKICAgICAgICB0aGlzLnRpdGxlID0gJ+S/ruaUueW6lOeUqOS/oeaBryc7CiAgICAgICAgdGhpcy5lZGl0YWJsZSA9IGVkaXQ7CiAgICAgICAgdmFyIGFzc2V0SWQgPSByb3cuYXNzZXRJZCB8fCB0aGlzLmlkczsKICAgICAgICB0aGlzLnBhcmFtcyA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7CiAgICAgICAgICBhc3NldElkOiBhc3NldElkCiAgICAgICAgfSwgZGF0YSk7CiAgICAgICAgdGhpcy5wYXJhbXMuaXNFZGl0ID0gdHJ1ZTsKICAgICAgICB0aGlzLmFwcGxpY2F0aW9uVmlzaWJsZSA9IHRydWU7CiAgICAgICAgLy8gdGhpcy4kdGFiLm9wZW5QYWdlKHRpdGxlLCAnL2Fzc2V0LWxlZGdlci9tb25pdG9yMi9hcHBsaWNhdGlvbi9pbmZvJywgcGFyYW1zKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnRpdGxlID0gZGF0YSA/IGRhdGEuc2hvd0RhdGEgPT09ICdmYWxzZScgPyAn5p+l55yL5bqU55So5L+h5oGvJyA6ICfkv67mlLnlupTnlKjkv6Hmga8nIDogJ+S/ruaUueW6lOeUqOS/oeaBryc7CiAgICAgICAgdGhpcy5lZGl0YWJsZSA9IGVkaXQ7CiAgICAgICAgdmFyIF9hc3NldElkID0gcm93LmFzc2V0SWQgfHwgdGhpcy5pZHM7CiAgICAgICAgdGhpcy5wYXJhbXMgPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoewogICAgICAgICAgYXNzZXRJZDogX2Fzc2V0SWQKICAgICAgICB9LCBkYXRhKTsKICAgICAgICB0aGlzLmFwcGxpY2F0aW9uVmlzaWJsZSA9IHRydWU7CiAgICAgICAgLy8gdGhpcy4kdGFiLm9wZW5QYWdlKHRpdGxlLCAnL2Fzc2V0LWxlZGdlci9tb25pdG9yMi9hcHBsaWNhdGlvbi9pbmZvJywge2Fzc2V0SWQsIC4uLmRhdGF9KTsKICAgICAgfQogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi9oYW5kbGVEZWxldGU6IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzOCA9IHRoaXM7CiAgICAgIHZhciBhc3NldElkcyA9IHJvdy5hc3NldElkIHx8IHRoaXMuaWRzOwogICAgICB2YXIgYXNzZXRzTmFtZSA9ICIiOwogICAgICBpZiAoIXJvdy5hc3NldElkKSB7CiAgICAgICAgYXNzZXRzTmFtZSA9IHRoaXMuY3VycmVudE5hbWVzLmpvaW4oIiwiKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBhc3NldHNOYW1lID0gcm93LmFzc2V0TmFtZTsKICAgICAgfQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTjgJAnICsgYXNzZXRzTmFtZSArICfjgJHnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gKDAsIF9hcHBsaWNhdGlvbi5kZWxBcHBsaWNhdGlvbikoYXNzZXRJZHMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczguZ2V0TGlzdCgpOwogICAgICAgIF90aGlzOC5kZXB0U2VsZWN0S2V5ICs9IDE7CiAgICAgICAgX3RoaXM4LiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkge30pOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi9oYW5kbGVFeHBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnc2FmZS9hcHBsaWNhdGlvbi9leHBvcnQnLCAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHRoaXMucXVlcnlQYXJhbXMpLCAiYXBwbGljYXRpb25fIi5jb25jYXQobmV3IERhdGUoKS5nZXRUaW1lKCksICIueGxzeCIpKTsKICAgIH0sCiAgICBhcHBsaWNhdGlvbkNoYW5nZTogZnVuY3Rpb24gYXBwbGljYXRpb25DaGFuZ2UoZGF0YSkgewogICAgICB0aGlzLmFwcGxpY2F0aW9uVmlzaWJsZSA9IGRhdGE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICB0aGlzLmluaXRTZWFyY2hEYXRhKCk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_application", "require", "_permission", "_vuex", "_auth", "name", "dicts", "components", "ApplicationDetails", "Promise", "resolve", "then", "_interopRequireWildcard2", "default", "ApplicationDialog", "importThreatenInfo", "DeptSelectSystem", "typeTree", "vendorSelect", "uploadFileTable", "SystemList", "data", "showAll", "upload", "open", "title", "clear", "isUploading", "headers", "Authorization", "getToken", "url", "process", "env", "VUE_APP_BASE_API", "whetherOrNotToAudit", "check<PERSON><PERSON><PERSON>", "checkRole", "content", "classId", "className", "typelist", "children", "loading", "ids", "currentNames", "assetNames", "single", "multiple", "showSearch", "total", "applicationList", "params", "deptSelectKey", "queryParams", "isAsc", "orderByColumn", "pageNum", "pageSize", "assetCode", "assetName", "degreeImportance", "domainId", "applicationIds", "systemsType", "assetState", "protectionGrade", "appcheckState", "hwIsTrueShutDown", "dict<PERSON><PERSON>l", "dict<PERSON><PERSON>ue", "paramsArray", "rules", "remarkMsg", "min", "max", "message", "trigger", "required", "columns", "key", "label", "visible", "editItem", "editable", "step", "currentComponent", "auditApp", "remarkFrom", "remarkDialog", "defaultShow", "applicationVersion", "applicationVisible", "optionColumns", "checkedColumns", "table<PERSON><PERSON>", "computed", "_objectSpread2", "mapGetters", "created", "_this", "auditConfig", "response", "rows", "config<PERSON><PERSON><PERSON>", "initSearchDataAndListData", "watch", "$routerName", "val", "init", "handler", "deptId", "parseInt", "systemType", "deep", "immediate", "methods", "handleImport", "handleFileUploadProgress", "event", "file", "fileList", "handleFileSuccess", "$refs", "clearFiles", "code", "$alert", "dangerouslyUseHTMLString", "msg", "getList", "submitFileForm", "submit", "importTemplate", "download", "concat", "Date", "getTime", "_this2", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "initSearchData", "stop", "_this3", "_callee2", "_iterator", "_step", "_callee2$", "_context2", "_createForOfIteratorHelper2", "s", "n", "done", "value", "getAppCountByDict", "sent", "countByDictList", "console", "log", "t0", "error", "t1", "e", "f", "finish", "_this4", "Object", "assign", "$route", "add", "$nextTick", "handleAdd", "form", "locationId", "deptSelect", "node", "id", "handleQuery", "sortChange", "column", "prop", "order", "_this5", "listApplication", "finally", "reset", "reset<PERSON><PERSON>y", "state", "protectGrade", "isOpenNetwork", "checkOn", "domainUrl", "systemList1", "resetSelection", "systemList2", "systemList3", "systemList4", "clearRouteQueryParams", "$router", "push", "handleSelectionChange", "selection", "map", "item", "assetId", "length", "handleApply", "app", "checkBy", "submitMsg", "formMsgSubmit", "_this6", "validate", "valid", "_this7", "applyApplication", "remark", "res", "$modal", "msgSuccess", "callOffMsg", "handleUpdate", "row", "edit", "arguments", "undefined", "isEdit", "showData", "handleDelete", "_this8", "assetIds", "assetsName", "join", "confirm", "delApplication", "catch", "handleExport", "applicationChange"], "sources": ["src/views/safe/application/index.vue"], "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-tree-container\" ref=\"system\">\n      <dept-select-system\n        :key=\"deptSelectKey\"\n        ref=\"deptSelect\"\n        :is-current=\"true\"\n        @deptSelect=\"deptSelect\"\n        asset-class=\"application\"\n        :current-dept-id=\"queryParams.deptId\"\n      />\n    </div>\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          @submit.native.prevent\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"系统名称\" prop=\"assetName\">\n                <el-input\n                  v-model=\"queryParams.assetName\"\n                  placeholder=\"请输入系统名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"登录地址\">\n                <el-input v-model=\"queryParams.url\" clearable placeholder=\"请输入登录地址\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"域名\">\n                <el-input v-model=\"queryParams.domainUrl\" clearable placeholder=\"请输入域名\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button class=\"btn1\" size=\"small\" @click=\"handleQuery\">查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"24\" v-if=\"systemsType.length\">\n              <el-form-item label=\"系统类型\">\n                <SystemList\n                  ref=\"systemList1\"\n                  :systemTypes=\"systemsType\"\n                  @filterSelect=\"handleQuery\"\n                  :systemTypeVal.sync=\"queryParams.systemType\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\" v-if=\"assetState.length\">\n              <el-form-item label=\"上线状态\">\n                <SystemList\n                  ref=\"systemList2\"\n                  :systemTypes=\"assetState\"\n                  @filterSelect=\"handleQuery\"\n                  :systemTypeVal.sync=\"queryParams.state\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\" v-if=\"protectionGrade.length\">\n              <el-form-item label=\"等保等级\">\n                <SystemList\n                  ref=\"systemList3\"\n                  paramVal=\"protectGrade\"\n                  @filterSelect=\"handleQuery\"\n                  :systemTypes=\"protectionGrade\"\n                  :systemTypeVal.sync=\"queryParams.protectGrade\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\" v-if=\"appcheckState.length\">\n              <el-form-item label=\"审核状态\">\n                <SystemList\n                  ref=\"systemList4\"\n                  @filterSelect=\"handleQuery\"\n                  :systemTypes=\"appcheckState\"\n                  :systemTypeVal.sync=\"queryParams.checkOn\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\" v-if=\"appcheckState.length\">\n              <el-form-item label=\"是否开放网络\">\n                <SystemList\n                  ref=\"systemList4\"\n                  @filterSelect=\"handleQuery\"\n                  :systemTypes=\"hwIsTrueShutDown\"\n                  :systemTypeVal.sync=\"queryParams.isOpenNetwork\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\" v-if=\"appcheckState.length\">\n              <el-form-item label=\"HW时期是否可关停\">\n                <SystemList\n                  ref=\"systemList4\"\n                  @filterSelect=\"handleQuery\"\n                  :systemTypes=\"hwIsTrueShutDown\"\n                  :systemTypeVal.sync=\"queryParams.hwIsTrueShutDown\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">业务系统列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"handleAdd\"\n                  v-hasPermi=\"['safe:application:add']\"\n                >新增</el-button\n                >\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  v-hasPermi=\"['safe:application:add']\"\n                  @click=\"handleImport\"\n                >导入</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"handleDelete\"\n                  v-hasPermi=\"['safe:application:remove']\"\n                >批量删除</el-button\n                >\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                  v-hasPermi=\"['safe:application:export']\"\n                >导出</el-button\n                >\n              </el-col>\n<!--              <right-toolbar-->\n<!--                :showSearch.sync=\"showSearch\"-->\n<!--                :columns=\"columns\"-->\n<!--                @queryTable=\"getList\"-->\n<!--              ></right-toolbar>-->\n            </el-row>\n          </div>\n        </div>\n        <div class=\"tableContainer\">\n          <el-table ref=\"elTable\"\n                    v-loading=\"loading\"\n                    height=\"100%\"\n                    :data=\"applicationList\"\n                    :key=\"tableKey\"\n                    @selection-change=\"handleSelectionChange\"\n                    @sort-change=\"sortChange\">\n            <el-table-column\n              type=\"selection\"\n              width=\"55\">\n            </el-table-column>\n            <el-table-column\n              label=\"系统名称\"\n              fixed=\"left\"\n              min-width=\"150\"\n              align=\"left\"\n              prop=\"assetName\"\n              v-if=\"columns[0].visible\"\n              :sortable=\"false\"\n              show-overflow-tooltip\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.assetName || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"信息完整度\"\n              min-width=\"150\"\n              prop=\"completeness\"\n            >\n              <template slot-scope=\"scope\">\n                <div style=\"display: flex; align-items: center;\">\n                  <el-progress\n                    :color=\"scope.row.completeness > 80 ? '#67c23a' : scope.row.completeness < 60 ? '#f56c6c' : '#e6a23c'\"\n                    :percentage=\"scope.row.completeness\"\n                    :show-text=\"false\"\n                    style=\"flex: 1;\"\n                  ></el-progress>\n                  <span style=\"margin-left: 10px; width: 50px;\">\n                    {{ scope.row.completenessStr }}%\n                  </span>\n                </div>\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"所属部门\"\n              min-width=\"150\"\n              prop=\"deptName\"\n              v-if=\"columns[2].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.deptName || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"责任人员\"\n              width=\"120\"\n              prop=\"managerName\"\n              v-if=\"columns[3].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.managerName || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"联系电话\"\n              width=\"150\"\n              prop=\"managerPhone\"\n              :sortable=\"false\"\n              v-if=\"columns[4].visible\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.managerPhone || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"关键设施\"\n\n              width=\"120\"\n              prop=\"iskey\"\n              v-if=\"columns[6].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.iskey || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"登录地址\"\n              min-width=\"120\"\n              prop=\"url\"\n              v-if=\"columns[1].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.url || \"-\" }}\n<!--                <el-popover placement=\"left-end\" width=\"200\" trigger=\"hover\">\n                  <span>{{ scope.row.url || \"-\" }}</span>\n                  <span class=\"r_popover\" slot=\"reference\">\n                  {{ scope.row.url || \"-\" }}\n                </span>\n                </el-popover>-->\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"关联服务器IP\"\n              width=\"140\"\n              prop=\"ip\"\n              v-if=\"columns[21].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.ip || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"等保级别\"\n              width=\"120\"\n              prop=\"protectGrade\"\n              v-if=\"columns[5].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <dict-tag\n                  :options=\"dict.type.protection_grade\"\n                  :value=\"scope.row.protectGrade || '-'\"\n                />\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"技术架构\"\n              width=\"120\"\n              prop=\"technical\"\n              v-if=\"columns[7].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <dict-tag\n                  :options=\"dict.type.construct_type\"\n                  :value=\"scope.row.construct || '-'\"\n                />\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"登录方式\"\n              width=\"100\"\n              prop=\"loginType\"\n              v-if=\"columns[8].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <dict-tag\n                  :options=\"dict.type.app_login_type\"\n                  :value=\"scope.row.loginType || '-'\"\n                />\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"部署方式\"\n              width=\"100\"\n              prop=\"deploy\"\n              v-if=\"columns[9].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <dict-tag\n                  :options=\"dict.type.app_deploy\"\n                  :value=\"scope.row.deploy || '-'\"\n                />\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"开发厂商\"\n              min-width=\"130\"\n              prop=\"vendorName\"\n              v-if=\"columns[10].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.vendorName || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"服务对象\"\n              width=\"100\"\n              prop=\"serviceGroup\"\n              v-if=\"columns[11].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <dict-tag\n                  :options=\"dict.type.serve_group\"\n                  :value=\"scope.row.serviceGroup || '-'\"\n                />\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"授权用户数\"\n              width=\"120\"\n              prop=\"userNums\"\n              v-if=\"columns[12].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.vnlnUpdateTime || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"月均活跃人数\"\n              width=\"130\"\n              prop=\"everydayActiveNums\"\n              v-if=\"columns[13].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.everydayActiveNums || 0 }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"上线状态\"\n              width=\"100\"\n              prop=\"state\"\n              v-if=\"columns[14].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <dict-tag\n                  :options=\"dict.type.asset_state\"\n                  :value=\"scope.row.state || '-'\"\n                />\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"是否开放网络\"\n              width=\"140\"\n              prop=\"isOpenNetwork\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.isOpenNetwork == '1' ? '是' : scope.row.isOpenNetwork == '0'  ? '否' : \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"HW时期是否可关停\"\n              width=\"150\"\n              prop=\"hwIsTrueShutDown\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.hwIsTrueShutDown == '1' ? '是' : scope.row.hwIsTrueShutDown == '0'  ? '否' : \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"审核状态\"\n              width=\"100\"\n              prop=\"checkOn\"\n              v-if=\"columns[15].visible && whetherOrNotToAudit\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <dict-tag\n                  :options=\"dict.type.appcheck_state\"\n                  :value=\"scope.row.checkOn || '-'\"\n                />\n              </template>\n            </el-table-column>\n            <el-table-column label=\"严重漏洞\" align=\"left\" prop=\"criticalVulnCount\" width=\"100\">\n              <template slot-scope=\"scope\">\n                {{ scope.row.criticalVulnCount || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column label=\"高危漏洞\" align=\"left\" width=\"100\" prop=\"highVulnCount\">\n              <template slot-scope=\"scope\">\n                {{ scope.row.highVulnCount || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column label=\"中危漏洞\" align=\"left\" width=\"100\" prop=\"mediumVulnCount\">\n              <template slot-scope=\"scope\">\n                {{ scope.row.mediumVulnCount || \"-\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"备注\"\n              min-width=\"140\"\n              align=\"left\"\n              prop=\"remark\"\n              v-if=\"columns[16].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row.remark }}\n<!--                <el-popover placement=\"right-end\" width=\"200\" trigger=\"hover\">\n                  <span>{{ scope.row.remark }}</span>\n                  <span class=\"r_popover\" slot=\"reference\">\n                  {{ scope.row.remark }}\n                </span>\n                </el-popover>-->\n              </template>\n            </el-table-column>\n            <el-table-column\n              label=\"标签\"\n              min-width=\"140\"\n              align=\"left\"\n              prop=\"tags\"\n              v-if=\"columns[17].visible\"\n              :sortable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-popover placement=\"right-end\" width=\"200\" trigger=\"hover\">\n                  <span>{{ scope.row.tags }}</span>\n                  <span class=\"r_popover\" slot=\"reference\">\n                  {{ scope.row.tags || \"-\" }}\n                </span>\n                </el-popover>\n              </template>\n            </el-table-column>\n            <el-table-column :key=\"Math.random()\" label=\"操作\" align=\"left\" fixed=\"right\" width=\"200\" :show-overflow-tooltip=\"false\">\n              <template slot=\"header\">\n                <ColumnFilter :optionColumns=\"optionColumns\" :checkedColumns.sync=\"checkedColumns\" :columns=\"columns\" />\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"text\"\n                           @click=\"handleUpdate(scope.row,true,{showData:'false', isShowGap: true},true)\"\n                           v-if=\"!whetherOrNotToAudit || (scope.row.checkOn!='new' && checkPermi(['safe:application:list']))\">\n                  详情\n                </el-button>\n                <el-button size=\"mini\" type=\"text\" @click=\"handleApply(scope.row)\"\n                           v-if=\"whetherOrNotToAudit && scope.row.checkOn == 'new' && checkPermi(['safe:application:apply'])\">\n                  提交审核\n                </el-button>\n                <el-button size=\"mini\" type=\"text\"\n                           @click=\"handleUpdate(scope.row,false,{showData:'false'})\"\n                           v-if=\"whetherOrNotToAudit && scope.row.checkOn=='wait'&& checkPermi(['safe:application:check'])\">\n                  审核\n                </el-button>\n<!--                <el-button size=\"mini\" type=\"text\" @click=\"handleUpdate(scope.row)\"\n                           v-if=\"scope.row.checkOn!='wait' && checkPermi(['safe:application:edit'])\">编辑\n                </el-button>\n                <el-button size=\"mini\" type=\"text\" @click=\"handleDelete(scope.row)\" class=\"table-delBtn\"\n                           v-if=\" scope.row.checkOn!='wait' && checkPermi(['safe:application:remove'])\">删除\n                </el-button>-->\n                <el-button size=\"mini\" type=\"text\" @click=\"handleUpdate(scope.row)\"\n                           v-if=\"checkPermi(['safe:application:edit'])\">编辑\n                </el-button>\n                <el-button size=\"mini\" type=\"text\" @click=\"handleDelete(scope.row)\" class=\"table-delBtn\"\n                           v-if=\"checkPermi(['safe:application:remove'])\">删除\n                </el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n        <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\n                    @pagination=\"getList\"/>\n      </div>\n    </div>\n\n    <el-dialog title=\"填写修改记录\" :visible.sync=\"remarkDialog\" width=\"600px\" append-to-body>\n      <el-form ref=\"remarkFrom\" :model=\"remarkFrom\" :rules=\"rules\">\n        <el-form-item prop=\"remarkMsg\">\n          <el-input type=\"textarea\" :rows=\"8\" minlength=\"3\" maxlength=\"170\"\n                    v-model.trim=\"remarkFrom.remarkMsg\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"formMsgSubmit\">提交</el-button>\n        <el-button @click=\"callOffMsg\">取消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n      <el-upload\n        ref=\"upload\"\n        :limit=\"1\"\n        accept=\".xlsx, .xls\"\n        :headers=\"upload.headers\"\n        :action=\"upload.url\"\n        :disabled=\"upload.isUploading\"\n        :data=\"{'clear':upload.clear}\"\n        :on-progress=\"handleFileUploadProgress\"\n        :on-success=\"handleFileSuccess\"\n        :auto-upload=\"false\"\n        drag\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">\n          将文件拖到此处，或\n          <em>点击上传</em>\n        </div>\n        <div class=\"el-upload__tip\" style=\"color: red\" slot=\"tip\">仅允许导入xls、xlsx格式文件。</div>\n      </el-upload>\n      <el-link\n        type=\"primary\"\n        :underline=\"false\"\n        style=\"font-size:12px;vertical-align: baseline;\"\n        @click=\"importTemplate\"\n      >下载模板\n      </el-link>\n      <!--      <el-checkbox v-model=\"upload.clear\" true-label=\"1\" false-label=\"0\">导入前清空</el-checkbox>-->\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n        <el-button @click=\"upload.open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n<!--    <application-dialog\n      :title=\"title\"\n      :params.sync=\"params\"\n      @applicationChange=\"applicationChange\"\n      :applicationVisible.sync=\"applicationVisible\"/>-->\n<!--业务系统新增交互改版-->\n    <application-details\n      :title=\"title\"\n      :params.sync=\"params\"\n      @applicationChange=\"applicationChange\"\n      @deptSelectKeyChange=\"deptSelectKey++\"\n      :applicationVisible.sync=\"applicationVisible\"/>\n  </div>\n</template>\n\n<script>\nimport {\n  delApplication,\n  listApplication,\n  applyApplication,\n  getAppCountByDict,\n  auditConfig\n} from '@/api/safe/application'\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport {checkPermi, checkRole} from \"@/utils/permission\";\nimport {mapGetters} from 'vuex'\nimport {getToken} from \"@/utils/auth\";\n\nexport default {\n  name: \"Application\",\n  dicts: [\n    'protection_grade',\n    'appcheck_state',\n    'app_login_type',\n    'app_deploy',\n    'serve_group',\n    'app_state',\n    'construct_type',\n    'system_type',\n    'asset_state',\n    'protection_grade',\n    'appcheck_state'\n  ],\n  components: {\n    ApplicationDetails: () => import('@/views/hhlCode/component/applicationDetails.vue'),\n    ApplicationDialog: () => import('@/views/hhlCode/component/application/applicationInfo.vue'),\n    importThreatenInfo: () => import('@/views/basis/securityWarn/importThreatenInfo'),\n    DeptSelectSystem: () => import('@/views/safe/application/component/deptSelectSystem'),\n    typeTree: () => import('@/views/components/typeTree'),\n    vendorSelect: () => import('@/views/components/select/vendorSelect'),\n    uploadFileTable: () => import('@/views/components/table/uploadFileTable'),\n    SystemList: () => import('../../../components/SystemList')\n  },\n  data() {\n    return {\n      showAll: false,\n      upload: {\n        open: false, // 是否显示弹出层（用户导入）\n        title: \"\", // 弹出层标题（用户导入）\n        clear: \"0\",\n        isUploading: false, // 是否禁用上传\n        headers: {Authorization: \"Bearer \" + getToken()}, // 设置上传的请求头部\n        url: process.env.VUE_APP_BASE_API + \"/safe/application/importDataToJiangTong\", // 上传的地址\n      },\n      whetherOrNotToAudit: false,\n      checkPermi: checkPermi,\n      checkRole: checkRole,\n      content: \"\",\n      classId: 7,\n      className: '业务应用系统/平台',\n      typelist: [],\n      children: [],\n      loading: true, // 遮罩层\n      ids: [], // 选中数组\n      currentNames: [],\n      assetNames: [], // 选中表数组\n      single: true, // 非单个禁用\n      multiple: true, // 非多个禁用\n      showSearch: true, // 显示搜索条件\n      total: 0, // 总条数\n      applicationList: [], // 业务应用系统表格数据\n      title: \"\", // 弹出层标题\n      open: false, // 是否显示弹出层\n      params: {},\n      deptSelectKey: 0,\n      // 查询参数\n      queryParams: {\n        isAsc: 'desc',\n        orderByColumn: 'createTime',\n        pageNum: 1,\n        pageSize: 10,\n        assetCode: null,\n        assetName: null,\n        degreeImportance: null,\n        domainId: null,\n        applicationIds:[]\n      },\n      systemsType: [], // 系统类型\n      assetState: [], // 上线状态\n      protectionGrade: [], // 等保等级\n      appcheckState: [], // 审核状态\n      hwIsTrueShutDown:[{dictLabel:'是',dictValue:'1'},{dictLabel:'否',dictValue:'0'}],\n      paramsArray: ['system_type', 'asset_state', 'protection_grade', 'appcheck_state'], // 搜索参数\n      // 表单校验\n      rules: {\n        remarkMsg: [\n          { min: 3, max: 170, message: '修改记录不能少于3个字符且不能大于170个字符', trigger: 'blur' },\n          { required: true, message: '修改记录不能为空', trigger: 'blur'}\n        ]\n      },\n      columns: [\n        {key: 0, label: \"系统名称\", visible: true},\n        {key: 1, label: \"登录地址\", visible: true},\n        {key: 2, label: \"所属部门\", visible: true},\n        {key: 3, label: \"责任人员\", visible: true},\n        {key: 4, label: \"联系电话\", visible: false},\n        {key: 5, label: \"等保级别\", visible: true},\n        {key: 6, label: \"关键设施\", visible: false},\n        {key: 7, label: \"技术架构\", visible: false},\n        {key: 8, label: \"登录方式\", visible: false},\n        {key: 9, label: \"部署方式\", visible: false},\n        {key: 10, label: \"供应商\", visible: false},\n        {key: 11, label: \"服务对象\", visible: false},\n        {key: 12, label: \"授权用户数\", visible: false},\n        {key: 13, label: \"月均活跃人数\", visible: false},\n        {key: 14, label: \"上线状态\", visible: true},\n        {key: 15, label: \"审核状态\", visible: true},\n        {key: 16, label: \"备注\", visible: false},\n        {key: 17, label: \"标签\", visible: false},\n        {key: 18, label: \"严重漏洞\", visible: true},\n        {key: 19, label: \"高危漏洞\", visible: true},\n        {key: 20, label: \"中危漏洞\", visible: true},\n        {key: 21, label: \"IP地址\", visible: true},\n      ],\n      editItem: \"edit\",\n      editable: true,\n      step: 0,\n      currentComponent: 'AddApp',\n      auditApp: null,\n      remarkFrom: {\n        remarkMsg: '',\n      },\n      remarkDialog: false,\n      defaultShow: true,\n      applicationVersion: false,\n      applicationVisible: false,\n      optionColumns: [\"系统名称\", \"登录地址\", \"IP地址\",  \"所属部门\", \"责任人员\", \"联系电话\", \"等保级别\", \"关键设施\", \"技术架构\", \"登录方式\", \"部署方式\", \"供应商\", \"服务对象\", \"授权用户数\", \"月均活跃人数\", \"上线状态\", \"审核状态\", \"备注\", \"标签\", \"严重漏洞\",\"高危漏洞\",\"中危漏洞\"],\n      checkedColumns: [\"系统名称\", \"所属部门\", \"责任人员\", \"登录地址\",  \"IP地址\", \"等保级别\", \"上线状态\", \"审核状态\", \"严重漏洞\",\"高危漏洞\",\"中危漏洞\"],\n      tableKey: 1,\n    }\n  },\n  computed: {\n    ...mapGetters([\"activeNames\"]),\n  },\n  created() {\n    auditConfig({\n      pageNum: 1,\n      pageSize: 10\n    }).then(response => {\n      this.whetherOrNotToAudit = response.rows[0].configValue !== 'false';\n    })\n    this.initSearchDataAndListData();\n    // this.handleQuery();\n  },\n  watch: {\n    '$router.name'(val) {\n      if (val == 'Application') {\n        this.init();\n      } else {\n        this.open = false;\n      }\n    },\n    '$route.query': {\n      handler(val) {\n        if(val && val.deptId){\n          this.queryParams.deptId = parseInt(val.deptId);\n        }\n        if (val && val.domainId){\n          this.queryParams.domainId = val.domainId;\n        }\n        if (val && val.applicationIds){\n          this.queryParams.applicationIds = val.applicationIds;\n        }\n        if (val && val.systemType){\n          this.queryParams.systemType = val.systemType;\n        }\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  methods: {\n    // 导入功能\n    handleImport() {\n      this.upload.title = \"业务系统导入\";\n      this.upload.open = true;\n    },\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false;\n      this.upload.isUploading = false;\n      this.$refs.upload.clearFiles();\n      if (response.code == 200)\n        this.$alert(\"成功导入\", \"导入结果\", {dangerouslyUseHTMLString: true});\n      else\n        this.$alert(response.msg, \"导入结果\", {dangerouslyUseHTMLString: true});\n      this.getList();\n      this.deptSelectKey += 1;\n    },\n    submitFileForm() {\n      this.$refs.upload.submit();\n    },\n    importTemplate() {\n      this.download('safe/application/importTemplateToJiangTong', {}, `application_${new Date().getTime()}.xlsx`)\n    },\n\n    // 初始化\n    async initSearchDataAndListData() {\n      await this.initSearchData();\n      this.init();\n    },\n\n    // 初始化查询条件\n    async initSearchData() {\n      for (let params of this.paramsArray) {\n        try {\n          const response = await getAppCountByDict(params);\n          if (params === 'system_type') this.systemsType = response.data.countByDictList || [];\n          if (params === 'asset_state') this.assetState = response.data.countByDictList || [];\n          if (params === 'protection_grade') this.protectionGrade = response.data.countByDictList || [];\n          if (params === 'appcheck_state') this.appcheckState = response.data.countByDictList || [];\n          console.log(this.appcheckState)\n        } catch (error) {\n          console.error('请求失败:', error);\n        }\n      }\n    },\n\n    init() {\n      this.queryParams = Object.assign(this.queryParams, this.$route.params);\n      if (this.$route.params.add) {\n        this.$nextTick(() => {\n          this.handleAdd();\n          this.form.locationId = this.$route.params.locationId;\n          this.open = true;\n        })\n      }\n    },\n\n    //选中部门事件\n    deptSelect(node) {\n      this.queryParams.assetCode = null;\n      // this.queryParams.assetName = null;\n      if (node.id != null) {\n        this.queryParams.deptId = node.id;\n      }\n      this.handleQuery();\n    },\n\n    //排序\n    sortChange(column, prop, order) {\n      if (column.order != null) {\n        this.queryParams.isAsc = 'desc';\n      } else {\n        this.queryParams.isAsc = 'asc';\n      }\n      if (column.prop == 'state') {\n        this.queryParams.orderByColumn = \"e.state\";\n      } else\n        this.queryParams.orderByColumn = column.prop;\n      this.getList(this.queryParams);\n    },\n    /** 查询业务应用系统列表 */\n    getList() {\n\n      this.loading = true;\n      if(this.$route.params){\n        this.queryParams.ids = this.$route.params.ids;\n      }\n      listApplication(this.queryParams).then(response => {\n        this.applicationList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      }).finally(() => {\n        this.$nextTick(() => {\n          this.tableKey++;\n        })\n      })\n    },\n\n    // 表单重置\n    reset() {\n      this.editItem = \"edit\";\n      this.editable = true;\n      this.step = 0;\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.queryParams.systemType = null;\n      this.queryParams.state = null;\n      this.queryParams.protectGrade = null;\n      this.queryParams.isOpenNetwork = null;\n      this.queryParams.hwIsTrueShutDown = null;\n      this.queryParams.checkOn = null;\n      this.queryParams.assetCode = null;\n      this.queryParams.assetName = null;\n      this.queryParams.url = null;\n      this.queryParams.domainUrl = null;\n      this.queryParams.domainId = null;\n      this.queryParams.applicationIds = [];\n      this.$refs.systemList1 && this.$refs.systemList1.resetSelection();\n      this.$refs.systemList2 && this.$refs.systemList2.resetSelection();\n      this.$refs.systemList3 && this.$refs.systemList3.resetSelection();\n      this.$refs.systemList4 && this.$refs.systemList4.resetSelection();\n      this.clearRouteQueryParams();\n      this.handleQuery();\n    },\n    clearRouteQueryParams(){\n      if(this.$route.params){\n        let queryParams = this.$route.params;\n        delete queryParams.ids;\n        this.$router.push({params: queryParams})\n      }\n    },\n\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.assetId);\n      this.assetNames = selection.map(item => item.assetName);\n      this.currentNames = selection.map(item => item.assetName);\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n\n    //提交审核\n    handleApply(app) {\n      this.auditApp = app\n      if (app.checkBy) {\n        this.remarkDialog = true\n      } else {\n        this.submitMsg()\n      }\n    },\n    formMsgSubmit() {\n      this.$refs['remarkFrom'].validate((valid) => {\n        if (valid) {\n          this.submitMsg()\n        }\n      });\n    },\n    submitMsg() {\n      applyApplication({\n        assetId: this.auditApp.assetId,\n        remark: this.remarkFrom.remarkMsg\n      }).then(res => {\n        this.$modal.msgSuccess(\"已经提交审核！\");\n        this.getList();\n        this.callOffMsg()\n      })\n    },\n    callOffMsg() {\n      this.auditApp = null\n      this.remarkFrom.remarkMsg = ''\n      this.remarkDialog = false\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.title = '添加应用信息';\n      this.params = {};\n      this.applicationVisible = true;\n      // this.$tab.openPage(\n      //   \"添加应用信息\",\n      //   \"/asset-ledger/monitor2/application/info\",\n      //   {}\n      // );\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row, edit = true, data) {\n      this.reset();\n      if (row.checkOn === \"pass\" && (data === undefined || data === null)) {\n        this.title = '修改应用信息'\n        this.editable = edit\n        const assetId = row.assetId || this.ids\n        this.params = {assetId, ...data};\n        this.params.isEdit = true;\n        this.applicationVisible = true;\n        // this.$tab.openPage(title, '/asset-ledger/monitor2/application/info', params);\n      } else {\n        this.title = data ? data.showData === 'false' ? '查看应用信息' : '修改应用信息' : '修改应用信息';\n        this.editable = edit;\n        const assetId = row.assetId || this.ids;\n        this.params = {assetId, ...data};\n        this.applicationVisible = true;\n        // this.$tab.openPage(title, '/asset-ledger/monitor2/application/info', {assetId, ...data});\n      }\n    },\n\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const assetIds = row.assetId || this.ids;\n      let assetsName = \"\";\n      if (!row.assetId) {\n        assetsName = this.currentNames.join(\",\");\n      } else {\n        assetsName = row.assetName;\n      }\n\n      this.$modal.confirm('是否确认删除【' + assetsName + '】的数据项？').then(function () {\n        return delApplication(assetIds);\n      }).then(() => {\n        this.getList();\n        this.deptSelectKey += 1;\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {\n      });\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('safe/application/export', {\n        ...this.queryParams\n      }, `application_${new Date().getTime()}.xlsx`)\n    },\n    applicationChange(data){\n      this.applicationVisible = data;\n      this.getList();\n      this.initSearchData();\n    },\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"../../../assets/styles/assetIndex.scss\";\n.small-padding {\n  padding-left: 0;\n  padding-right: 0;\n  width: 150px;\n}\n\n.operate {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 30px;\n}\n\n.option-app {\n  margin-right: 10px;\n}\n\n.r_popover {\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  width: 150px;\n  overflow: hidden;\n}\n\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;AA+kBA,IAAAA,YAAA,GAAAC,OAAA;AAOAA,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,KAAA,GACA,oBACA,kBACA,kBACA,cACA,eACA,aACA,kBACA,eACA,eACA,oBACA,iBACA;EACAC,UAAA;IACAC,kBAAA,WAAAA,mBAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAZ,OAAA;MAAA;IAAA;IACAa,iBAAA,WAAAA,kBAAA;MAAA,OAAAL,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAZ,OAAA;MAAA;IAAA;IACAc,kBAAA,WAAAA,mBAAA;MAAA,OAAAN,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAZ,OAAA;MAAA;IAAA;IACAe,gBAAA,WAAAA,iBAAA;MAAA,OAAAP,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAZ,OAAA;MAAA;IAAA;IACAgB,QAAA,WAAAA,SAAA;MAAA,OAAAR,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAZ,OAAA;MAAA;IAAA;IACAiB,YAAA,WAAAA,aAAA;MAAA,OAAAT,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAZ,OAAA;MAAA;IAAA;IACAkB,eAAA,WAAAA,gBAAA;MAAA,OAAAV,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAZ,OAAA;MAAA;IAAA;IACAmB,UAAA,WAAAA,WAAA;MAAA,OAAAX,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAZ,OAAA;MAAA;IAAA;EACA;EACAoB,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,MAAA;QACAC,IAAA;QAAA;QACAC,KAAA;QAAA;QACAC,KAAA;QACAC,WAAA;QAAA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;QAAA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACAC,mBAAA;MACAC,UAAA,EAAAA,sBAAA;MACAC,SAAA,EAAAA,qBAAA;MACAC,OAAA;MACAC,OAAA;MACAC,SAAA;MACAC,QAAA;MACAC,QAAA;MACAC,OAAA;MAAA;MACAC,GAAA;MAAA;MACAC,YAAA;MACAC,UAAA;MAAA;MACAC,MAAA;MAAA;MACAC,QAAA;MAAA;MACAC,UAAA;MAAA;MACAC,KAAA;MAAA;MACAC,eAAA;MAAA;MACA1B,KAAA;MAAA;MACAD,IAAA;MAAA;MACA4B,MAAA;MACAC,aAAA;MACA;MACAC,WAAA;QACAC,KAAA;QACAC,aAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,gBAAA;QACAC,QAAA;QACAC,cAAA;MACA;MACAC,WAAA;MAAA;MACAC,UAAA;MAAA;MACAC,eAAA;MAAA;MACAC,aAAA;MAAA;MACAC,gBAAA;QAAAC,SAAA;QAAAC,SAAA;MAAA;QAAAD,SAAA;QAAAC,SAAA;MAAA;MACAC,WAAA;MAAA;MACA;MACAC,KAAA;QACAC,SAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,QAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAE,OAAA,GACA;QAAAC,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,EACA;MACAC,QAAA;MACAC,QAAA;MACAC,IAAA;MACAC,gBAAA;MACAC,QAAA;MACAC,UAAA;QACAf,SAAA;MACA;MACAgB,YAAA;MACAC,WAAA;MACAC,kBAAA;MACAC,kBAAA;MACAC,aAAA;MACAC,cAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA,MAAAC,cAAA,CAAApF,OAAA,MACA,IAAAqF,gBAAA,mBACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,wBAAA;MACA5C,OAAA;MACAC,QAAA;IACA,GAAA/C,IAAA,WAAA2F,QAAA;MACAF,KAAA,CAAAjE,mBAAA,GAAAmE,QAAA,CAAAC,IAAA,IAAAC,WAAA;IACA;IACA,KAAAC,yBAAA;IACA;EACA;EACAC,KAAA;IACA,yBAAAC,YAAAC,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,IAAA;MACA;QACA,KAAArF,IAAA;MACA;IACA;IACA;MACAsF,OAAA,WAAAA,QAAAF,GAAA;QACA,IAAAA,GAAA,IAAAA,GAAA,CAAAG,MAAA;UACA,KAAAzD,WAAA,CAAAyD,MAAA,GAAAC,QAAA,CAAAJ,GAAA,CAAAG,MAAA;QACA;QACA,IAAAH,GAAA,IAAAA,GAAA,CAAA9C,QAAA;UACA,KAAAR,WAAA,CAAAQ,QAAA,GAAA8C,GAAA,CAAA9C,QAAA;QACA;QACA,IAAA8C,GAAA,IAAAA,GAAA,CAAA7C,cAAA;UACA,KAAAT,WAAA,CAAAS,cAAA,GAAA6C,GAAA,CAAA7C,cAAA;QACA;QACA,IAAA6C,GAAA,IAAAA,GAAA,CAAAK,UAAA;UACA,KAAA3D,WAAA,CAAA2D,UAAA,GAAAL,GAAA,CAAAK,UAAA;QACA;MACA;MACAC,IAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAA9F,MAAA,CAAAE,KAAA;MACA,KAAAF,MAAA,CAAAC,IAAA;IACA;IACA8F,wBAAA,WAAAA,yBAAAC,KAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,KAAAlG,MAAA,CAAAI,WAAA;IACA;IACA+F,iBAAA,WAAAA,kBAAApB,QAAA,EAAAkB,IAAA,EAAAC,QAAA;MACA,KAAAlG,MAAA,CAAAC,IAAA;MACA,KAAAD,MAAA,CAAAI,WAAA;MACA,KAAAgG,KAAA,CAAApG,MAAA,CAAAqG,UAAA;MACA,IAAAtB,QAAA,CAAAuB,IAAA,SACA,KAAAC,MAAA;QAAAC,wBAAA;MAAA,QAEA,KAAAD,MAAA,CAAAxB,QAAA,CAAA0B,GAAA;QAAAD,wBAAA;MAAA;MACA,KAAAE,OAAA;MACA,KAAA5E,aAAA;IACA;IACA6E,cAAA,WAAAA,eAAA;MACA,KAAAP,KAAA,CAAApG,MAAA,CAAA4G,MAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MACA,KAAAC,QAAA,kEAAAC,MAAA,KAAAC,IAAA,GAAAC,OAAA;IACA;IAEA;IACA/B,yBAAA,WAAAA,0BAAA;MAAA,IAAAgC,MAAA;MAAA,WAAAC,kBAAA,CAAA7H,OAAA,mBAAA8H,oBAAA,CAAA9H,OAAA,IAAA+H,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAA9H,OAAA,IAAAiI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACAT,MAAA,CAAAU,cAAA;YAAA;cACAV,MAAA,CAAA5B,IAAA;YAAA;YAAA;cAAA,OAAAmC,QAAA,CAAAI,IAAA;UAAA;QAAA,GAAAP,OAAA;MAAA;IACA;IAEA;IACAM,cAAA,WAAAA,eAAA;MAAA,IAAAE,MAAA;MAAA,WAAAX,kBAAA,CAAA7H,OAAA,mBAAA8H,oBAAA,CAAA9H,OAAA,IAAA+H,IAAA,UAAAU,SAAA;QAAA,IAAAC,SAAA,EAAAC,KAAA,EAAApG,MAAA,EAAAkD,QAAA;QAAA,WAAAqC,oBAAA,CAAA9H,OAAA,IAAAiI,IAAA,UAAAW,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAT,IAAA,GAAAS,SAAA,CAAAR,IAAA;YAAA;cAAAK,SAAA,OAAAI,2BAAA,CAAA9I,OAAA,EACAwI,MAAA,CAAA9E,WAAA;cAAAmF,SAAA,CAAAT,IAAA;cAAAM,SAAA,CAAAK,CAAA;YAAA;cAAA,KAAAJ,KAAA,GAAAD,SAAA,CAAAM,CAAA,IAAAC,IAAA;gBAAAJ,SAAA,CAAAR,IAAA;gBAAA;cAAA;cAAA9F,MAAA,GAAAoG,KAAA,CAAAO,KAAA;cAAAL,SAAA,CAAAT,IAAA;cAAAS,SAAA,CAAAR,IAAA;cAAA,OAEA,IAAAc,8BAAA,EAAA5G,MAAA;YAAA;cAAAkD,QAAA,GAAAoD,SAAA,CAAAO,IAAA;cACA,IAAA7G,MAAA,oBAAAiG,MAAA,CAAArF,WAAA,GAAAsC,QAAA,CAAAjF,IAAA,CAAA6I,eAAA;cACA,IAAA9G,MAAA,oBAAAiG,MAAA,CAAApF,UAAA,GAAAqC,QAAA,CAAAjF,IAAA,CAAA6I,eAAA;cACA,IAAA9G,MAAA,yBAAAiG,MAAA,CAAAnF,eAAA,GAAAoC,QAAA,CAAAjF,IAAA,CAAA6I,eAAA;cACA,IAAA9G,MAAA,uBAAAiG,MAAA,CAAAlF,aAAA,GAAAmC,QAAA,CAAAjF,IAAA,CAAA6I,eAAA;cACAC,OAAA,CAAAC,GAAA,CAAAf,MAAA,CAAAlF,aAAA;cAAAuF,SAAA,CAAAR,IAAA;cAAA;YAAA;cAAAQ,SAAA,CAAAT,IAAA;cAAAS,SAAA,CAAAW,EAAA,GAAAX,SAAA;cAEAS,OAAA,CAAAG,KAAA,UAAAZ,SAAA,CAAAW,EAAA;YAAA;cAAAX,SAAA,CAAAR,IAAA;cAAA;YAAA;cAAAQ,SAAA,CAAAR,IAAA;cAAA;YAAA;cAAAQ,SAAA,CAAAT,IAAA;cAAAS,SAAA,CAAAa,EAAA,GAAAb,SAAA;cAAAH,SAAA,CAAAiB,CAAA,CAAAd,SAAA,CAAAa,EAAA;YAAA;cAAAb,SAAA,CAAAT,IAAA;cAAAM,SAAA,CAAAkB,CAAA;cAAA,OAAAf,SAAA,CAAAgB,MAAA;YAAA;YAAA;cAAA,OAAAhB,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAGA;IAEAzC,IAAA,WAAAA,KAAA;MAAA,IAAA8D,MAAA;MACA,KAAArH,WAAA,GAAAsH,MAAA,CAAAC,MAAA,MAAAvH,WAAA,OAAAwH,MAAA,CAAA1H,MAAA;MACA,SAAA0H,MAAA,CAAA1H,MAAA,CAAA2H,GAAA;QACA,KAAAC,SAAA;UACAL,MAAA,CAAAM,SAAA;UACAN,MAAA,CAAAO,IAAA,CAAAC,UAAA,GAAAR,MAAA,CAAAG,MAAA,CAAA1H,MAAA,CAAA+H,UAAA;UACAR,MAAA,CAAAnJ,IAAA;QACA;MACA;IACA;IAEA;IACA4J,UAAA,WAAAA,WAAAC,IAAA;MACA,KAAA/H,WAAA,CAAAK,SAAA;MACA;MACA,IAAA0H,IAAA,CAAAC,EAAA;QACA,KAAAhI,WAAA,CAAAyD,MAAA,GAAAsE,IAAA,CAAAC,EAAA;MACA;MACA,KAAAC,WAAA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAAC,MAAA,EAAAC,IAAA,EAAAC,KAAA;MACA,IAAAF,MAAA,CAAAE,KAAA;QACA,KAAArI,WAAA,CAAAC,KAAA;MACA;QACA,KAAAD,WAAA,CAAAC,KAAA;MACA;MACA,IAAAkI,MAAA,CAAAC,IAAA;QACA,KAAApI,WAAA,CAAAE,aAAA;MACA,OACA,KAAAF,WAAA,CAAAE,aAAA,GAAAiI,MAAA,CAAAC,IAAA;MACA,KAAAzD,OAAA,MAAA3E,WAAA;IACA;IACA,iBACA2E,OAAA,WAAAA,QAAA;MAAA,IAAA2D,MAAA;MAEA,KAAAjJ,OAAA;MACA,SAAAmI,MAAA,CAAA1H,MAAA;QACA,KAAAE,WAAA,CAAAV,GAAA,QAAAkI,MAAA,CAAA1H,MAAA,CAAAR,GAAA;MACA;MACA,IAAAiJ,4BAAA,OAAAvI,WAAA,EAAA3C,IAAA,WAAA2F,QAAA;QACAsF,MAAA,CAAAzI,eAAA,GAAAmD,QAAA,CAAAC,IAAA;QACAqF,MAAA,CAAA1I,KAAA,GAAAoD,QAAA,CAAApD,KAAA;QACA0I,MAAA,CAAAjJ,OAAA;MACA,GAAAmJ,OAAA;QACAF,MAAA,CAAAZ,SAAA;UACAY,MAAA,CAAA7F,QAAA;QACA;MACA;IACA;IAEA;IACAgG,KAAA,WAAAA,MAAA;MACA,KAAA5G,QAAA;MACA,KAAAC,QAAA;MACA,KAAAC,IAAA;IACA;IACA,aACAkG,WAAA,WAAAA,YAAA;MACA,KAAAjI,WAAA,CAAAG,OAAA;MACA,KAAAwE,OAAA;IACA;IACA,aACA+D,UAAA,WAAAA,WAAA;MACA,KAAA1I,WAAA,CAAA2D,UAAA;MACA,KAAA3D,WAAA,CAAA2I,KAAA;MACA,KAAA3I,WAAA,CAAA4I,YAAA;MACA,KAAA5I,WAAA,CAAA6I,aAAA;MACA,KAAA7I,WAAA,CAAAc,gBAAA;MACA,KAAAd,WAAA,CAAA8I,OAAA;MACA,KAAA9I,WAAA,CAAAK,SAAA;MACA,KAAAL,WAAA,CAAAM,SAAA;MACA,KAAAN,WAAA,CAAAvB,GAAA;MACA,KAAAuB,WAAA,CAAA+I,SAAA;MACA,KAAA/I,WAAA,CAAAQ,QAAA;MACA,KAAAR,WAAA,CAAAS,cAAA;MACA,KAAA4D,KAAA,CAAA2E,WAAA,SAAA3E,KAAA,CAAA2E,WAAA,CAAAC,cAAA;MACA,KAAA5E,KAAA,CAAA6E,WAAA,SAAA7E,KAAA,CAAA6E,WAAA,CAAAD,cAAA;MACA,KAAA5E,KAAA,CAAA8E,WAAA,SAAA9E,KAAA,CAAA8E,WAAA,CAAAF,cAAA;MACA,KAAA5E,KAAA,CAAA+E,WAAA,SAAA/E,KAAA,CAAA+E,WAAA,CAAAH,cAAA;MACA,KAAAI,qBAAA;MACA,KAAApB,WAAA;IACA;IACAoB,qBAAA,WAAAA,sBAAA;MACA,SAAA7B,MAAA,CAAA1H,MAAA;QACA,IAAAE,WAAA,QAAAwH,MAAA,CAAA1H,MAAA;QACA,OAAAE,WAAA,CAAAV,GAAA;QACA,KAAAgK,OAAA,CAAAC,IAAA;UAAAzJ,MAAA,EAAAE;QAAA;MACA;IACA;IAEA;IACAwJ,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAnK,GAAA,GAAAmK,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,OAAA;MAAA;MACA,KAAApK,UAAA,GAAAiK,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAArJ,SAAA;MAAA;MACA,KAAAf,YAAA,GAAAkK,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAArJ,SAAA;MAAA;MACA,KAAAb,MAAA,GAAAgK,SAAA,CAAAI,MAAA;MACA,KAAAnK,QAAA,IAAA+J,SAAA,CAAAI,MAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAAC,GAAA;MACA,KAAA9H,QAAA,GAAA8H,GAAA;MACA,IAAAA,GAAA,CAAAC,OAAA;QACA,KAAA7H,YAAA;MACA;QACA,KAAA8H,SAAA;MACA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAA9F,KAAA,eAAA+F,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,MAAA,CAAAF,SAAA;QACA;MACA;IACA;IACAA,SAAA,WAAAA,UAAA;MAAA,IAAAK,MAAA;MACA,IAAAC,6BAAA;QACAX,OAAA,OAAA3H,QAAA,CAAA2H,OAAA;QACAY,MAAA,OAAAtI,UAAA,CAAAf;MACA,GAAA9D,IAAA,WAAAoN,GAAA;QACAH,MAAA,CAAAI,MAAA,CAAAC,UAAA;QACAL,MAAA,CAAA3F,OAAA;QACA2F,MAAA,CAAAM,UAAA;MACA;IACA;IACAA,UAAA,WAAAA,WAAA;MACA,KAAA3I,QAAA;MACA,KAAAC,UAAA,CAAAf,SAAA;MACA,KAAAgB,YAAA;IACA;IACA,aACAwF,SAAA,WAAAA,UAAA;MACA,KAAAc,KAAA;MACA,KAAAtK,KAAA;MACA,KAAA2B,MAAA;MACA,KAAAwC,kBAAA;MACA;MACA;MACA;MACA;MACA;IACA;IACA,aACAuI,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAnB,MAAA,QAAAmB,SAAA,QAAAC,SAAA,GAAAD,SAAA;MAAA,IAAAjN,IAAA,GAAAiN,SAAA,CAAAnB,MAAA,OAAAmB,SAAA,MAAAC,SAAA;MACA,KAAAxC,KAAA;MACA,IAAAqC,GAAA,CAAAhC,OAAA,gBAAA/K,IAAA,KAAAkN,SAAA,IAAAlN,IAAA;QACA,KAAAI,KAAA;QACA,KAAA2D,QAAA,GAAAiJ,IAAA;QACA,IAAAnB,OAAA,GAAAkB,GAAA,CAAAlB,OAAA,SAAAtK,GAAA;QACA,KAAAQ,MAAA,OAAA6C,cAAA,CAAApF,OAAA;UAAAqM,OAAA,EAAAA;QAAA,GAAA7L,IAAA;QACA,KAAA+B,MAAA,CAAAoL,MAAA;QACA,KAAA5I,kBAAA;QACA;MACA;QACA,KAAAnE,KAAA,GAAAJ,IAAA,GAAAA,IAAA,CAAAoN,QAAA;QACA,KAAArJ,QAAA,GAAAiJ,IAAA;QACA,IAAAnB,QAAA,GAAAkB,GAAA,CAAAlB,OAAA,SAAAtK,GAAA;QACA,KAAAQ,MAAA,OAAA6C,cAAA,CAAApF,OAAA;UAAAqM,OAAA,EAAAA;QAAA,GAAA7L,IAAA;QACA,KAAAuE,kBAAA;QACA;MACA;IACA;IAEA,aACA8I,YAAA,WAAAA,aAAAN,GAAA;MAAA,IAAAO,MAAA;MACA,IAAAC,QAAA,GAAAR,GAAA,CAAAlB,OAAA,SAAAtK,GAAA;MACA,IAAAiM,UAAA;MACA,KAAAT,GAAA,CAAAlB,OAAA;QACA2B,UAAA,QAAAhM,YAAA,CAAAiM,IAAA;MACA;QACAD,UAAA,GAAAT,GAAA,CAAAxK,SAAA;MACA;MAEA,KAAAoK,MAAA,CAAAe,OAAA,aAAAF,UAAA,aAAAlO,IAAA;QACA,WAAAqO,2BAAA,EAAAJ,QAAA;MACA,GAAAjO,IAAA;QACAgO,MAAA,CAAA1G,OAAA;QACA0G,MAAA,CAAAtL,aAAA;QACAsL,MAAA,CAAAX,MAAA,CAAAC,UAAA;MACA,GAAAgB,KAAA,cACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAA7G,QAAA,gCAAApC,cAAA,CAAApF,OAAA,MACA,KAAAyC,WAAA,kBAAAgF,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACA2G,iBAAA,WAAAA,kBAAA9N,IAAA;MACA,KAAAuE,kBAAA,GAAAvE,IAAA;MACA,KAAA4G,OAAA;MACA,KAAAkB,cAAA;IACA;EACA;AACA", "ignoreList": []}]}