{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\components\\SystemList\\index.vue?vue&type=style&index=0&id=8e6f0040&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\components\\SystemList\\index.vue", "mtime": 1756459866243}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouc3lzdGVtLWxpc3QgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1mbG93OiB3cmFwOwp9Cgouc3lzdGVtLWxpc3QgbGkgewogIG1hcmdpbi1yaWdodDogMTVweDsKICBmb250LXNpemU6IDE0cHg7CiAgY29sb3I6ICM0MzgyRkQ7Cn0KCi5zeXN0ZW0tbGlzdCBsaS5zZWxlY3RlZCB7CiAgcGFkZGluZzogMCA1cHg7CiAgY29sb3I6ICNGRkZGRkY7IC8vIOiuvue9rumAieS4reWQjueahOminOiJsgogIGJhY2tncm91bmQ6ICM0MzgyRkQ7Cn0KCi5zeXN0ZW0tbGlzdCBzcGFuIHsKICBjdXJzb3I6IHBvaW50ZXI7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/SystemList", "sourcesContent": ["<template>\n  <ul class=\"system-list\">\n    <li v-for=\"dict in systemTypes\" :key=\"dict.value\" :class=\"{ 'selected': dict.dictValue === selectedSystemType }\">\n      <span @click=\"selectSystemType(dict.dictValue)\">{{ `${dict.dictLabel}${dict.count != null ? '('+dict.count+')' : ''}` }}</span>\n    </li>\n  </ul>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      selectedSystemType: null // 添加状态变量\n    }\n  },\n  props: {\n    systemTypes: {\n      type: Array,\n      required: true\n    },\n    systemTypeVal: {\n      type: [String, Number],\n      default: null\n    },\n  },\n  methods: {\n    selectSystemType(val) {\n      if (this.selectedSystemType === val) {\n        this.selectedSystemType = null; // 取消选中\n        this.$emit('update:systemTypeVal', null);\n      } else {\n        this.selectedSystemType = val; // 选中\n        this.$emit('update:systemTypeVal', val);\n      }\n      this.$emit('filterSelect');\n    },\n    resetSelection() {\n      this.selectedSystemType = null; // 重置选择状态\n      // this.$emit('update:systemTypeVal', null); // 通知父组件选择已重置\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n.system-list {\n  display: flex;\n  flex-flow: wrap;\n}\n\n.system-list li {\n  margin-right: 15px;\n  font-size: 14px;\n  color: #4382FD;\n}\n\n.system-list li.selected {\n  padding: 0 5px;\n  color: #FFFFFF; // 设置选中后的颜色\n  background: #4382FD;\n}\n\n.system-list span {\n  cursor: pointer;\n}\n</style>\n"]}]}