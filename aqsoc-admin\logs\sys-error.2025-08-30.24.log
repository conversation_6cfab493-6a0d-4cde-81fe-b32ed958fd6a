2025-08-30 10:15:43.485 [async-task-pool183] ERROR c.r.f.a.s.i.ApiResultSeviceImpl - [dealFlowDetailResult,215] - 非凡流量入库出错: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
2025-08-30 10:15:43.526 [async-task-pool162] ERROR c.r.f.a.s.i.ApiResultSeviceImpl - [dealFlowDetailResult,215] - 非凡流量入库出错: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
2025-08-30 10:15:49.451 [async-task-pool157] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=64, attackId=128, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:15:49.480 [async-task-pool0] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=63, attackId=127, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:15:55.943 [async-task-pool42] ERROR c.r.f.a.s.i.ApiResultSeviceImpl - [dealFlowDetailResult,215] - 非凡流量入库出错: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
2025-08-30 10:15:56.557 [async-task-pool96] ERROR c.r.f.a.s.i.ApiResultSeviceImpl - [dealFlowDetailResult,215] - 非凡流量入库出错: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
2025-08-30 10:15:59.609 [async-task-pool101] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=63, attackId=127, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:15:59.609 [async-task-pool174] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=64, attackId=128, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:16:04.006 [async-task-pool34] ERROR c.r.m.c.c.FfsafeClientService - [processBatch,672] - 处理流量风险资产数据失败
org.apache.ibatis.exceptions.PersistenceException: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowRiskAssetsMapper.batchUpdateFfsafeFlowRiskAssets (batch index #1) failed. Cause: java.sql.BatchUpdateException: Lock wait timeout exceeded; try restarting transaction
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowRiskAssetsMapper.batchUpdateFfsafeFlowRiskAssets (batch index #1) failed. Cause: java.sql.BatchUpdateException: Lock wait timeout exceeded; try restarting transaction
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.flushStatements(DefaultSqlSession.java:251)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processBatch(FfsafeClientService.java:659)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullFlowRiskAssets(FfsafeClientService.java:542)
	at com.ruoyi.ffsafe.api.event.PullFlowRiskAssetsEvent$1.lambda$null$0(PullFlowRiskAssetsEvent.java:116)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowRiskAssetsMapper.batchUpdateFfsafeFlowRiskAssets (batch index #1) failed. Cause: java.sql.BatchUpdateException: Lock wait timeout exceeded; try restarting transaction
	at org.apache.ibatis.executor.BatchExecutor.doFlushStatements(BatchExecutor.java:149)
	at org.apache.ibatis.executor.BaseExecutor.flushStatements(BaseExecutor.java:129)
	at org.apache.ibatis.executor.BaseExecutor.flushStatements(BaseExecutor.java:122)
	at org.apache.ibatis.executor.CachingExecutor.flushStatements(CachingExecutor.java:114)
	at sun.reflect.GeneratedMethodAccessor743.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy151.flushStatements(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor743.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy151.flushStatements(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.flushStatements(DefaultSqlSession.java:249)
	... 7 common frames omitted
Caused by: java.sql.BatchUpdateException: Lock wait timeout exceeded; try restarting transaction
	at sun.reflect.GeneratedConstructorAccessor201.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.util.Util.handleNewInstance(Util.java:192)
	at com.mysql.cj.util.Util.getInstance(Util.java:167)
	at com.mysql.cj.util.Util.getInstance(Util.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418)
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.executeBatch(DruidPooledPreparedStatement.java:551)
	at sun.reflect.GeneratedMethodAccessor353.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:78)
	at com.sun.proxy.$Proxy154.executeBatch(Unknown Source)
	at org.apache.ibatis.executor.BatchExecutor.doFlushStatements(BatchExecutor.java:123)
	... 21 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException: Lock wait timeout exceeded; try restarting transaction
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:123)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795)
	... 35 common frames omitted
2025-08-30 10:16:04.006 [async-task-pool72] ERROR c.r.m.c.c.FfsafeClientService - [processBatch,672] - 处理流量风险资产数据失败
org.apache.ibatis.exceptions.PersistenceException: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowRiskAssetsMapper.batchUpdateFfsafeFlowRiskAssets (batch index #1) failed. Cause: java.sql.BatchUpdateException: Lock wait timeout exceeded; try restarting transaction
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowRiskAssetsMapper.batchUpdateFfsafeFlowRiskAssets (batch index #1) failed. Cause: java.sql.BatchUpdateException: Lock wait timeout exceeded; try restarting transaction
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.flushStatements(DefaultSqlSession.java:251)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processBatch(FfsafeClientService.java:659)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullFlowRiskAssets(FfsafeClientService.java:542)
	at com.ruoyi.ffsafe.api.event.PullFlowRiskAssetsEvent$1.lambda$null$0(PullFlowRiskAssetsEvent.java:116)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowRiskAssetsMapper.batchUpdateFfsafeFlowRiskAssets (batch index #1) failed. Cause: java.sql.BatchUpdateException: Lock wait timeout exceeded; try restarting transaction
	at org.apache.ibatis.executor.BatchExecutor.doFlushStatements(BatchExecutor.java:149)
	at org.apache.ibatis.executor.BaseExecutor.flushStatements(BaseExecutor.java:129)
	at org.apache.ibatis.executor.BaseExecutor.flushStatements(BaseExecutor.java:122)
	at org.apache.ibatis.executor.CachingExecutor.flushStatements(CachingExecutor.java:114)
	at sun.reflect.GeneratedMethodAccessor743.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy151.flushStatements(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor743.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy151.flushStatements(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.flushStatements(DefaultSqlSession.java:249)
	... 7 common frames omitted
Caused by: java.sql.BatchUpdateException: Lock wait timeout exceeded; try restarting transaction
	at sun.reflect.GeneratedConstructorAccessor201.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.util.Util.handleNewInstance(Util.java:192)
	at com.mysql.cj.util.Util.getInstance(Util.java:167)
	at com.mysql.cj.util.Util.getInstance(Util.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418)
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.executeBatch(DruidPooledPreparedStatement.java:551)
	at sun.reflect.GeneratedMethodAccessor353.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:78)
	at com.sun.proxy.$Proxy154.executeBatch(Unknown Source)
	at org.apache.ibatis.executor.BatchExecutor.doFlushStatements(BatchExecutor.java:123)
	... 21 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException: Lock wait timeout exceeded; try restarting transaction
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:123)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795)
	... 35 common frames omitted
2025-08-30 10:16:08.811 [async-task-pool98] ERROR c.r.f.a.s.i.ApiResultSeviceImpl - [dealFlowDetailResult,215] - 非凡流量入库出错: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
2025-08-30 10:16:09.697 [async-task-pool49] ERROR c.r.f.a.s.i.ApiResultSeviceImpl - [dealFlowDetailResult,215] - 非凡流量入库出错: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
2025-08-30 10:16:09.756 [async-task-pool125] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=63, attackId=127, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:16:09.769 [async-task-pool186] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=64, attackId=128, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:16:19.934 [async-task-pool142] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=63, attackId=127, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:16:19.949 [async-task-pool35] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=64, attackId=128, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:16:23.893 [async-task-pool163] ERROR c.r.f.a.s.i.ApiResultSeviceImpl - [dealFlowDetailResult,215] - 非凡流量入库出错: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
2025-08-30 10:16:23.991 [async-task-pool31] ERROR c.r.f.a.s.i.ApiResultSeviceImpl - [dealFlowDetailResult,215] - 非凡流量入库出错: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
2025-08-30 10:16:30.060 [async-task-pool189] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=63, attackId=127, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:16:30.087 [async-task-pool110] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=64, attackId=128, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:16:37.335 [async-task-pool185] ERROR c.r.f.a.s.i.ApiResultSeviceImpl - [dealFlowDetailResult,215] - 非凡流量入库出错: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
2025-08-30 10:16:37.597 [async-task-pool32] ERROR c.r.f.a.s.i.ApiResultSeviceImpl - [dealFlowDetailResult,215] - 非凡流量入库出错: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
2025-08-30 10:16:40.638 [async-task-pool78] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=64, attackId=128, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:16:40.642 [async-task-pool109] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=63, attackId=127, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:16:50.022 [async-task-pool172] ERROR c.r.f.a.s.i.ApiResultSeviceImpl - [dealFlowDetailResult,215] - 非凡流量入库出错: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
2025-08-30 10:16:52.547 [async-task-pool13] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=64, attackId=128, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:16:52.547 [async-task-pool86] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=63, attackId=127, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:16:52.984 [async-task-pool191] ERROR c.r.f.a.s.i.ApiResultSeviceImpl - [dealFlowDetailResult,215] - 非凡流量入库出错: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
2025-08-30 10:17:03.705 [async-task-pool102] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=63, attackId=127, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:17:03.714 [async-task-pool74] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=64, attackId=128, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:17:04.320 [async-task-pool71] ERROR c.r.m.c.c.FfsafeClientService - [processBatch,672] - 处理流量风险资产数据失败
org.apache.ibatis.exceptions.PersistenceException: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowRiskAssetsMapper.batchUpdateFfsafeFlowRiskAssets (batch index #1) failed. Cause: java.sql.BatchUpdateException: Lock wait timeout exceeded; try restarting transaction
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowRiskAssetsMapper.batchUpdateFfsafeFlowRiskAssets (batch index #1) failed. Cause: java.sql.BatchUpdateException: Lock wait timeout exceeded; try restarting transaction
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.flushStatements(DefaultSqlSession.java:251)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processBatch(FfsafeClientService.java:659)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullFlowRiskAssets(FfsafeClientService.java:542)
	at com.ruoyi.ffsafe.api.event.PullFlowRiskAssetsEvent$1.lambda$null$0(PullFlowRiskAssetsEvent.java:116)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowRiskAssetsMapper.batchUpdateFfsafeFlowRiskAssets (batch index #1) failed. Cause: java.sql.BatchUpdateException: Lock wait timeout exceeded; try restarting transaction
	at org.apache.ibatis.executor.BatchExecutor.doFlushStatements(BatchExecutor.java:149)
	at org.apache.ibatis.executor.BaseExecutor.flushStatements(BaseExecutor.java:129)
	at org.apache.ibatis.executor.BaseExecutor.flushStatements(BaseExecutor.java:122)
	at org.apache.ibatis.executor.CachingExecutor.flushStatements(CachingExecutor.java:114)
	at sun.reflect.GeneratedMethodAccessor743.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy151.flushStatements(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor743.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy151.flushStatements(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.flushStatements(DefaultSqlSession.java:249)
	... 7 common frames omitted
Caused by: java.sql.BatchUpdateException: Lock wait timeout exceeded; try restarting transaction
	at sun.reflect.GeneratedConstructorAccessor201.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.util.Util.handleNewInstance(Util.java:192)
	at com.mysql.cj.util.Util.getInstance(Util.java:167)
	at com.mysql.cj.util.Util.getInstance(Util.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418)
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.executeBatch(DruidPooledPreparedStatement.java:551)
	at sun.reflect.GeneratedMethodAccessor353.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:78)
	at com.sun.proxy.$Proxy154.executeBatch(Unknown Source)
	at org.apache.ibatis.executor.BatchExecutor.doFlushStatements(BatchExecutor.java:123)
	... 21 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException: Lock wait timeout exceeded; try restarting transaction
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:123)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795)
	... 35 common frames omitted
2025-08-30 10:17:04.324 [async-task-pool95] ERROR c.r.m.c.c.FfsafeClientService - [processBatch,672] - 处理流量风险资产数据失败
org.apache.ibatis.exceptions.PersistenceException: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowRiskAssetsMapper.batchUpdateFfsafeFlowRiskAssets (batch index #1) failed. Cause: java.sql.BatchUpdateException: Lock wait timeout exceeded; try restarting transaction
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowRiskAssetsMapper.batchUpdateFfsafeFlowRiskAssets (batch index #1) failed. Cause: java.sql.BatchUpdateException: Lock wait timeout exceeded; try restarting transaction
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.flushStatements(DefaultSqlSession.java:251)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processBatch(FfsafeClientService.java:659)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullFlowRiskAssets(FfsafeClientService.java:542)
	at com.ruoyi.ffsafe.api.event.PullFlowRiskAssetsEvent$1.lambda$null$0(PullFlowRiskAssetsEvent.java:116)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowRiskAssetsMapper.batchUpdateFfsafeFlowRiskAssets (batch index #1) failed. Cause: java.sql.BatchUpdateException: Lock wait timeout exceeded; try restarting transaction
	at org.apache.ibatis.executor.BatchExecutor.doFlushStatements(BatchExecutor.java:149)
	at org.apache.ibatis.executor.BaseExecutor.flushStatements(BaseExecutor.java:129)
	at org.apache.ibatis.executor.BaseExecutor.flushStatements(BaseExecutor.java:122)
	at org.apache.ibatis.executor.CachingExecutor.flushStatements(CachingExecutor.java:114)
	at sun.reflect.GeneratedMethodAccessor743.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy151.flushStatements(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor743.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy151.flushStatements(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.flushStatements(DefaultSqlSession.java:249)
	... 7 common frames omitted
Caused by: java.sql.BatchUpdateException: Lock wait timeout exceeded; try restarting transaction
	at sun.reflect.GeneratedConstructorAccessor201.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.util.Util.handleNewInstance(Util.java:192)
	at com.mysql.cj.util.Util.getInstance(Util.java:167)
	at com.mysql.cj.util.Util.getInstance(Util.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418)
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.executeBatch(DruidPooledPreparedStatement.java:551)
	at sun.reflect.GeneratedMethodAccessor353.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:78)
	at com.sun.proxy.$Proxy154.executeBatch(Unknown Source)
	at org.apache.ibatis.executor.BatchExecutor.doFlushStatements(BatchExecutor.java:123)
	... 21 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException: Lock wait timeout exceeded; try restarting transaction
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:123)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795)
	... 35 common frames omitted
2025-08-30 10:17:07.657 [async-task-pool182] ERROR c.r.f.a.s.i.ApiResultSeviceImpl - [dealFlowDetailResult,215] - 非凡流量入库出错: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
2025-08-30 10:17:10.220 [async-task-pool37] ERROR c.r.f.a.s.i.ApiResultSeviceImpl - [dealFlowDetailResult,215] - 非凡流量入库出错: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
### Cause: org.apache.ibatis.executor.BatchExecutorException: com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper.insertFfsafeFlowDetail (batch index #1) failed. Cause: java.sql.BatchUpdateException: The table 'ffsafe_flow_detail' is full
2025-08-30 10:17:13.981 [async-task-pool176] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=63, attackId=127, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-30 10:17:13.981 [async-task-pool170] ERROR c.r.m.c.c.FfsafeClientService - [processHostIntrusionAttackBatch,1173] - 批量处理主机入侵攻击数据失败
java.lang.IllegalStateException: Duplicate key FfsafeHostIntrusionAttackDetail(id=64, attackId=128, detailType=vuln_scan, detailData={"alert_info": "1分钟内，攻击IP ************** 探测了 *********** 以下端口: 3389,8888,111,80,3306,8080,21,22,23,11211,389,1900,1433,81,2121,88,3128,873,8009,5432,8081,7001..."}, createTime=Fri Aug 29 17:52:09 CST 2025)
	at java.util.stream.Collectors.lambda$throwingMerger$0(Collectors.java:133)
	at java.util.HashMap.merge(HashMap.java:1255)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackDetails(FfsafeClientService.java:1295)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.processHostIntrusionAttackBatch(FfsafeClientService.java:1163)
	at com.ruoyi.monitor2.changting.client.FfsafeClientService.pullHostIntrusionAttacks(FfsafeClientService.java:1068)
	at com.ruoyi.ffsafe.api.event.PullHostIntrusionAttackEvent$1.lambda$null$0(PullHostIntrusionAttackEvent.java:96)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
