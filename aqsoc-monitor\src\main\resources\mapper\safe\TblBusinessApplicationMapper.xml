<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblBusinessApplicationMapper">

    <resultMap type="com.ruoyi.safe.domain.TblBusinessApplication" id="TblBusinessApplicationResult">
        <result property="assetId" column="asset_id"/>
        <result property="assetCode" column="asset_code"/>
        <result property="assetName" column="asset_name"/>
        <result property="softwareVersion" column="software_version"/>
        <result property="degreeImportance" column="degree_importance"/>
        <result property="manager" column="manager"/>
        <result property="domainUrl" column="domain_url"/>
        <result property="systemType" column="system_type"/>
        <result property="assetType" column="asset_type"/>
        <result property="assetTypeDesc" column="asset_type_desc"/>
        <result property="assetClass" column="asset_class"/>
        <result property="assetClassDesc" column="asset_class_desc"/>
        <result property="construct" column="construct"/>
        <result property="netType" column="net_type"/>
        <result property="appType" column="app_type"/>
        <result property="serviceGroup" column="service_group"/>
        <result property="frequency" column="frequency"/>
        <result property="usageCount" column="usage_count"/>
        <result property="userScale" column="user_scale"/>
        <result property="userObject" column="user_object"/>
        <result property="url" column="url"/>
        <result property="loginType" column="login_type"/>
        <result property="ipd" column="ipd"/>
        <result property="technical" column="technical"/>
        <result property="deploy" column="deploy"/>
        <result property="storage" column="storage"/>
        <result property="netenv" column="netenv"/>
        <result property="iskey" column="iskey"/>
        <result property="datanum" column="datanum"/>
        <result property="isbase" column="isbase"/>
        <result property="islink" column="islink"/>
        <result property="ishare" column="ishare"/>
        <result property="islog" column="islog"/>
        <result property="isplan" column="isplan"/>
        <result property="isadapt" column="isadapt"/>
        <result property="iscipher" column="iscipher"/>
        <result property="adaptDate" column="adapt_date"/>
        <result property="cipherDate" column="cipher_date"/>
        <result property="function" column="function"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="orgnId" column="orgn_id"/>
        <result property="vendor" column="vendor"/>
        <result property="userName" column="user_name"/>
        <result property="deptName" column="dept_name"/>
        <result property="vendorName" column="vendor_name"/>
        <result property="vendors" column="vendors"/>
        <result property="upTime" column="up_time"/>
        <result property="dwid" column="dwid"/>
        <result property="contactor" column="contactor"/>
        <result property="domainId" column="domain_id"/>
        <result property="netScale" column="net_scale"/>
        <result property="netTopo" column="net_topo"/>
        <result property="netMemo" column="net_memo"/>
        <result property="phone" column="phone"/>
        <result property="protectGrade" column="protect_grade"/>
        <result property="sysBusinessState" column="sys_business_state"/>
        <result property="coverArea" column="cover_area"/>
        <result property="userNums" column="user_nums"/>
        <result property="everydayVisitNums" column="everyday_visit_nums"/>
        <result property="id" column="id"/>
        <result property="checkOn" column="check_on"/>
        <result property="checkBy" column="check_by"/>
        <result property="checkTime" column="check_time"/>
        <result property="operateHandbook"    column="operate_handbook"    />
        <result property="everydayActiveNums"    column="everyday_active_nums"    />
        <result property="managerName" column="manager_name"/>
        <result property="managerPhone" column="manager_phone" />
        <result property="waitingInsuranceFilingNumber" column="waiting_insurance_filing_number" />
        <result property="waitingInsuranceFilingTime" column="waiting_insurance_filing_time" />
        <result property="waitingInsuranceFilingScan" column="waiting_insurance_filing_scan" />
        <result property="productName" column="product_name" />
        <result property="versionNumber" column="version_number" />
        <result property="evaluationYear" column="evaluation_year" />
        <result property="evaluationAgency" column="evaluation_agency" />
        <result property="evaluationNumber" column="evaluation_number" />
        <result property="evaluationResults" column="evaluation_results" />
        <result property="evaluationScore" column="evaluation_score" />
        <result property="evaluationReport" column="evaluation_report" />
        <result property="evaluationStatus" column="evaluation_status" />
        <result property="assetIp" column="asset_ip" />
        <result property="otherSystemNotes" column="other_system_notes" />
        <result property="isOpenNetwork" column="is_open_network" />
        <result property="accessibleNetwork" column="accessible_network" />
        <result property="developmentLanguage" column="development_language" />
        <result property="hwIsTrueShutDown" column="hw_is_true_shut_down" />
        <result property="protectionDescription" column="protection_description" />
        <result property="serverIpStr" column="server_ip_str" />
    </resultMap>
    <resultMap id="AppFunctionRelMap" type="com.ruoyi.safe.vo.AppFunctionRel">
        <result property="id" column="id"/>
        <result property="functionId" column="function_id"/>
        <result property="assetId" column="asset_id"/>
        <result property="serverId" column="server_id"/>
    </resultMap>
    <resultMap id="FunctionApplicationMap" type="com.ruoyi.safe.domain.FunctionState">
        <result property="functionId" column="function_id"/>
        <result property="editFunctionState" column="edit_function_state"/>
        <result property="assetId" column="asset_id"/>
        <result property="editFunctionName" column="edit_function_name"/>
    </resultMap>

    <sql id="selectTblBusinessApplicationVo">
        SELECT
            a.asset_id,
            a.asset_code,
            a.asset_name,
            a.software_version,
            a.degree_importance,
            a.manager,
            a.asset_type,
            a.asset_type_desc,
            a.domain_url,
            a.system_type,
            a.asset_class,
            a.asset_class_desc,
            a.construct,
            a.net_type,
            a.app_type,
            a.service_group,
            a.frequency,
            a.usage_count,
            a.user_scale,
            a.user_object,
            a.url,
            a.login_type,
            a.ipd,
            a.technical,
            a.deploy,
            a.STORAGE,
            a.netenv,
            a.iskey,
            a.datanum,
            a.isbase,
            a.islink,
            a.ishare,
            a.islog,
            a.isplan,
            a.isadapt,
            a.iscipher,
            a.adapt_date,
            a.cipher_date,
            a.FUNCTION,
            a.remark,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time,
            a.user_id,
            a.dept_id,
            a.orgn_id,
            a.vendor,
            a.up_time,
            a.dwid,
            a.contactor,
            a.domain_id,
            a.net_scale,
            a.net_topo,
            a.net_memo,
            a.phone,
            a.protect_grade,
            a.sys_business_state,
            a.cover_area,
            a.user_nums,
            a.everyday_visit_nums,
            a.edit_function_state,
            e.state,
            b.user_name,
            c.dept_name,
            m.nick_name AS manager_name,
            m.phonenumber AS manager_phone,
            d.vendor_name,
            a.operate_handbook,
            a.everyday_active_nums,
            a.check_on,
            a.check_by,
            a.check_time,
            a.vendors,
            a.waiting_insurance_filing_number,
            a.waiting_insurance_filing_time,
            a.waiting_insurance_filing_scan,
            a.product_name,
            a.version_number,
            a.evaluation_year,
            a.evaluation_agency,
            a.evaluation_number,
            a.evaluation_results,
            a.evaluation_score,
            a.evaluation_report,
            a.evaluation_status,
            a.other_system_notes,
            a.is_open_network,
            a.accessible_network,
            a.development_language,
            a.hw_is_true_shut_down,
            a.protection_description,
            GROUP_CONCAT( tas.server_id SEPARATOR ',' ) AS server_ip_str
        FROM
            tbl_business_application a
                LEFT JOIN sys_user m ON a.manager = m.user_id
                LEFT JOIN sys_user b ON a.user_id = b.user_id
                LEFT JOIN sys_dept c ON a.dept_id = c.dept_id
                LEFT JOIN tbl_vendor d ON a.vendor = d.id
                LEFT JOIN tbl_asset_overview e ON a.asset_id = e.asset_id
                LEFT JOIN ( SELECT server_id, asset_id FROM tbl_application_server WHERE type = '0' ) tas ON tas.asset_id = a.asset_id
    </sql>

    <select id="selectTblBusinessApplicationList" parameterType="TblBusinessApplication"
            resultMap="TblBusinessApplicationResult">
        <include refid="selectTblBusinessApplicationVo"/>
        <where>
            <if test="applicationIds != null and applicationIds.size() > 0">
                and a.asset_id in
                <foreach collection="applicationIds" item="queryAssetId" open="(" close=")" separator=",">
                    #{queryAssetId}
                </foreach>
            </if>
            <if test="domainId != '' and domainId != null">and a.domain_id = #{domainId}</if>
            <if test="isOpenNetwork != '' and isOpenNetwork != null">and a.is_open_network = #{isOpenNetwork}</if>
            <if test="hwIsTrueShutDown != '' and hwIsTrueShutDown != null">and a.hw_is_true_shut_down = #{hwIsTrueShutDown}</if>
            <if test="domainIdsList != null and domainIdsList.size() > 0">a.domain_id in
                <foreach collection="domainIdsList" item="domainId" open="(" separator="," close=")">
                    #{domainId}
                </foreach>
            </if>
            <if test="assetId != null and assetId != ''">and a.asset_id = #{assetId}</if>
            <if test="ids != null and ids.size() > 0">
                and a.asset_id in
                <foreach collection="ids" item="queryAssetId" open="(" close=")" separator=",">
                    #{queryAssetId}
                </foreach>
            </if>
            <if test="assetCode != null  and assetCode != ''">and a.asset_code = #{assetCode}</if>
            <if test="assetName != null  and assetName != ''">and a.asset_name like concat('%', #{assetName}, '%')</if>
            <if test="degreeImportance != null  and degreeImportance != ''">and a.degree_importance =
                #{degreeImportance}
            </if>
            <if test="assetType != null  and assetType != ''">and a.asset_type = #{assetType}</if>
            <if test="assetClass != null  and assetClass != ''">and a.asset_class = #{assetClass}</if>
            <if test="netType != null  and netType != ''">and a.net_type = #{netType}</if>
            <if test="frequency != null">and a.frequency = #{frequency}</if>
            <if test="serviceGroup != null">and a.service_group = #{serviceGroup}</if>
            <if test="deptId != null and deptId != 0">
                <!-- and (a.dept_id = #{deptId} or a.dept_id in ( select t.dept_id from sys_dept t where
                find_in_set(#{deptId}, ancestors) )) -->
                and (c.dept_id=#{deptId} OR find_in_set(#{deptId},c.ancestors))
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and
                <foreach collection="deptIds" item="deptId" open="(" separator=" OR " close=")">
                    c.dept_id=#{deptId} OR find_in_set(#{deptId},c.ancestors)
                </foreach>
            </if>
            <if test="tags != null">and find_in_set(#{tags} , e.tags)</if>
            <if test="checkOn != null">and a.check_on = #{checkOn}</if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
            <if test="url != null  and url != ''">and a.url like concat('%', #{url}, '%')</if>
            <if test="domainUrl != null  and domainUrl != ''">and a.domain_url like concat('%', #{domainUrl}, '%')</if>
            <if test="systemType != null ">and a.system_type = #{systemType}</if>
            <if test="state != null and state != ''">and e.state = #{state}</if>
            <if test="protectGrade != null and protectGrade != ''">and a.protect_grade = #{protectGrade}</if>
            <if test="manager != null and manager != ''">and FIND_IN_SET(#{manager} , a.manager)</if>
            <if test="managersList != null and managersList.size() > 0">
                <foreach collection="managersList" item="manager" open="and (" separator=" OR " close=")">
                    FIND_IN_SET(#{manager} , a.manager)
                </foreach>
            </if>
        </where>
        GROUP BY
        a.asset_id
    </select>

    <select id="getPageList" parameterType="TblBusinessApplication"
            resultMap="TblBusinessApplicationResult">
        select a.asset_id,a.asset_code,a.asset_name,a.software_version,a.degree_importance,a.manager,a.asset_type,m.phonenumber as manager_phone,
               a.asset_type_desc,a.asset_class,a.system_type,a.domain_url,a.asset_class_desc,a.construct,a.net_type,a.app_type,a.service_group,
               a.frequency,a.usage_count,a.user_scale,a.user_object,a.url,a.login_type,a.ipd,a.technical,a.deploy,
               a.storage,a.netenv,a.iskey,a.datanum,a.isbase,a.islink,a.ishare,a.islog,a.isplan,a.isadapt,a.iscipher,
               a.adapt_date,a.cipher_date,a.function,a.remark,a.create_by,a.create_time,a.update_by,a.update_time,
               a.user_id,a.dept_id,a.orgn_id,a.vendor,a.up_time,a.dwid,a.contactor,a.domain_id,a.net_scale,
               a.net_topo,a.net_memo,a.phone,a.protect_grade,a.sys_business_state,a.cover_area,a.user_nums,
               a.everyday_visit_nums,a.edit_function_state,a.vendors,
               e.state, b.user_name, c.dept_name,
               a.operate_handbook,a.everyday_active_nums,
               a.check_on,a.check_by,a.check_time,GROUP_CONCAT(DISTINCT d.vendor_name) as vendor_name,GROUP_CONCAT(DISTINCT m.nick_name) as manager_name,
               a.waiting_insurance_filing_number,a.waiting_insurance_filing_time,a.waiting_insurance_filing_scan,a.product_name,a.version_number,
               a.evaluation_year,a.evaluation_agency,a.evaluation_number,a.evaluation_results,a.evaluation_score,a.evaluation_report,a.evaluation_status,
                a.is_open_network,a.accessible_network,a.development_language,a.hw_is_true_shut_down,a.protection_description
        from tbl_business_application a
                 left join sys_user b on a.user_id = b.user_id
                 left join sys_dept c on a.dept_id = c.dept_id
                 LEFT join tbl_vendor d on FIND_IN_SET(d.id, a.vendors)
                 left join sys_user m on FIND_IN_SET(m.user_id, a.manager)
                 left join tbl_asset_overview e on a.asset_id = e.asset_id
        <where>
            <if test="assetId != null and assetId != ''">and a.asset_id = #{assetId}</if>
            <if test="assetCode != null  and assetCode != ''">and a.asset_code = #{assetCode}</if>
            <if test="assetName != null  and assetName != ''">and a.asset_name like concat('%', #{assetName}, '%')</if>
            <if test="degreeImportance != null  and degreeImportance != ''">and a.degree_importance =
                #{degreeImportance}
            </if>
            <if test="assetType != null  and assetType != ''">and a.asset_type = #{assetType}</if>
            <if test="assetClass != null  and assetClass != ''">and a.asset_class = #{assetClass}</if>
            <if test="netType != null  and netType != ''">and a.net_type = #{netType}</if>
            <if test="frequency != null">and a.frequency = #{frequency}</if>
            <if test="serviceGroup != null">and a.service_group = #{serviceGroup}</if>
            <if test="deptId != null and deptId != 0">
                and (a.dept_id = #{deptId} or a.dept_id in ( select t.dept_id from sys_dept t where
                find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="tags != null">and find_in_set(#{tags} , e.tags)</if>
            <if test="checkOn != null">and a.check_on = #{checkOn}</if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        </where>
        GROUP BY a.asset_id
        ORDER BY create_time DESC
    </select>

    <select id="selectTblBusinessApplicationByAssetId" parameterType="java.lang.Long"
            resultMap="TblBusinessApplicationResult">
        <include refid="selectTblBusinessApplicationVo"/>
        where a.asset_id = #{assetId}
        GROUP BY
        a.asset_id
    </select>

    <select id="selectBusinessApplicationByDomainId" parameterType="Long" resultMap="TblBusinessApplicationResult">
        select a.asset_id,b.id,
        a.asset_code,
        a.asset_name,
        a.`function`,
        a.software_version,
        a.vendor,
        url,
        up_time,
        degree_importance,
        asset_type,
        asset_type_desc,
        asset_class,
        asset_class_desc,
        a.frequency,
        a.service_group,
        a.net_type,
        a.remark,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.user_id,
        a.dept_id,
        a.orgn_id,
        a.domain_id,a.check_on,a.check_by,a.check_time,a.is_open_network,a.accessible_network,a.development_language,a.hw_is_true_shut_down,a.protection_description
        from tbl_business_application a
        left join tbl_domain_asset b on a.asset_id = b.asset_id
        where b.did = #{domainId}
    </select>

    <select id="selectTblBusinessApplicationByAssetIds" parameterType="Long" resultMap="TblBusinessApplicationResult">
        <include refid="selectTblBusinessApplicationVo"/>
        where a.asset_id in
        <foreach item="assetId" collection="array" open="(" close=")" separator=",">
            #{assetId}
        </foreach>
        GROUP BY
        a.asset_id
    </select>

    <insert id="insertTblBusinessApplication" parameterType="TblBusinessApplication">
        insert into tbl_business_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="assetCode != null">asset_code,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="softwareVersion != null">software_version,</if>
            <if test="degreeImportance != null">degree_importance,</if>
            <if test="manager != null">manager,</if>
            <if test="domainUrl != null">domain_url,</if>
            <if test="systemType != null">system_type,</if>
            <if test="assetType != null">asset_type,</if>
            <if test="assetTypeDesc != null">asset_type_desc,</if>
            <if test="assetClass != null">asset_class,</if>
            <if test="assetClassDesc != null">asset_class_desc,</if>
            <if test="construct != null">construct,</if>
            <if test="netType != null">net_type,</if>
            <if test="appType != null">app_type,</if>
            <if test="serviceGroup != null">service_group,</if>
            <if test="frequency != null">frequency,</if>
            <if test="usageCount != null">usage_count,</if>
            <if test="userScale != null">user_scale,</if>
            <if test="userObject != null">user_object,</if>
            <if test="url != null">url,</if>
            <if test="loginType != null">login_type,</if>
            <if test="ipd != null">ipd,</if>
            <if test="technical != null">technical,</if>
            <if test="deploy != null">deploy,</if>
            <if test="storage != null">storage,</if>
            <if test="netenv != null">netenv,</if>
            <if test="iskey != null">iskey,</if>
            <if test="datanum != null">datanum,</if>
            <if test="isbase != null">isbase,</if>
            <if test="islink != null">islink,</if>
            <if test="ishare != null">ishare,</if>
            <if test="islog != null">islog,</if>
            <if test="isplan != null">isplan,</if>
            <if test="isadapt != null">isadapt,</if>
            <if test="iscipher != null">iscipher,</if>
            <if test="adaptDate != null">adapt_date,</if>
            <if test="cipherDate != null">cipher_date,</if>
            <if test="function != null">`function`,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="orgnId != null">orgn_id,</if>
            <if test="vendor != null">vendor,</if>
            <if test="vendors != null">vendors,</if>
            <if test="upTime != null">up_time,</if>
            <if test="dwid != null">dwid,</if>
            <if test="contactor != null">contactor,</if>
            <if test="domainId != null">domain_id,</if>
            <if test="netScale != null">net_scale,</if>
            <if test="netTopo != null">net_topo,</if>
            <if test="netMemo != null">net_memo,</if>
            <if test="phone != null">phone,</if>
            <if test="protectGrade != null">protect_grade,</if>
            <if test="sysBusinessState != null">sys_business_state,</if>
            <if test="coverArea != null">cover_area,</if>
            <if test="userNums != null">user_nums,</if>
            <if test="everydayVisitNums != null">everyday_visit_nums,</if>
            <if test="checkOn != null">check_on,</if>
            <if test="checkBy != null">check_by,</if>
            <if test="checkTime != null">check_time,</if>
            <if test="operateHandbook != null">operate_handbook,</if>
            <if test="everydayActiveNums != null">everyday_active_nums,</if>
            <if test="waitingInsuranceFilingNumber != null">waiting_insurance_filing_number,</if>
            <if test="waitingInsuranceFilingTime != null">waiting_insurance_filing_time,</if>
            <if test="waitingInsuranceFilingScan != null">waiting_insurance_filing_scan,</if>
            <if test="productName != null">product_name,</if>
            <if test="versionNumber != null">version_number,</if>
            <if test="evaluationYear != null">evaluation_year,</if>
            <if test="evaluationAgency != null">evaluation_agency,</if>
            <if test="evaluationNumber != null">evaluation_number,</if>
            <if test="evaluationResults != null">evaluation_results,</if>
            <if test="evaluationScore != null">evaluation_score,</if>
            <if test="evaluationReport != null">evaluation_report,</if>
            <if test="evaluationStatus != null">evaluation_status,</if>
            <if test="otherSystemNotes != null">other_system_notes,</if>
            <if test="isOpenNetwork != null">is_open_network,</if>
            <if test="accessibleNetwork != null">accessible_network,</if>
            <if test="developmentLanguage != null">development_language,</if>
            <if test="hwIsTrueShutDown != null">hw_is_true_shut_down,</if>
            <if test="protectionDescription != null">protection_description,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="assetCode != null">#{assetCode},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="softwareVersion != null">#{softwareVersion},</if>
            <if test="degreeImportance != null">#{degreeImportance},</if>
            <if test="manager != null">#{manager},</if>
            <if test="domainUrl != null">#{domainUrl},</if>
            <if test="systemType != null">#{systemType},</if>
            <if test="assetType != null">#{assetType},</if>
            <if test="assetTypeDesc != null">#{assetTypeDesc},</if>
            <if test="assetClass != null">#{assetClass},</if>
            <if test="assetClassDesc != null">#{assetClassDesc},</if>
            <if test="construct != null">#{construct},</if>
            <if test="netType != null">#{netType},</if>
            <if test="appType != null">#{appType},</if>
            <if test="serviceGroup != null">#{serviceGroup},</if>
            <if test="frequency != null">#{frequency},</if>
            <if test="usageCount != null">#{usageCount},</if>
            <if test="userScale != null">#{userScale},</if>
            <if test="userObject != null">#{userObject},</if>
            <if test="url != null">#{url},</if>
            <if test="loginType != null">#{loginType},</if>
            <if test="ipd != null">#{ipd},</if>
            <if test="technical != null">#{technical},</if>
            <if test="deploy != null">#{deploy},</if>
            <if test="storage != null">#{storage},</if>
            <if test="netenv != null">#{netenv},</if>
            <if test="iskey != null">#{iskey},</if>
            <if test="datanum != null">#{datanum},</if>
            <if test="isbase != null">#{isbase},</if>
            <if test="islink != null">#{islink},</if>
            <if test="ishare != null">#{ishare},</if>
            <if test="islog != null">#{islog},</if>
            <if test="isplan != null">#{isplan},</if>
            <if test="isadapt != null">#{isadapt},</if>
            <if test="iscipher != null">#{iscipher},</if>
            <if test="adaptDate != null">#{adaptDate},</if>
            <if test="cipherDate != null">#{cipherDate},</if>
            <if test="function != null">#{function},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="orgnId != null">#{orgnId},</if>
            <if test="vendor != null">#{vendor},</if>
            <if test="vendors != null">#{vendors},</if>
            <if test="upTime != null">#{upTime},</if>
            <if test="dwid != null">#{dwid},</if>
            <if test="contactor != null">#{contactor},</if>
            <if test="domainId != null">#{domainId},</if>
            <if test="netScale != null">#{netScale},</if>
            <if test="netTopo != null">#{netTopo},</if>
            <if test="netMemo != null">#{netMemo},</if>
            <if test="phone != null">#{phone},</if>
            <if test="protectGrade != null">#{protectGrade},</if>
            <if test="sysBusinessState != null">#{sysBusinessState},</if>
            <if test="coverArea != null">#{coverArea},</if>
            <if test="userNums != null">#{userNums},</if>
            <if test="everydayVisitNums != null">#{everydayVisitNums},</if>
            <if test="checkOn != null">#{checkOn},</if>
            <if test="checkBy != null">#{checkBy},</if>
            <if test="checkTime != null">#{checkTime},</if>
            <if test="operateHandbook != null">#{operateHandbook},</if>
            <if test="everydayActiveNums != null">#{everydayActiveNums},</if>
            <if test="waitingInsuranceFilingNumber != null">#{waitingInsuranceFilingNumber},</if>
            <if test="waitingInsuranceFilingTime != null">#{waitingInsuranceFilingTime},</if>
            <if test="waitingInsuranceFilingScan != null">#{waitingInsuranceFilingScan},</if>
            <if test="productName != null">#{productName},</if>
            <if test="versionNumber != null">#{versionNumber},</if>
            <if test="evaluationYear != null">#{evaluationYear},</if>
            <if test="evaluationAgency != null">#{evaluationAgency},</if>
            <if test="evaluationNumber != null">#{evaluationNumber},</if>
            <if test="evaluationResults != null">#{evaluationResults},</if>
            <if test="evaluationScore != null">#{evaluationScore},</if>
            <if test="evaluationReport != null">#{evaluationReport},</if>
            <if test="evaluationStatus != null">#{evaluationStatus},</if>
            <if test="otherSystemNotes != null">#{otherSystemNotes},</if>
            <if test="isOpenNetwork != null">#{isOpenNetwork},</if>
            <if test="accessibleNetwork != null">#{accessibleNetwork},</if>
            <if test="developmentLanguage != null">#{developmentLanguage},</if>
            <if test="hwIsTrueShutDown != null">#{hwIsTrueShutDown},</if>
            <if test="protectionDescription != null">#{protectionDescription},</if>
        </trim>
    </insert>

    <update id="updateTblBusinessApplication" parameterType="TblBusinessApplication">
        update tbl_business_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetCode != null">asset_code = #{assetCode},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="softwareVersion != null">software_version = #{softwareVersion},</if>
            <if test="degreeImportance != null">degree_importance = #{degreeImportance},</if>
            <if test="manager != null">manager = #{manager},</if>
            <if test="domainUrl != null">domain_url = #{domainUrl},</if>
            <if test="systemType != null || systemType == null">system_type = #{systemType},</if>
            <if test="assetType != null">asset_type = #{assetType},</if>
            <if test="assetTypeDesc != null">asset_type_desc = #{assetTypeDesc},</if>
            <if test="assetClass != null">asset_class = #{assetClass},</if>
            <if test="assetClassDesc != null">asset_class_desc = #{assetClassDesc},</if>
            <if test="construct != null">construct = #{construct},</if>
            <if test="netType != null">net_type = #{netType},</if>
            <if test="appType != null">app_type = #{appType},</if>
            <if test="serviceGroup != null">service_group = #{serviceGroup},</if>
            <if test="frequency != null">frequency = #{frequency},</if>
            <if test="usageCount != null">usage_count = #{usageCount},</if>
            <if test="userScale != null">user_scale = #{userScale},</if>
            <if test="userObject != null">user_object = #{userObject},</if>
            <if test="url != null">url = #{url},</if>
            <if test="loginType != null">login_type = #{loginType},</if>
            <if test="ipd != null">ipd = #{ipd},</if>
            <if test="technical != null">technical = #{technical},</if>
            <if test="deploy != null">deploy = #{deploy},</if>
            <if test="storage != null">storage = #{storage},</if>
            <if test="netenv != null">netenv = #{netenv},</if>
            <if test="iskey != null">iskey = #{iskey},</if>
            <if test="datanum != null">datanum = #{datanum},</if>
            <if test="isbase != null">isbase = #{isbase},</if>
            <if test="islink != null">islink = #{islink},</if>
            <if test="ishare != null">ishare = #{ishare},</if>
            <if test="islog != null">islog = #{islog},</if>
            <if test="isplan != null">isplan = #{isplan},</if>
            <if test="isadapt != null">isadapt = #{isadapt},</if>
            <if test="iscipher != null">iscipher = #{iscipher},</if>
            <if test="1==1">adapt_date = #{adaptDate},</if>
            <if test="1==1">cipher_date = #{cipherDate},</if>
            <if test="function != null">`function` = #{function},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="orgnId != null">orgn_id = #{orgnId},</if>
            <if test="vendor != null">vendor = #{vendor},</if>
            <if test="vendors != null">vendors = #{vendors},</if>
            <if test="upTime != null">up_time = #{upTime},</if>
            <if test="dwid != null">dwid = #{dwid},</if>
            <if test="contactor != null">contactor = #{contactor},</if>
            <if test="1==1">domain_id = #{domainId},</if>
            <if test="netScale != null">net_scale = #{netScale},</if>
            <if test="netTopo != null">net_topo = #{netTopo},</if>
            <if test="netMemo != null">net_memo = #{netMemo},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="protectGrade != null">protect_grade = #{protectGrade},</if>
            <if test="sysBusinessState != null">sys_business_state = #{sysBusinessState},</if>
            <if test="coverArea != null">cover_area = #{coverArea},</if>
            <if test="checkOn != null">check_on = #{checkOn},</if>
            <if test="checkBy != null">check_by = #{checkBy},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
            <if test="operateHandbook != null">operate_handbook = #{operateHandbook},</if>
            <if test="waitingInsuranceFilingNumber != null">waiting_insurance_filing_number = #{waitingInsuranceFilingNumber},</if>
            <if test="waitingInsuranceFilingTime != null">waiting_insurance_filing_time = #{waitingInsuranceFilingTime},</if>
            <if test="waitingInsuranceFilingScan != null">waiting_insurance_filing_scan = #{waitingInsuranceFilingScan},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="versionNumber != null">version_number = #{versionNumber},</if>
            <if test="evaluationYear != null">evaluation_year = #{evaluationYear},</if>
            <if test="evaluationAgency != null">evaluation_agency = #{evaluationAgency},</if>
            <if test="evaluationNumber != null">evaluation_number = #{evaluationNumber},</if>
            <if test="evaluationResults != null">evaluation_results = #{evaluationResults},</if>
            <if test="evaluationScore != null">evaluation_score = #{evaluationScore},</if>
            <if test="evaluationReport != null">evaluation_report = #{evaluationReport},</if>
            <if test="evaluationStatus != null">evaluation_status = #{evaluationStatus},</if>
            <if test="otherSystemNotes != null || otherSystemNotes == null">other_system_notes = #{otherSystemNotes},</if>
            <if test="isOpenNetwork != null || isOpenNetwork == null">is_open_network = #{isOpenNetwork},</if>
            <if test="accessibleNetwork != null || accessibleNetwork == null">accessible_network = #{accessibleNetwork},</if>
            <if test="developmentLanguage != null || developmentLanguage == null">development_language = #{developmentLanguage},</if>
            <if test="hwIsTrueShutDown != null || hwIsTrueShutDown == null">hw_is_true_shut_down = #{hwIsTrueShutDown},</if>
            <if test="protectionDescription != null || protectionDescription == null">protection_description = #{protectionDescription},</if>
        </trim>
        where asset_id = #{assetId}
    </update>



    <update id="updateTblBusinessApplicationStatus" parameterType="TblBusinessApplication">
        update tbl_business_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="checkOn != null">check_on = #{checkOn},</if>
            <if test="checkBy != null">check_by = #{checkBy},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where asset_id = #{assetId}
    </update>

    <update id="updateTblBusinessApplicationInfo" parameterType="TblBusinessApplication">
        update tbl_business_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetCode != null">asset_code = #{assetCode},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="softwareVersion != null">software_version = #{softwareVersion},</if>
            <if test="degreeImportance != null">degree_importance = #{degreeImportance},</if>
            <if test="manager != null">manager = #{manager},</if>
            <if test="domainUrl != null">domain_url = #{domainUrl},</if>
            <if test="systemType != null">system_type = #{systemType},</if>
            <if test="assetType != null">asset_type = #{assetType},</if>
            <if test="assetTypeDesc != null">asset_type_desc = #{assetTypeDesc},</if>
            <if test="assetClass != null">asset_class = #{assetClass},</if>
            <if test="assetClassDesc != null">asset_class_desc = #{assetClassDesc},</if>
            <if test="construct != null">construct = #{construct},</if>
            <if test="netType != null">net_type = #{netType},</if>
            <if test="appType != null">app_type = #{appType},</if>
            <if test="serviceGroup != null">service_group = #{serviceGroup},</if>
            <if test="frequency != null">frequency = #{frequency},</if>
            <if test="usageCount != null">usage_count = #{usageCount},</if>
            <if test="userScale != null">user_scale = #{userScale},</if>
            <if test="userObject != null">user_object = #{userObject},</if>
            <if test="url != null">url = #{url},</if>
            <if test="loginType != null">login_type = #{loginType},</if>
            <if test="ipd != null">ipd = #{ipd},</if>
            <if test="technical != null">technical = #{technical},</if>
            <if test="deploy != null">deploy = #{deploy},</if>
            <if test="storage != null">storage = #{storage},</if>
            <if test="netenv != null">netenv = #{netenv},</if>
            <if test="iskey != null">iskey = #{iskey},</if>
            <if test="datanum != null">datanum = #{datanum},</if>
            <if test="isbase != null">isbase = #{isbase},</if>
            <if test="islink != null">islink = #{islink},</if>
            <if test="ishare != null">ishare = #{ishare},</if>
            <if test="islog != null">islog = #{islog},</if>
            <if test="isplan != null">isplan = #{isplan},</if>
            <if test="isadapt != null">isadapt = #{isadapt},</if>
            <if test="iscipher != null">iscipher = #{iscipher},</if>
            <if test="function != null">`function` = #{function},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="orgnId != null">orgn_id = #{orgnId},</if>
            <if test="vendor != null">vendor = #{vendor},</if>
            <if test="upTime != null">up_time = #{upTime},</if>
            <if test="dwid != null">dwid = #{dwid},</if>
            <if test="contactor != null">contactor = #{contactor},</if>
            <if test="netScale != null">net_scale = #{netScale},</if>
            <if test="netTopo != null">net_topo = #{netTopo},</if>
            <if test="netMemo != null">net_memo = #{netMemo},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="protectGrade != null">protect_grade = #{protectGrade},</if>
            <if test="sysBusinessState != null">sys_business_state = #{sysBusinessState},</if>
            <if test="coverArea != null">cover_area = #{coverArea},</if>
            <if test="1==1">user_nums = #{userNums},</if>
            <if test="1==1">everyday_visit_nums = #{everydayVisitNums},</if>
            <if test="checkOn != null">check_on = #{checkOn},</if>
            <if test="checkBy != null">check_by = #{checkBy},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
            <if test="operateHandbook != null">operate_handbook = #{operateHandbook},</if>
            <if test="1==1">everyday_active_nums = #{everydayActiveNums},</if>
        </trim>
        where asset_id = #{assetId}
    </update>

    <delete id="deleteTblBusinessApplicationByAssetId" parameterType="Long">
        delete from tbl_business_application where asset_id = #{assetId}
    </delete>

    <delete id="deleteTblBusinessApplicationByAssetIds" parameterType="String">
        delete from tbl_business_application where asset_id in
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>

    <insert id="addBusinessApplicationDomainRel" parameterType="collection">
        insert into tbl_domain_asset (did,asset_id) values
        <foreach collection="list" item="item" open="(" separator="),(" close=")">
            #{item.did},#{item.assetId}
        </foreach>
    </insert>

    <delete id="deleteBusinessApplicationDomainRel" parameterType="TblDomainAsset">
        delete from tbl_domain_asset
        <where>
            <if test="id!=null and id!=''">and id = #{id}</if>
            <if test="did!=null and did!=''">and did = #{did}</if>
            <if test="assetId!=null and assetId!=''">and asset_id = #{assetId}</if>
        </where>
    </delete>

    <insert id="addFunctionState" parameterType="FunctionState">
        insert into tbl_function_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="functionId != null">function_id,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="editFunctionState != null and editFunctionState != ''">edit_function_state,</if>
            <if test="editFunctionName != null">edit_function_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="functionId != null">#{functionId},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="editFunctionState != null and editFunctionState != ''">#{editFunctionState},</if>
            <if test="editFunctionName != null">#{editFunctionName},</if>
        </trim>
    </insert>

    <select id="seleAppFunRel" parameterType="com.ruoyi.safe.vo.AppFunctionRel" resultMap="AppFunctionRelMap">
        select * from tbl_application_function_rel
        <where>
            <if test="assetId">and asset_id = #{assetId}</if>
            <if test="serverId">and server_id = #{serverId}</if>
            <if test="functionId">and function_id = #{functionId}</if>
        </where>
    </select>
    <select id="selectFunctionByIds" parameterType="string" resultMap="FunctionApplicationMap">
        select * from tbl_function_application where function_id in
        <foreach collection="list" item="id" separator="," close=")" open="(">
            #{id}
        </foreach>
    </select>

    <delete id="delApplicationServerFunctionRel">
        delete from tbl_application_function_rel where id in
        <foreach collection="list" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </delete>
    <insert id="addApplicationServerFunctionRel">
        insert into tbl_application_function_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="functionId != null">function_id,</if>
            <if test="id != null">id,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="serverId != null">server_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="functionId != null">#{functionId},</if>
            <if test="id != null">#{id},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="serverId != null">#{serverId},</if>
        </trim>
    </insert>

    <select id="selectTblBusinessInfoByAssetId" resultType="com.ruoyi.safe.domain.TblBusinessApplication">
        select tbs.asset_id,
        tbs.service_group,tbs.sys_business_state,tbs.cover_area,tbs.user_nums,tbs.everyday_visit_nums
        from tbl_business_application tbs where tbs.asset_id=#{assetId}
    </select>

    <select id="selectTblBusinessStateByAssetId" resultType="com.ruoyi.safe.domain.FunctionState">
        select tfa.asset_id, tfa.function_id, tfa.edit_function_name, tfa.edit_function_state
        from tbl_function_application tfa
        where tfa.asset_id = #{assetId}
    </select>

    <select id="getApplicationNameList" parameterType="TblBusinessApplication" resultMap="TblBusinessApplicationResult">
        select asset_id,asset_name
        from tbl_business_application
        <where>
            <if test="deptId != null and deptId != 0">
                and (dept_id = #{deptId} or dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
        </where>
    </select>

    <select id="getApplicationList" parameterType="TblBusinessApplication" resultMap="TblBusinessApplicationResult">
        select asset_id,asset_name,url
        from tbl_business_application
        <where>
            <if test="deptId != null and deptId != 0">
                and (dept_id = #{deptId} or dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="assetId != null and assetId != ''">and asset_id = #{assetId}</if>
            <if test="assetName != null  and assetName != ''">and asset_name like concat('%', #{assetName}, '%')</if>
        </where>
    </select>

    <select id="getApplicationListByCondition" resultMap="TblBusinessApplicationResult">
        SELECT
            t1.asset_id,t1.asset_name,t1.url
        FROM
            tbl_business_application t1
            LEFT JOIN ( SELECT asset_id, server_id, `port` FROM tbl_application_server GROUP BY server_id, asset_id ) t2 ON t2.asset_id = t1.asset_id
            left join tbl_server t3 ON t3.asset_id = t2.server_id
            LEFT JOIN tbl_network_ip_mac t4 ON t4.asset_id = t3.asset_id
        <where>
            <if test="deptId != null">
                AND (t3.dept_id = #{deptId} OR t3.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors ) ))
            </if>
            <if test="ipv4 != null and ipv4 != ''">
                <if test="eventType != null and eventType == 2">
                    and t1.url LIKE concat('%', #{ipv4}, '%')
                </if>
                <if test="eventType != null and eventType != 2">
                    and t4.ipv4 = #{ipv4}
                </if>
                <if test="eventType == null">
                    and t4.ipv4 = #{ipv4}
                </if>
            </if>
            <if test="applicationId != null and applicationId != ''">
                and t1.asset_id = #{applicationId}
            </if>
        </where>
        GROUP BY
            t1.asset_id
        ORDER BY
            t1.create_time DESC
    </select>

    <select id="selectDetailsByAssetId" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            t1.asset_id,t1.asset_name,t1.manager,t1.url,t1.dept_id,t2.domain_id,t3.user_name,t3.phonenumber
        FROM
            tbl_business_application t1
            LEFT JOIN tbl_network_domain t2 ON t2.domain_id = t1.domain_id
            LEFT JOIN sys_user t3 ON t3.user_id = t1.manager
        WHERE
            t1.asset_id = #{assetId}
    </select>


    <resultMap id="DeptApplicationCountMap" type="com.ruoyi.safe.vo.DeptApplicationCount">
        <result property="deptId" column="deptId"/>
        <result property="ancestors" column="ancestors"/>
        <result property="applicationCount" column="applicationCount"/>
    </resultMap>

    <select id="getDeptApplicationCount" parameterType="QueryDeptApplicationCountDto" resultMap="DeptApplicationCountMap">
        SELECT
        sd.dept_id AS deptId,sd.ancestors,
        <choose>
            <when test="params.userId != null">
                COALESCE(SUM(CASE WHEN a.user_id = #{params.userId} THEN 1 ELSE 0 END), 0)
            </when>
            <otherwise>
                COALESCE(COUNT(a.asset_id), 0)
            </otherwise>
        </choose>
        AS applicationCount
        FROM sys_dept sd
        LEFT JOIN tbl_business_application a ON a.dept_id = sd.dept_id <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        left join sys_user m on a.manager = m.user_id
        left join sys_user b on a.user_id = b.user_id
        left join sys_dept c on a.dept_id = c.dept_id
        left join tbl_vendor d on a.vendor = d.id
        left join tbl_asset_overview e on a.asset_id = e.asset_id
        WHERE sd.dept_id IN
        <foreach collection="deptIdList" item="deptId" separator="," close=")" open="(">
            #{deptId}
        </foreach>
        GROUP BY sd.dept_id
    </select>
    <select id="selectMatchIpApplication" resultType="com.ruoyi.safe.domain.TblBusinessApplication" resultMap="TblBusinessApplicationResult">
        SELECT asset_id,asset_name,ipd FROM `tbl_business_application`
        WHERE ipd LIKE CONCAT('%',#{ip},'%') OR (ipd LIKE CONCAT('%',#{ipd},'%') and ipd LIKE CONCAT('%','-','%'))
    </select>
    <select id="selectListByServerId" resultType="com.ruoyi.safe.domain.TblBusinessApplication" resultMap="TblBusinessApplicationResult">
        SELECT
            t1.*
        FROM
            tbl_business_application t1
            LEFT JOIN tbl_application_server t2 ON t2.asset_id=t1.asset_id
        WHERE
            FIND_IN_SET(t1.asset_id,#{serverId}) OR FIND_IN_SET(t2.server_id,#{serverId})
        GROUP BY
            t1.asset_id
    </select>
    <select id="selectListByAssociationAssetId" resultType="com.ruoyi.safe.domain.TblBusinessApplication" resultMap="TblBusinessApplicationResult">
        SELECT
            t1.asset_id,t1.asset_name
        FROM
            tbl_business_application t1
            LEFT JOIN tbl_application_server t2 ON t2.asset_id=t1.asset_id
        WHERE
            t1.asset_id=#{assetId} OR t2.server_id=#{assetId}
        GROUP BY
            t1.asset_id
    </select>
    <select id="selectListByNetworkIpMacIds" resultType="com.ruoyi.safe.domain.TblBusinessApplication" resultMap="TblBusinessApplicationResult">
        SELECT
            DISTINCT t1.asset_id,
                     t1.asset_name,
                     nim.ipv4 asset_ip
        FROM
            tbl_network_ip_mac nim
            LEFT JOIN tbl_application_server t2 ON t2.server_id = nim.asset_id
            LEFT JOIN tbl_business_application t1 ON t1.asset_id = t2.asset_id OR t1.asset_id = nim.asset_id
        WHERE
            t1.asset_id IS NOT NULL and
            nim.id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
    </select>
    <select id="selectListByDeptIds" resultType="com.ruoyi.safe.domain.TblBusinessApplication">
        SELECT
            t1.*
        FROM
            tbl_business_application t1
        WHERE
            t1.dept_id in
        <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
            #{deptId}
        </foreach>
    </select>

    <select id="selectAssetIpByAssetId" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            t1.asset_id AS assetId,
            t2.server_id AS serverId,
            t3.ipv4
        FROM
            ( SELECT * FROM tbl_business_application WHERE asset_id = #{assetId} ) t1
                LEFT JOIN tbl_application_server t2 ON t1.asset_id = t2.asset_id
                LEFT JOIN tbl_network_ip_mac t3 ON t2.server_id = t3.asset_id
        WHERE
            t3.main_ip = 1 UNION ALL
        SELECT
            t1.asset_id AS assetId,
            t2.netware_id AS netwareId,
            t3.ipv4
        FROM
            ( SELECT * FROM tbl_business_application WHERE asset_id = #{assetId} ) t1
                LEFT JOIN tbl_application_netware t2 ON t1.asset_id = t2.asset_id
                LEFT JOIN tbl_network_ip_mac t3 ON t2.netware_id = t3.asset_id
        WHERE
            t3.main_ip = 1 UNION ALL
        SELECT
            t1.asset_id AS assetId,
            t2.safe_id AS netwareId,
            t3.ipv4
        FROM
            ( SELECT * FROM tbl_business_application WHERE asset_id = #{assetId} ) t1
                LEFT JOIN tbl_application_safe t2 ON t2.asset_id = t1.asset_id
                LEFT JOIN tbl_network_ip_mac t3 ON t3.asset_id = t2.safe_id
        WHERE
            t3.main_ip = 1
        GROUP BY t3.ipv4
    </select>

    <select id="selectAssetServerByAssetId" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            t1.asset_id assetId,t2.server_id serverId,t3.ipv4
        FROM
            tbl_business_application t1 LEFT JOIN tbl_application_server t2 on t1.asset_id = t2.asset_id
                                        LEFT JOIN tbl_network_ip_mac t3 ON t2.server_id = t3.asset_id
        WHERE
            t1.asset_id = #{assetId} AND t3.main_ip = 1
        GROUP BY t3.ipv4
    </select>
    <select id="getProtectGradeCountRatio" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT temp.`name`,SUM(temp.`value`) `value` FROM
            (SELECT
                 IFNULL(dict.dict_label,'未做等保') as `name`,COUNT(distinct t1.asset_id) as `value`
             FROM
                 tbl_business_application t1
                 LEFT JOIN sys_dict_data dict ON dict_type='protection_grade' AND dict.dict_value=t1.protect_grade
                 LEFT JOIN sys_dept sd on sd.dept_id=t1.dept_id
             <where>
                 <if test="deptId != null">
                     AND (sd.dept_id = #{deptId} OR find_in_set(#{deptId},sd.ancestors))
                 </if>
             </where>
             GROUP BY
                 t1.protect_grade) temp
        GROUP BY
            temp.`name`
    </select>

    <select id="countNum" resultType="java.lang.Integer">
        select count(1) from tbl_business_application
    </select>

    <select id="getByDomains" resultType="com.ruoyi.safe.vo.assetOverview.CountByDomainVO">
        SELECT
          domain_id domainId,COUNT(asset_id) `count`
        FROM
          tbl_business_application
        WHERE
          domain_id in
          <foreach collection="domainIds" item="domainId" open="(" close=")" separator=",">
              #{domainId}
          </foreach>
        GROUP BY
          domain_id
    </select>
    <select id="getLoginUrlById" resultType="java.lang.String">
        select url from tbl_business_application where asset_id=#{applicationId}
    </select>


    <select id="selectAssetServerByAssetIds" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            t1.asset_id assetId,t2.server_id serverId,t3.ipv4
        FROM
            tbl_business_application t1
            LEFT JOIN tbl_application_server t2 on t1.asset_id = t2.asset_id
            LEFT JOIN tbl_network_ip_mac t3 ON t2.server_id = t3.asset_id
        WHERE
        t1.asset_id in
        <foreach collection="assetIds" item="assetId" open="(" close=")" separator=",">
            #{assetId}
        </foreach>
           AND t3.main_ip = 1
        GROUP BY assetId
    </select>
    <select id="selectByAssetIds" resultType="com.ruoyi.safe.domain.TblBusinessApplication" resultMap="TblBusinessApplicationResult">
        SELECT
            t1.*
        FROM
            tbl_business_application t1
        WHERE
            t1.asset_id in
        <foreach collection="assetIds" item="assetId" open="(" close=")" separator=",">
            #{assetId}
        </foreach>
    </select>

    <select id="selectAssetFieldsItemList" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            t2.form_name formName,
            t1.ref_id refId,
            t1.field_name fieldName,
            t1.field_key fieldKey
        FROM
            tbl_asset_fields_item t1
                LEFT JOIN tbl_asset_fields t2 ON t2.id = t1.ref_id
        WHERE
            t2.asset_type = #{assetType} AND t1.is_show = 1
    </select>
</mapper>
