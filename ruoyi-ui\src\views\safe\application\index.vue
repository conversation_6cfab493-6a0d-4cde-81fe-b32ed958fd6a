<template>
  <div class="custom-container">
    <div class="custom-tree-container" ref="system">
      <dept-select-system
        :key="deptSelectKey"
        ref="deptSelect"
        :is-current="true"
        @deptSelect="deptSelect"
        asset-class="application"
        :current-dept-id="queryParams.deptId"
      />
    </div>
    <div class="custom-content-container-right">
      <div class="custom-content-search-box">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          @submit.native.prevent
        >
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="系统名称" prop="assetName">
                <el-input
                  v-model="queryParams.assetName"
                  placeholder="请输入系统名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="登录地址">
                <el-input v-model="queryParams.url" clearable placeholder="请输入登录地址"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="域名">
                <el-input v-model="queryParams.domainUrl" clearable placeholder="请输入域名"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="custom-search-btn">
                <el-button class="btn1" size="small" @click="handleQuery">查询</el-button>
                <el-button class="btn2" size="small" @click="resetQuery">重置</el-button>
                <el-button class="btn2" size="small" icon="el-icon-arrow-down" @click="showAll=true" v-if="!showAll">
                  展开
                </el-button>
                <el-button class="btn2" size="small" icon="el-icon-arrow-up" @click="showAll=false" v-else>收起
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10" v-if="showAll">
            <el-col :span="24" v-if="systemsType.length">
              <el-form-item label="系统类型">
                <SystemList
                  ref="systemList1"
                  :systemTypes="systemsType"
                  @filterSelect="handleQuery"
                  :systemTypeVal.sync="queryParams.systemType"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="assetState.length">
              <el-form-item label="上线状态">
                <SystemList
                  ref="systemList2"
                  :systemTypes="assetState"
                  @filterSelect="handleQuery"
                  :systemTypeVal.sync="queryParams.state"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="protectionGrade.length">
              <el-form-item label="等保等级">
                <SystemList
                  ref="systemList3"
                  paramVal="protectGrade"
                  @filterSelect="handleQuery"
                  :systemTypes="protectionGrade"
                  :systemTypeVal.sync="queryParams.protectGrade"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="appcheckState.length">
              <el-form-item label="审核状态">
                <SystemList
                  ref="systemList4"
                  @filterSelect="handleQuery"
                  :systemTypes="appcheckState"
                  :systemTypeVal.sync="queryParams.checkOn"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="appcheckState.length">
              <el-form-item label="是否开放网络">
                <SystemList
                  ref="systemList4"
                  @filterSelect="handleQuery"
                  :systemTypes="hwIsTrueShutDown"
                  :systemTypeVal.sync="queryParams.isOpenNetwork"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="appcheckState.length">
              <el-form-item label="HW时期是否可关停">
                <SystemList
                  ref="systemList4"
                  @filterSelect="handleQuery"
                  :systemTypes="hwIsTrueShutDown"
                  :systemTypeVal.sync="queryParams.hwIsTrueShutDown"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="custom-content-container">
        <div class="common-header">
          <div><span class="common-head-title">业务系统列表</span></div>
          <div class="common-head-right">
            <el-row :gutter="10">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleAdd"
                  v-hasPermi="['safe:application:add']"
                >新增</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  class="btn1"
                  size="small"
                  v-hasPermi="['safe:application:add']"
                  @click="handleImport"
                >导入</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  class="btn1"
                  size="small"
                  :disabled="multiple"
                  @click="handleDelete"
                  v-hasPermi="['safe:application:remove']"
                >批量删除</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  class="btn1"
                  size="small"
                  @click="handleExport"
                  v-hasPermi="['safe:application:export']"
                >导出</el-button
                >
              </el-col>
<!--              <right-toolbar-->
<!--                :showSearch.sync="showSearch"-->
<!--                :columns="columns"-->
<!--                @queryTable="getList"-->
<!--              ></right-toolbar>-->
            </el-row>
          </div>
        </div>
        <div class="tableContainer">
          <el-table ref="elTable"
                    v-loading="loading"
                    height="100%"
                    :data="applicationList"
                    :key="tableKey"
                    @selection-change="handleSelectionChange"
                    @sort-change="sortChange">
            <el-table-column
              type="selection"
              width="55">
            </el-table-column>
            <el-table-column
              label="系统名称"
              fixed="left"
              min-width="150"
              align="left"
              prop="assetName"
              v-if="columns[0].visible"
              :sortable="false"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ scope.row.assetName || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="信息完整度"
              min-width="150"
              prop="completeness"
            >
              <template slot-scope="scope">
                <div style="display: flex; align-items: center;">
                  <el-progress
                    :color="scope.row.completeness > 80 ? '#67c23a' : scope.row.completeness < 60 ? '#f56c6c' : '#e6a23c'"
                    :percentage="scope.row.completeness"
                    :show-text="false"
                    style="flex: 1;"
                  ></el-progress>
                  <span style="margin-left: 10px; width: 50px;">
                    {{ scope.row.completenessStr }}%
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="所属部门"
              min-width="150"
              prop="deptName"
              v-if="columns[2].visible"
              :sortable="false"
            >
              <template slot-scope="scope">
                {{ scope.row.deptName || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="责任人员"
              width="120"
              prop="managerName"
              v-if="columns[3].visible"
              :sortable="false"
            >
              <template slot-scope="scope">
                {{ scope.row.managerName || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="联系电话"
              width="150"
              prop="managerPhone"
              :sortable="false"
              v-if="columns[4].visible"
            >
              <template slot-scope="scope">
                {{ scope.row.managerPhone || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="关键设施"

              width="120"
              prop="iskey"
              v-if="columns[6].visible"
              :sortable="false"
            >
              <template slot-scope="scope">
                {{ scope.row.iskey || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="登录地址"
              min-width="120"
              prop="url"
              v-if="columns[1].visible"
              :sortable="false"
            >
              <template slot-scope="scope">
                {{ scope.row.url || "-" }}
<!--                <el-popover placement="left-end" width="200" trigger="hover">
                  <span>{{ scope.row.url || "-" }}</span>
                  <span class="r_popover" slot="reference">
                  {{ scope.row.url || "-" }}
                </span>
                </el-popover>-->
              </template>
            </el-table-column>
            <el-table-column
              label="关联服务器IP"
              width="140"
              prop="ip"
              v-if="columns[21].visible"
              :sortable="false"
            >
              <template slot-scope="scope">
                {{ scope.row.ip || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="等保级别"
              width="120"
              prop="protectGrade"
              v-if="columns[5].visible"
              :sortable="false"
            >
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.protection_grade"
                  :value="scope.row.protectGrade || '-'"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="技术架构"
              width="120"
              prop="technical"
              v-if="columns[7].visible"
              :sortable="false"
            >
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.construct_type"
                  :value="scope.row.construct || '-'"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="登录方式"
              width="100"
              prop="loginType"
              v-if="columns[8].visible"
              :sortable="false"
            >
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.app_login_type"
                  :value="scope.row.loginType || '-'"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="部署方式"
              width="100"
              prop="deploy"
              v-if="columns[9].visible"
              :sortable="false"
            >
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.app_deploy"
                  :value="scope.row.deploy || '-'"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="开发厂商"
              min-width="130"
              prop="vendorName"
              v-if="columns[10].visible"
              :sortable="false"
            >
              <template slot-scope="scope">
                {{ scope.row.vendorName || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="服务对象"
              width="100"
              prop="serviceGroup"
              v-if="columns[11].visible"
              :sortable="false"
            >
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.serve_group"
                  :value="scope.row.serviceGroup || '-'"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="授权用户数"
              width="120"
              prop="userNums"
              v-if="columns[12].visible"
              :sortable="false"
            >
              <template slot-scope="scope">
                {{ scope.row.vnlnUpdateTime || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="月均活跃人数"
              width="130"
              prop="everydayActiveNums"
              v-if="columns[13].visible"
              :sortable="false"
            >
              <template slot-scope="scope">
                {{ scope.row.everydayActiveNums || 0 }}
              </template>
            </el-table-column>
            <el-table-column
              label="上线状态"
              width="100"
              prop="state"
              v-if="columns[14].visible"
              :sortable="false"
            >
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.asset_state"
                  :value="scope.row.state || '-'"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="是否开放网络"
              width="140"
              prop="isOpenNetwork"
              :sortable="false"
            >
              <template slot-scope="scope">
                {{ scope.row.isOpenNetwork == '1' ? '是' : scope.row.isOpenNetwork == '0'  ? '否' : "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="HW时期是否可关停"
              width="150"
              prop="hwIsTrueShutDown"
              :sortable="false"
            >
              <template slot-scope="scope">
                {{ scope.row.hwIsTrueShutDown == '1' ? '是' : scope.row.hwIsTrueShutDown == '0'  ? '否' : "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="审核状态"
              width="100"
              prop="checkOn"
              v-if="columns[15].visible && whetherOrNotToAudit"
              :sortable="false"
            >
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.appcheck_state"
                  :value="scope.row.checkOn || '-'"
                />
              </template>
            </el-table-column>
            <el-table-column label="严重漏洞" align="left" prop="criticalVulnCount" width="100">
              <template slot-scope="scope">
                {{ scope.row.criticalVulnCount || "-" }}
              </template>
            </el-table-column>
            <el-table-column label="高危漏洞" align="left" width="100" prop="highVulnCount">
              <template slot-scope="scope">
                {{ scope.row.highVulnCount || "-" }}
              </template>
            </el-table-column>
            <el-table-column label="中危漏洞" align="left" width="100" prop="mediumVulnCount">
              <template slot-scope="scope">
                {{ scope.row.mediumVulnCount || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="备注"
              min-width="140"
              align="left"
              prop="remark"
              v-if="columns[16].visible"
              :sortable="false"
            >
              <template slot-scope="scope">
                {{ scope.row.remark }}
<!--                <el-popover placement="right-end" width="200" trigger="hover">
                  <span>{{ scope.row.remark }}</span>
                  <span class="r_popover" slot="reference">
                  {{ scope.row.remark }}
                </span>
                </el-popover>-->
              </template>
            </el-table-column>
            <el-table-column
              label="标签"
              min-width="140"
              align="left"
              prop="tags"
              v-if="columns[17].visible"
              :sortable="false"
            >
              <template slot-scope="scope">
                <el-popover placement="right-end" width="200" trigger="hover">
                  <span>{{ scope.row.tags }}</span>
                  <span class="r_popover" slot="reference">
                  {{ scope.row.tags || "-" }}
                </span>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column :key="Math.random()" label="操作" align="left" fixed="right" width="200" :show-overflow-tooltip="false">
              <template slot="header">
                <ColumnFilter :optionColumns="optionColumns" :checkedColumns.sync="checkedColumns" :columns="columns" />
              </template>
              <template slot-scope="scope">
                <el-button size="mini" type="text"
                           @click="handleUpdate(scope.row,true,{showData:'false', isShowGap: true},true)"
                           v-if="!whetherOrNotToAudit || (scope.row.checkOn!='new' && checkPermi(['safe:application:list']))">
                  详情
                </el-button>
                <el-button size="mini" type="text" @click="handleApply(scope.row)"
                           v-if="whetherOrNotToAudit && scope.row.checkOn == 'new' && checkPermi(['safe:application:apply'])">
                  提交审核
                </el-button>
                <el-button size="mini" type="text"
                           @click="handleUpdate(scope.row,false,{showData:'false'})"
                           v-if="whetherOrNotToAudit && scope.row.checkOn=='wait'&& checkPermi(['safe:application:check'])">
                  审核
                </el-button>
<!--                <el-button size="mini" type="text" @click="handleUpdate(scope.row)"
                           v-if="scope.row.checkOn!='wait' && checkPermi(['safe:application:edit'])">编辑
                </el-button>
                <el-button size="mini" type="text" @click="handleDelete(scope.row)" class="table-delBtn"
                           v-if=" scope.row.checkOn!='wait' && checkPermi(['safe:application:remove'])">删除
                </el-button>-->
                <el-button size="mini" type="text" @click="handleUpdate(scope.row)"
                           v-if="checkPermi(['safe:application:edit'])">编辑
                </el-button>
                <el-button size="mini" type="text" @click="handleDelete(scope.row)" class="table-delBtn"
                           v-if="checkPermi(['safe:application:remove'])">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                    @pagination="getList"/>
      </div>
    </div>

    <el-dialog title="填写修改记录" :visible.sync="remarkDialog" width="600px" append-to-body>
      <el-form ref="remarkFrom" :model="remarkFrom" :rules="rules">
        <el-form-item prop="remarkMsg">
          <el-input type="textarea" :rows="8" minlength="3" maxlength="170"
                    v-model.trim="remarkFrom.remarkMsg"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="formMsgSubmit">提交</el-button>
        <el-button @click="callOffMsg">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :data="{'clear':upload.clear}"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" style="color: red" slot="tip">仅允许导入xls、xlsx格式文件。</div>
      </el-upload>
      <el-link
        type="primary"
        :underline="false"
        style="font-size:12px;vertical-align: baseline;"
        @click="importTemplate"
      >下载模板
      </el-link>
      <!--      <el-checkbox v-model="upload.clear" true-label="1" false-label="0">导入前清空</el-checkbox>-->
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

<!--    <application-dialog
      :title="title"
      :params.sync="params"
      @applicationChange="applicationChange"
      :applicationVisible.sync="applicationVisible"/>-->
<!--业务系统新增交互改版-->
    <application-details
      :title="title"
      :params.sync="params"
      @applicationChange="applicationChange"
      @deptSelectKeyChange="deptSelectKey++"
      :applicationVisible.sync="applicationVisible"/>
  </div>
</template>

<script>
import {
  delApplication,
  listApplication,
  applyApplication,
  getAppCountByDict,
  auditConfig
} from '@/api/safe/application'
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {checkPermi, checkRole} from "@/utils/permission";
import {mapGetters} from 'vuex'
import {getToken} from "@/utils/auth";

export default {
  name: "Application",
  dicts: [
    'protection_grade',
    'appcheck_state',
    'app_login_type',
    'app_deploy',
    'serve_group',
    'app_state',
    'construct_type',
    'system_type',
    'asset_state',
    'protection_grade',
    'appcheck_state'
  ],
  components: {
    ApplicationDetails: () => import('@/views/hhlCode/component/applicationDetails.vue'),
    ApplicationDialog: () => import('@/views/hhlCode/component/application/applicationInfo.vue'),
    importThreatenInfo: () => import('@/views/basis/securityWarn/importThreatenInfo'),
    DeptSelectSystem: () => import('@/views/safe/application/component/deptSelectSystem'),
    typeTree: () => import('@/views/components/typeTree'),
    vendorSelect: () => import('@/views/components/select/vendorSelect'),
    uploadFileTable: () => import('@/views/components/table/uploadFileTable'),
    SystemList: () => import('../../../components/SystemList')
  },
  data() {
    return {
      showAll: false,
      upload: {
        open: false, // 是否显示弹出层（用户导入）
        title: "", // 弹出层标题（用户导入）
        clear: "0",
        isUploading: false, // 是否禁用上传
        headers: {Authorization: "Bearer " + getToken()}, // 设置上传的请求头部
        url: process.env.VUE_APP_BASE_API + "/safe/application/importDataToJiangTong", // 上传的地址
      },
      whetherOrNotToAudit: false,
      checkPermi: checkPermi,
      checkRole: checkRole,
      content: "",
      classId: 7,
      className: '业务应用系统/平台',
      typelist: [],
      children: [],
      loading: true, // 遮罩层
      ids: [], // 选中数组
      currentNames: [],
      assetNames: [], // 选中表数组
      single: true, // 非单个禁用
      multiple: true, // 非多个禁用
      showSearch: true, // 显示搜索条件
      total: 0, // 总条数
      applicationList: [], // 业务应用系统表格数据
      title: "", // 弹出层标题
      open: false, // 是否显示弹出层
      params: {},
      deptSelectKey: 0,
      // 查询参数
      queryParams: {
        isAsc: 'desc',
        orderByColumn: 'createTime',
        pageNum: 1,
        pageSize: 10,
        assetCode: null,
        assetName: null,
        degreeImportance: null,
        domainId: null,
        applicationIds:[]
      },
      systemsType: [], // 系统类型
      assetState: [], // 上线状态
      protectionGrade: [], // 等保等级
      appcheckState: [], // 审核状态
      hwIsTrueShutDown:[{dictLabel:'是',dictValue:'1'},{dictLabel:'否',dictValue:'0'}],
      paramsArray: ['system_type', 'asset_state', 'protection_grade', 'appcheck_state'], // 搜索参数
      // 表单校验
      rules: {
        remarkMsg: [
          { min: 3, max: 170, message: '修改记录不能少于3个字符且不能大于170个字符', trigger: 'blur' },
          { required: true, message: '修改记录不能为空', trigger: 'blur'}
        ]
      },
      columns: [
        {key: 0, label: "系统名称", visible: true},
        {key: 1, label: "登录地址", visible: true},
        {key: 2, label: "所属部门", visible: true},
        {key: 3, label: "责任人员", visible: true},
        {key: 4, label: "联系电话", visible: false},
        {key: 5, label: "等保级别", visible: true},
        {key: 6, label: "关键设施", visible: false},
        {key: 7, label: "技术架构", visible: false},
        {key: 8, label: "登录方式", visible: false},
        {key: 9, label: "部署方式", visible: false},
        {key: 10, label: "供应商", visible: false},
        {key: 11, label: "服务对象", visible: false},
        {key: 12, label: "授权用户数", visible: false},
        {key: 13, label: "月均活跃人数", visible: false},
        {key: 14, label: "上线状态", visible: true},
        {key: 15, label: "审核状态", visible: true},
        {key: 16, label: "备注", visible: false},
        {key: 17, label: "标签", visible: false},
        {key: 18, label: "严重漏洞", visible: true},
        {key: 19, label: "高危漏洞", visible: true},
        {key: 20, label: "中危漏洞", visible: true},
        {key: 21, label: "IP地址", visible: true},
      ],
      editItem: "edit",
      editable: true,
      step: 0,
      currentComponent: 'AddApp',
      auditApp: null,
      remarkFrom: {
        remarkMsg: '',
      },
      remarkDialog: false,
      defaultShow: true,
      applicationVersion: false,
      applicationVisible: false,
      optionColumns: ["系统名称", "登录地址", "IP地址",  "所属部门", "责任人员", "联系电话", "等保级别", "关键设施", "技术架构", "登录方式", "部署方式", "供应商", "服务对象", "授权用户数", "月均活跃人数", "上线状态", "审核状态", "备注", "标签", "严重漏洞","高危漏洞","中危漏洞"],
      checkedColumns: ["系统名称", "所属部门", "责任人员", "登录地址",  "IP地址", "等保级别", "上线状态", "审核状态", "严重漏洞","高危漏洞","中危漏洞"],
      tableKey: 1,
    }
  },
  computed: {
    ...mapGetters(["activeNames"]),
  },
  created() {
    auditConfig({
      pageNum: 1,
      pageSize: 10
    }).then(response => {
      this.whetherOrNotToAudit = response.rows[0].configValue !== 'false';
    })
    this.initSearchDataAndListData();
    // this.handleQuery();
  },
  watch: {
    '$router.name'(val) {
      if (val == 'Application') {
        this.init();
      } else {
        this.open = false;
      }
    },
    '$route.query': {
      handler(val) {
        if(val && val.deptId){
          this.queryParams.deptId = parseInt(val.deptId);
        }
        if (val && val.domainId){
          this.queryParams.domainId = val.domainId;
        }
        if (val && val.applicationIds){
          this.queryParams.applicationIds = val.applicationIds;
        }
        if (val && val.systemType){
          this.queryParams.systemType = val.systemType;
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 导入功能
    handleImport() {
      this.upload.title = "业务系统导入";
      this.upload.open = true;
    },
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      if (response.code == 200)
        this.$alert("成功导入", "导入结果", {dangerouslyUseHTMLString: true});
      else
        this.$alert(response.msg, "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
      this.deptSelectKey += 1;
    },
    submitFileForm() {
      this.$refs.upload.submit();
    },
    importTemplate() {
      this.download('safe/application/importTemplateToJiangTong', {}, `application_${new Date().getTime()}.xlsx`)
    },

    // 初始化
    async initSearchDataAndListData() {
      await this.initSearchData();
      this.init();
    },

    // 初始化查询条件
    async initSearchData() {
      for (let params of this.paramsArray) {
        try {
          const response = await getAppCountByDict(params);
          if (params === 'system_type') this.systemsType = response.data.countByDictList || [];
          if (params === 'asset_state') this.assetState = response.data.countByDictList || [];
          if (params === 'protection_grade') this.protectionGrade = response.data.countByDictList || [];
          if (params === 'appcheck_state') this.appcheckState = response.data.countByDictList || [];
          console.log(this.appcheckState)
        } catch (error) {
          console.error('请求失败:', error);
        }
      }
    },

    init() {
      this.queryParams = Object.assign(this.queryParams, this.$route.params);
      if (this.$route.params.add) {
        this.$nextTick(() => {
          this.handleAdd();
          this.form.locationId = this.$route.params.locationId;
          this.open = true;
        })
      }
    },

    //选中部门事件
    deptSelect(node) {
      this.queryParams.assetCode = null;
      // this.queryParams.assetName = null;
      if (node.id != null) {
        this.queryParams.deptId = node.id;
      }
      this.handleQuery();
    },

    //排序
    sortChange(column, prop, order) {
      if (column.order != null) {
        this.queryParams.isAsc = 'desc';
      } else {
        this.queryParams.isAsc = 'asc';
      }
      if (column.prop == 'state') {
        this.queryParams.orderByColumn = "e.state";
      } else
        this.queryParams.orderByColumn = column.prop;
      this.getList(this.queryParams);
    },
    /** 查询业务应用系统列表 */
    getList() {

      this.loading = true;
      if(this.$route.params){
        this.queryParams.ids = this.$route.params.ids;
      }
      listApplication(this.queryParams).then(response => {
        this.applicationList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).finally(() => {
        this.$nextTick(() => {
          this.tableKey++;
        })
      })
    },

    // 表单重置
    reset() {
      this.editItem = "edit";
      this.editable = true;
      this.step = 0;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.systemType = null;
      this.queryParams.state = null;
      this.queryParams.protectGrade = null;
      this.queryParams.isOpenNetwork = null;
      this.queryParams.hwIsTrueShutDown = null;
      this.queryParams.checkOn = null;
      this.queryParams.assetCode = null;
      this.queryParams.assetName = null;
      this.queryParams.url = null;
      this.queryParams.domainUrl = null;
      this.queryParams.domainId = null;
      this.queryParams.applicationIds = [];
      this.$refs.systemList1 && this.$refs.systemList1.resetSelection();
      this.$refs.systemList2 && this.$refs.systemList2.resetSelection();
      this.$refs.systemList3 && this.$refs.systemList3.resetSelection();
      this.$refs.systemList4 && this.$refs.systemList4.resetSelection();
      this.clearRouteQueryParams();
      this.handleQuery();
    },
    clearRouteQueryParams(){
      if(this.$route.params){
        let queryParams = this.$route.params;
        delete queryParams.ids;
        this.$router.push({params: queryParams})
      }
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.assetId);
      this.assetNames = selection.map(item => item.assetName);
      this.currentNames = selection.map(item => item.assetName);
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    //提交审核
    handleApply(app) {
      this.auditApp = app
      if (app.checkBy) {
        this.remarkDialog = true
      } else {
        this.submitMsg()
      }
    },
    formMsgSubmit() {
      this.$refs['remarkFrom'].validate((valid) => {
        if (valid) {
          this.submitMsg()
        }
      });
    },
    submitMsg() {
      applyApplication({
        assetId: this.auditApp.assetId,
        remark: this.remarkFrom.remarkMsg
      }).then(res => {
        this.$modal.msgSuccess("已经提交审核！");
        this.getList();
        this.callOffMsg()
      })
    },
    callOffMsg() {
      this.auditApp = null
      this.remarkFrom.remarkMsg = ''
      this.remarkDialog = false
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.title = '添加应用信息';
      this.params = {};
      this.applicationVisible = true;
      // this.$tab.openPage(
      //   "添加应用信息",
      //   "/asset-ledger/monitor2/application/info",
      //   {}
      // );
    },
    /** 修改按钮操作 */
    handleUpdate(row, edit = true, data) {
      this.reset();
      if (row.checkOn === "pass" && (data === undefined || data === null)) {
        this.title = '修改应用信息'
        this.editable = edit
        const assetId = row.assetId || this.ids
        this.params = {assetId, ...data};
        this.params.isEdit = true;
        this.applicationVisible = true;
        // this.$tab.openPage(title, '/asset-ledger/monitor2/application/info', params);
      } else {
        this.title = data ? data.showData === 'false' ? '查看应用信息' : '修改应用信息' : '修改应用信息';
        this.editable = edit;
        const assetId = row.assetId || this.ids;
        this.params = {assetId, ...data};
        this.applicationVisible = true;
        // this.$tab.openPage(title, '/asset-ledger/monitor2/application/info', {assetId, ...data});
      }
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const assetIds = row.assetId || this.ids;
      let assetsName = "";
      if (!row.assetId) {
        assetsName = this.currentNames.join(",");
      } else {
        assetsName = row.assetName;
      }

      this.$modal.confirm('是否确认删除【' + assetsName + '】的数据项？').then(function () {
        return delApplication(assetIds);
      }).then(() => {
        this.getList();
        this.deptSelectKey += 1;
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('safe/application/export', {
        ...this.queryParams
      }, `application_${new Date().getTime()}.xlsx`)
    },
    applicationChange(data){
      this.applicationVisible = data;
      this.getList();
      this.initSearchData();
    },
  }
};
</script>

<style scoped lang="scss">
@import "../../../assets/styles/assetIndex.scss";
.small-padding {
  padding-left: 0;
  padding-right: 0;
  width: 150px;
}

.operate {
  display: flex;
  justify-content: flex-end;
  margin-top: 30px;
}

.option-app {
  margin-right: 10px;
}

.r_popover {
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 150px;
  overflow: hidden;
}

</style>
