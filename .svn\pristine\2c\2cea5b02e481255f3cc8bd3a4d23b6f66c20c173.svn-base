<template>
  <ul class="system-list">
    <li v-for="dict in systemTypes" :key="dict.value" :class="{ 'selected': dict.dictValue === selectedSystemType }">
      <span @click="selectSystemType(dict.dictValue)">{{ `${dict.dictLabel}${dict.count != null ? '('+dict.count+')' : ''}` }}</span>
    </li>
  </ul>
</template>

<script>
export default {
  data() {
    return {
      selectedSystemType: null // 添加状态变量
    }
  },
  props: {
    systemTypes: {
      type: Array,
      required: true
    },
    systemTypeVal: {
      type: [String, Number],
      default: null
    },
  },
  methods: {
    selectSystemType(val) {
      if (this.selectedSystemType === val) {
        this.selectedSystemType = null; // 取消选中
        this.$emit('update:systemTypeVal', null);
      } else {
        this.selectedSystemType = val; // 选中
        this.$emit('update:systemTypeVal', val);
      }
      this.$emit('filterSelect');
    },
    resetSelection() {
      this.selectedSystemType = null; // 重置选择状态
      // this.$emit('update:systemTypeVal', null); // 通知父组件选择已重置
    }
  }
}
</script>
<style lang="scss" scoped>
.system-list {
  display: flex;
  flex-flow: wrap;
}

.system-list li {
  margin-right: 15px;
  font-size: 14px;
  color: #4382FD;
}

.system-list li.selected {
  padding: 0 5px;
  color: #FFFFFF; // 设置选中后的颜色
  background: #4382FD;
}

.system-list span {
  cursor: pointer;
}
</style>
