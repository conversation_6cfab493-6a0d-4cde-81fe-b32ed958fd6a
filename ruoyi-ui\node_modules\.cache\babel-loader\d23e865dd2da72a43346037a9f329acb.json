{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\components\\SystemList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\components\\SystemList\\index.vue", "mtime": 1756459866243}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5udW1iZXIuY29uc3RydWN0b3IuanMiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzZWxlY3RlZFN5c3RlbVR5cGU6IG51bGwgLy8g5re75Yqg54q25oCB5Y+Y6YePCiAgICB9OwogIH0sCiAgcHJvcHM6IHsKICAgIHN5c3RlbVR5cGVzOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfSwKICAgIHN5c3RlbVR5cGVWYWw6IHsKICAgICAgdHlwZTogW1N0cmluZywgTnVtYmVyXSwKICAgICAgZGVmYXVsdDogbnVsbAogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgc2VsZWN0U3lzdGVtVHlwZTogZnVuY3Rpb24gc2VsZWN0U3lzdGVtVHlwZSh2YWwpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRTeXN0ZW1UeXBlID09PSB2YWwpIHsKICAgICAgICB0aGlzLnNlbGVjdGVkU3lzdGVtVHlwZSA9IG51bGw7IC8vIOWPlua2iOmAieS4rQogICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTpzeXN0ZW1UeXBlVmFsJywgbnVsbCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZFN5c3RlbVR5cGUgPSB2YWw7IC8vIOmAieS4rQogICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTpzeXN0ZW1UeXBlVmFsJywgdmFsKTsKICAgICAgfQogICAgICB0aGlzLiRlbWl0KCdmaWx0ZXJTZWxlY3QnKTsKICAgIH0sCiAgICByZXNldFNlbGVjdGlvbjogZnVuY3Rpb24gcmVzZXRTZWxlY3Rpb24oKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRTeXN0ZW1UeXBlID0gbnVsbDsgLy8g6YeN572u6YCJ5oup54q25oCBCiAgICAgIC8vIHRoaXMuJGVtaXQoJ3VwZGF0ZTpzeXN0ZW1UeXBlVmFsJywgbnVsbCk7IC8vIOmAmuefpeeItue7hOS7tumAieaLqeW3sumHjee9rgogICAgfQogIH0KfTs="}, {"version": 3, "names": ["data", "selectedSystemType", "props", "systemTypes", "type", "Array", "required", "systemTypeVal", "String", "Number", "default", "methods", "selectSystemType", "val", "$emit", "resetSelection"], "sources": ["src/components/SystemList/index.vue"], "sourcesContent": ["<template>\n  <ul class=\"system-list\">\n    <li v-for=\"dict in systemTypes\" :key=\"dict.value\" :class=\"{ 'selected': dict.dictValue === selectedSystemType }\">\n      <span @click=\"selectSystemType(dict.dictValue)\">{{ `${dict.dictLabel}${dict.count != null ? '('+dict.count+')' : ''}` }}</span>\n    </li>\n  </ul>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      selectedSystemType: null // 添加状态变量\n    }\n  },\n  props: {\n    systemTypes: {\n      type: Array,\n      required: true\n    },\n    systemTypeVal: {\n      type: [String, Number],\n      default: null\n    },\n  },\n  methods: {\n    selectSystemType(val) {\n      if (this.selectedSystemType === val) {\n        this.selectedSystemType = null; // 取消选中\n        this.$emit('update:systemTypeVal', null);\n      } else {\n        this.selectedSystemType = val; // 选中\n        this.$emit('update:systemTypeVal', val);\n      }\n      this.$emit('filterSelect');\n    },\n    resetSelection() {\n      this.selectedSystemType = null; // 重置选择状态\n      // this.$emit('update:systemTypeVal', null); // 通知父组件选择已重置\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n.system-list {\n  display: flex;\n  flex-flow: wrap;\n}\n\n.system-list li {\n  margin-right: 15px;\n  font-size: 14px;\n  color: #4382FD;\n}\n\n.system-list li.selected {\n  padding: 0 5px;\n  color: #FFFFFF; // 设置选中后的颜色\n  background: #4382FD;\n}\n\n.system-list span {\n  cursor: pointer;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;iCASA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,kBAAA;IACA;EACA;EACAC,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,QAAA;IACA;IACAC,aAAA;MACAH,IAAA,GAAAI,MAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,OAAA;IACAC,gBAAA,WAAAA,iBAAAC,GAAA;MACA,SAAAZ,kBAAA,KAAAY,GAAA;QACA,KAAAZ,kBAAA;QACA,KAAAa,KAAA;MACA;QACA,KAAAb,kBAAA,GAAAY,GAAA;QACA,KAAAC,KAAA,yBAAAD,GAAA;MACA;MACA,KAAAC,KAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MACA,KAAAd,kBAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}